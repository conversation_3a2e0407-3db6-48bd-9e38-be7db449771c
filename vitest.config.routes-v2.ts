import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';

export default defineConfig({
  plugins: [react(), tsconfigPaths()],
  test: {
    name: 'routes-v2-migration',
    environment: 'jsdom',
    setupFiles: ['./test-setup/routes-v2-setup.ts'],
    globals: true,
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'test-setup/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**',
        '**/dist/**',
        '**/build/**',
      ],
      include: [
        'app/lib/google-routes-v2-service.ts',
        'app/lib/routes-v2-transformer.ts',
        'app/lib/route-planning-utils.ts',
        'app/hooks/useRouteCalculation.ts',
        'app/components/route-planner/Enhanced*.tsx',
        'app/components/route-planner/Route*.tsx',
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
    testTimeout: 30000,
    hookTimeout: 10000,
    teardownTimeout: 5000,
    isolate: true,
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true,
      },
    },
    env: {
      NODE_ENV: 'test',
      VITE_GOOGLE_MAPS_API_KEY: 'test-api-key',
    },
    include: [
      'app/lib/__tests__/**/*.test.{ts,tsx}',
      'app/components/route-planner/__tests__/**/*.test.{ts,tsx}',
      'app/__tests__/e2e/**/*.test.{ts,tsx}',
    ],
    exclude: [
      'node_modules/',
      'dist/',
      'build/',
      '**/*.d.ts',
    ],
    reporters: ['verbose', 'json'],
    outputFile: {
      json: './test-reports/routes-v2-results.json',
    },
  },
  define: {
    'import.meta.vitest': 'undefined',
  },
  resolve: {
    alias: {
      '~': new URL('./app', import.meta.url).pathname,
    },
  },
});
