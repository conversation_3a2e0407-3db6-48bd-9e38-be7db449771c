# Unified Map Explorer Plan

## Goal

Refactor `venues.tsx`, `wanderlust.tsx`, and `route-planner.tsx` into a single, unified React page, leveraging the component architecture defined in `REFACTORING_STRATEGY.md`.

## Detailed Steps

1.  **Create a new route file:** Create a new file, `app/routes/map-explorer.tsx`, to house the unified map explorer page.
2.  **Implement the `MapExplorer` component:** Implement the `MapExplorer` component in `app/routes/map-explorer.tsx`, using the shared hooks and components as outlined in the previous plan.
3.  **Implement mode switching:** Implement a mechanism for switching between the different modes (venue discovery, travel history, and route planning) within the `MapExplorer` component. This could be a tab bar, a dropdown menu, or some other UI element.
4.  **Audit and remove redundant components:** Conduct a thorough audit of the original `venues.tsx`, `wanderlust.tsx`, and `route-planner.tsx` files to identify and remove any redundant or obsolete components that are no longer essential for the unified page's core functionality.
5.  **Optimize for performance:** Optimize the resulting page for performance, considering rendering time, memory usage, and overall responsiveness.
6.  **Ensure accessibility:** Ensure that the unified page adheres to accessibility best practices.
7.  **Implement comprehensive testing:** Implement comprehensive unit and integration tests to validate the refactored functionality.
8.  **Update navigation:** Update the application's navigation to point to the new `app/routes/map-explorer.tsx` file.

## Code Examples

1.  **`app/routes/map-explorer.tsx`:**
    ```typescript
    // app/routes/map-explorer.tsx
    import React from 'react';
    import { useMapExplorer } from '~/hooks/useMapExplorer';
    import { MapExplorerComponent } from '~/components/shared/MapExplorer'; // Assuming you renamed UnifiedMapInterface to MapExplorerComponent

    export default function MapExplorerRoute() {
      const { mode, setMode } = useMapExplorer();

      return (
        <div>
          {/* Mode switching UI */}
          <button onClick={() => setMode('venue-discovery')}>Venues</button>
          <button onClick={() => setMode('travel-history')}>Travel History</button>
          <button onClick={() => setMode('route-planning')}>Route Planner</button>

          {/* Map Explorer Component */}
          <MapExplorerComponent mode={mode} />
        </div>
      );
    }

    export function meta() {
      return [{ title: 'Map Explorer' }];
    }
    ```

2.  **`app/hooks/useMapExplorer.ts`:**
    ```typescript
    // app/hooks/useMapExplorer.ts
    import { useState } from 'react';

    export function useMapExplorer() {
      const [mode, setMode] = useState<'venue-discovery' | 'route-planning' | 'travel-history'>('venue-discovery');
      // ... other state and functions ...

      return {
        mode,
        setMode,
        // ... other state and functions ...
      };
    }
    ```

3.  **`app/hooks/useUnifiedPlaceManagement.ts`:**
    ```typescript
    // app/hooks/useUnifiedPlaceManagement.ts
    import { useState } from 'react';
    import type { VisitedPlace } from '~/types/wanderlust';

    export function useUnifiedPlaceManagement() {
      const [places, setPlaces] = useState<VisitedPlace[]>([]);

      const addPlace = (place: VisitedPlace) => {
        setPlaces([...places, place]);
      };

      const removePlace = (placeId: string) => {
        setPlaces(places.filter(p => p.id !== placeId));
      };

      return {
        places,
        addPlace,
        removePlace,
      };
    }
    ```

## Mermaid Diagram

```mermaid
graph LR
    subgraph Hooks
        A[useMapExplorer]
        B[useSearch]
        C[usePlaceManagement]
    end

    subgraph MapExplorerComponent
        D[Mode Switching] --> E(Data Loading & Management)
        E --> F(Map Interactions)
        F --> G(Search Functionality)
        G --> H(Place Management)
    end

    subgraph MapExplorerRoute
        I[Import MapExplorerComponent] --> J(Render MapExplorerComponent with mode)
    end

    subgraph Common Steps
        O[Testing]
    end

    A --> D
    B --> G
    C --> H
    H --> J
    J --> O