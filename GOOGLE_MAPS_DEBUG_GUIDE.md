# Google Maps Debug Guide - Route Planner

## Issue: DemoMap Fallback Appearing Instead of Google Maps

After the recent refactoring to simplified architecture, the Route Planner is showing the DemoMap fallback instead of the actual Google Maps. Here's a systematic approach to diagnose and fix this issue.

## Step 1: Environment Variable Check

### 1.1 Verify API Key Configuration

```bash
# Check if environment variable is set
echo $VITE_GOOGLE_MAPS_API_KEY

# Or check in your .env file
cat .env | grep VITE_GOOGLE_MAPS_API_KEY
```

### 1.2 Verify API Key Format
- Should start with `AIza`
- Should be 39 characters long
- Should not have any extra spaces or quotes

### 1.3 Check Environment Loading
Add this to your route-planner page temporarily:

```tsx
// Add to route-planner page for debugging
console.log('API Key:', import.meta.env.VITE_GOOGLE_MAPS_API_KEY);
console.log('Environment:', import.meta.env.MODE);
```

## Step 2: Browser Console Debugging

### 2.1 Run Debug Script
Copy and paste the contents of `debug-google-maps.js` into your browser console while on the route-planner page.

### 2.2 Check Network Tab
1. Open Developer Tools → Network tab
2. Reload the page
3. Look for requests to `googleapis.com`
4. Check if any requests are failing (red status)

### 2.3 Check Console Errors
Look for errors containing:
- "Google Maps"
- "Loader"
- "API key"
- "CORS"
- "Network"

## Step 3: API Key Validation

### 3.1 Google Cloud Console Check
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services → Credentials
3. Find your API key and verify:
   - Maps JavaScript API is enabled
   - Places API is enabled
   - Routes API is enabled (for Routes v2)

### 3.2 API Restrictions Check
1. Check if API key has domain restrictions
2. Verify your domain (localhost:3000, your-domain.com) is allowed
3. Check if there are IP restrictions

### 3.3 Test API Key Manually
```bash
# Test API key directly
curl "https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=places,geometry,routes"
```

## Step 4: Component Integration Check

### 4.1 Add Debug Component
Temporarily add the GoogleMapsDebugger component to your route-planner:

```tsx
// In app/routes/route-planner.tsx
import { GoogleMapsDebugger } from '~/components/debug/GoogleMapsDebugger';

// Add inside the component
{process.env.NODE_ENV === 'development' && <GoogleMapsDebugger />}
```

### 4.2 Check Error Boundary
The issue might be that the GoogleMapsErrorBoundary is catching an error and showing DemoMap. Check:

```tsx
// Temporarily disable error boundary to see actual errors
<GoogleMap
  features={['markers', 'routes']}
  onMapLoad={handleMapLoad}
  onPlaceSelect={handlePlaceSelect}
  className="h-full w-full"
/>
// Remove GoogleMapsErrorBoundary wrapper temporarily
```

## Step 5: Dynamic Import Issue Check

### 5.1 Test Dynamic Import
Add this to your component to test the dynamic import:

```tsx
useEffect(() => {
  const testImport = async () => {
    try {
      const module = await import('@googlemaps/js-api-loader');
      console.log('Dynamic import success:', !!module.Loader);
    } catch (error) {
      console.error('Dynamic import failed:', error);
    }
  };
  testImport();
}, []);
```

### 5.2 Alternative Import Method
If dynamic import fails, try this alternative in GoogleMapsLoader.tsx:

```tsx
// Alternative import method
import GoogleMapsLoader from '@googlemaps/js-api-loader';

// Then use:
const loader = new GoogleMapsLoader({
  apiKey,
  version: 'weekly',
  libraries,
  language: 'en',
});
```

## Step 6: Common Issues and Solutions

### 6.1 CORS Issues
If you see CORS errors:
- Check API key domain restrictions
- Verify you're not running on a blocked domain
- Try temporarily removing domain restrictions

### 6.2 Network Blocking
- Check if ad blockers are interfering
- Try disabling browser extensions
- Test in incognito mode

### 6.3 Build vs Development
- Test in both development and production builds
- Check if environment variables are loaded in production

### 6.4 React Strict Mode
If using React Strict Mode, it might cause double loading:
```tsx
// Temporarily disable strict mode
// <React.StrictMode>
  <App />
// </React.StrictMode>
```

## Step 7: Temporary Fixes

### 7.1 Force Google Maps Loading
Add this to GoogleMapsLoader.tsx for debugging:

```tsx
// Add more detailed logging
console.log('🔍 GoogleMapsLoader Debug:', {
  apiKey: !!apiKey,
  apiKeyLength: apiKey?.length,
  windowGoogle: !!(window as any).google,
  windowGoogleMaps: !!(window as any).google?.maps,
});
```

### 7.2 Bypass Error Boundary
Temporarily remove the error boundary to see the actual error:

```tsx
// In RouteMap.tsx, replace:
<GoogleMapsErrorBoundary fallback={<DemoMap />}>
  <GoogleMap />
</GoogleMapsErrorBoundary>

// With:
<GoogleMap />
```

## Step 8: Verification Steps

### 8.1 Success Indicators
You should see:
- ✅ API key present and valid
- ✅ Dynamic import successful
- ✅ Network requests to googleapis.com successful
- ✅ No console errors
- ✅ `window.google.maps` available

### 8.2 If Still Failing
1. Check the exact error message in console
2. Verify API billing is enabled in Google Cloud
3. Check API quotas and limits
4. Try creating a new API key
5. Test with a minimal Google Maps example

## Step 9: Contact Information

If the issue persists after following these steps, provide:
1. Console error messages
2. Network tab screenshots
3. API key configuration (without revealing the key)
4. Browser and environment details
5. Results from the debug script

This systematic approach should help identify and resolve the Google Maps loading issue in your Route Planner application.
