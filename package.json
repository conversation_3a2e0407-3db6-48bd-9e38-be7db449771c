{"name": "world-cup", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "test": "vitest run", "test:watch": "vitest", "typecheck": "react-router typegen && tsc", "test:routes-v2": "tsx scripts/test-routes-v2.ts", "test:routes-v2:unit": "vitest run app/lib/__tests__/routes-v2-migration.test.ts", "test:routes-v2:components": "vitest run app/components/route-planner/__tests__/enhanced-components.test.tsx", "test:routes-v2:performance": "vitest run app/lib/__tests__/routes-v2-performance.test.ts", "test:routes-v2:e2e": "vitest run app/__tests__/e2e/routes-v2-integration.test.tsx", "test:routes-v2:report": "tsx scripts/test-routes-v2.ts && open test-reports/", "test:routes-v2:all-envs": "npm run test:routes-v2 routes-v2-enabled && npm run test:routes-v2 routes-v2-disabled && npm run test:routes-v2 routes-v2-partial", "deploy:routes-v2": "tsx scripts/deploy-routes-v2.ts", "deploy:feature-flag": "tsx scripts/deploy-routes-v2.ts --strategy=feature-flag", "deploy:canary": "tsx scripts/deploy-routes-v2.ts --strategy=canary", "deploy:blue-green": "tsx scripts/deploy-routes-v2.ts --strategy=blue-green", "rollback:routes-v2": "tsx scripts/rollback-routes-v2.ts", "health-check:routes-v2": "tsx scripts/health-check-routes-v2.ts", "monitor:routes-v2": "tsx scripts/monitor-routes-v2.ts", "feature-flags:status": "tsx scripts/feature-flags-cli.ts status", "feature-flags:enable": "tsx scripts/feature-flags-cli.ts enable", "feature-flags:disable": "tsx scripts/feature-flags-cli.ts disable", "knip": "knip"}, "dependencies": {"@emotion/is-prop-valid": "^1.3.1", "@googlemaps/js-api-loader": "^1.16.8", "@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.6", "@react-google-maps/api": "^2.20.6", "@react-router/node": "^7.5.3", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.76.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "framer-motion": "^12.15.0", "i18next": "^25.1.3", "i18next-browser-languagedetector": "^8.1.0", "isbot": "^5.1.27", "lucide-react": "^0.511.0", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.1", "react-router": "^7.5.3", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.3.0", "uuid": "^11.1.0", "zod": "^3.25.20", "zustand": "^5.0.4"}, "devDependencies": {"@react-router/dev": "^7.5.3", "@tailwindcss/vite": "^4.1.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/google.maps": "^3.58.1", "@types/jest-axe": "^3.5.9", "@types/node": "^20.17.52", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vercel/react-router": "^1.1.0", "@vitejs/plugin-react": "^4.4.1", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "knip": "^5.59.0", "tsx": "^4.19.2", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.3"}, "version": "1.0.0", "description": "A modern, production-ready template for building full-stack React applications using React Router.", "directories": {"doc": "doc"}, "repository": {"type": "git", "url": "git+https://github.com/Monem-N/World-cup.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Monem-N/World-cup/issues"}, "homepage": "https://github.com/Monem-N/World-cup#readme"}