#!/usr/bin/env tsx

/**
 * Routes V2 Deployment Script
 * 
 * Automated deployment script for Routes API v2 with multiple deployment strategies,
 * health checks, and rollback capabilities.
 */

import { execSync } from 'child_process';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { getDeploymentManager, type DeploymentConfig } from '../app/lib/production/routes-v2-deployment';
import { getProductionConfig } from '../app/lib/production/routes-v2-config';
import { getRoutesV2Monitor } from '../app/lib/production/routes-v2-monitoring';

interface DeploymentOptions {
  strategy: 'feature_flag' | 'canary' | 'rolling' | 'blue_green';
  environment: 'staging' | 'production';
  percentage?: number;
  traffic?: number;
  dryRun?: boolean;
  skipTests?: boolean;
  skipHealthChecks?: boolean;
  autoRollback?: boolean;
  version?: string;
}

class RoutesV2Deployer {
  private config = getProductionConfig();
  private monitor = getRoutesV2Monitor();
  private deployment = getDeploymentManager();
  private options: DeploymentOptions;

  constructor(options: DeploymentOptions) {
    this.options = {
      percentage: 1,
      traffic: 5,
      dryRun: false,
      skipTests: false,
      skipHealthChecks: false,
      autoRollback: true,
      version: this.config.getConfig().version,
      ...options,
    };
  }

  async deploy(): Promise<void> {
    console.log('🚀 Starting Routes V2 Deployment');
    console.log('================================');
    console.log(`Strategy: ${this.options.strategy}`);
    console.log(`Environment: ${this.options.environment}`);
    console.log(`Version: ${this.options.version}`);
    console.log(`Dry Run: ${this.options.dryRun}`);
    console.log('');

    try {
      // Pre-deployment validation
      await this.preDeploymentChecks();

      // Create deployment configuration
      const deploymentConfig = this.createDeploymentConfig();

      // Execute deployment
      if (!this.options.dryRun) {
        const deploymentStatus = await this.deployment.startDeployment(deploymentConfig);
        await this.monitorDeployment(deploymentStatus.id);
      } else {
        console.log('🔍 Dry run mode - deployment configuration validated');
        console.log(JSON.stringify(deploymentConfig, null, 2));
      }

      console.log('✅ Deployment completed successfully');
    } catch (error) {
      console.error('❌ Deployment failed:', error);
      process.exit(1);
    }
  }

  private async preDeploymentChecks(): Promise<void> {
    console.log('🔍 Running pre-deployment checks...');

    // Check environment
    if (this.options.environment === 'production' && !this.config.isProductionEnvironment()) {
      throw new Error('Cannot deploy to production from non-production environment');
    }

    // Run tests unless skipped
    if (!this.options.skipTests) {
      console.log('🧪 Running test suite...');
      try {
        execSync('npm run test:routes-v2', { stdio: 'inherit' });
        console.log('✅ All tests passed');
      } catch (error) {
        throw new Error('Tests failed - deployment aborted');
      }
    }

    // Check system health unless skipped
    if (!this.options.skipHealthChecks) {
      console.log('🏥 Checking system health...');
      const healthStatus = this.monitor.getHealthStatus();
      const unhealthyServices = Object.entries(healthStatus)
        .filter(([_, status]) => status.status === 'unhealthy')
        .map(([service]) => service);

      if (unhealthyServices.length > 0) {
        throw new Error(`Unhealthy services detected: ${unhealthyServices.join(', ')}`);
      }
      console.log('✅ System health check passed');
    }

    // Validate API connectivity
    console.log('🔗 Validating API connectivity...');
    await this.validateApiConnectivity();
    console.log('✅ API connectivity validated');

    console.log('✅ Pre-deployment checks completed');
  }

  private async validateApiConnectivity(): Promise<void> {
    try {
      const response = await fetch('https://routes.googleapis.com/directions/v2:computeRoutes', {
        method: 'OPTIONS',
        headers: {
          'X-Goog-Api-Key': this.config.getConfig().apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`API validation failed: ${response.status}`);
      }
    } catch (error) {
      throw new Error(`Routes V2 API is not accessible: ${error}`);
    }
  }

  private createDeploymentConfig(): DeploymentConfig {
    const deploymentId = `routes-v2-${this.options.strategy}-${Date.now()}`;

    return {
      id: deploymentId,
      version: this.options.version!,
      strategy: this.options.strategy,
      environment: this.options.environment,
      rolloutPercentage: this.options.percentage!,
      healthCheckConfig: {
        enabled: !this.options.skipHealthChecks,
        interval: 30000, // 30 seconds
        timeout: 5000,   // 5 seconds
        retries: 3,
        successThreshold: 0.8,
        failureThreshold: 0.5,
        checks: [
          {
            name: 'routes_v2_api',
            type: 'api',
            config: {
              url: 'https://routes.googleapis.com/directions/v2:computeRoutes',
              method: 'OPTIONS',
              timeout: 5000,
              maxResponseTime: 3000,
            },
            weight: 1,
          },
          {
            name: 'error_rate',
            type: 'metric',
            config: {
              metric: 'errorCount',
              operator: 'less_than',
              threshold: 10,
            },
            weight: 1,
          },
          {
            name: 'response_time',
            type: 'metric',
            config: {
              metric: 'performanceCount',
              operator: 'less_than',
              threshold: 5000,
            },
            weight: 1,
          },
        ],
      },
      rollbackConfig: {
        enabled: true,
        autoRollback: this.options.autoRollback!,
        triggers: [
          {
            type: 'error_rate',
            threshold: 0.05, // 5% error rate
            duration: 300000, // 5 minutes
          },
          {
            type: 'response_time',
            threshold: 10000, // 10 seconds
            duration: 180000, // 3 minutes
          },
          {
            type: 'health_check',
            threshold: 0.5, // 50% health score
            duration: 120000, // 2 minutes
          },
        ],
        strategy: 'immediate',
        preserveData: true,
      },
      metadata: {
        deployedBy: process.env.USER || 'unknown',
        deployedAt: new Date().toISOString(),
        strategy: this.options.strategy,
        environment: this.options.environment,
        version: this.options.version,
        dryRun: this.options.dryRun,
      },
    };
  }

  private async monitorDeployment(deploymentId: string): Promise<void> {
    console.log(`📊 Monitoring deployment: ${deploymentId}`);
    
    const startTime = Date.now();
    const maxWaitTime = 30 * 60 * 1000; // 30 minutes

    while (Date.now() - startTime < maxWaitTime) {
      const status = this.deployment.getCurrentDeployment();
      
      if (!status || status.id !== deploymentId) {
        throw new Error('Deployment status not found');
      }

      console.log(`Status: ${status.status}, Progress: ${status.progress}%`);

      if (status.status === 'healthy') {
        console.log('✅ Deployment completed successfully');
        break;
      } else if (status.status === 'failed') {
        throw new Error(`Deployment failed: ${status.errors.join(', ')}`);
      } else if (status.status === 'rolled_back') {
        throw new Error('Deployment was rolled back due to issues');
      }

      // Wait before next check
      await new Promise(resolve => setTimeout(resolve, 10000)); // 10 seconds
    }

    // Generate deployment report
    this.generateDeploymentReport(deploymentId);
  }

  private generateDeploymentReport(deploymentId: string): void {
    const status = this.deployment.getCurrentDeployment();
    if (!status) return;

    const report = {
      deploymentId,
      strategy: this.options.strategy,
      environment: this.options.environment,
      version: this.options.version,
      status: status.status,
      progress: status.progress,
      startTime: new Date(status.startTime).toISOString(),
      endTime: status.endTime ? new Date(status.endTime).toISOString() : null,
      duration: status.endTime ? status.endTime - status.startTime : Date.now() - status.startTime,
      healthChecks: status.healthChecks,
      metrics: status.metrics,
      errors: status.errors,
      metadata: this.options,
    };

    // Save report
    const reportsDir = join(process.cwd(), 'deployment-reports');
    if (!existsSync(reportsDir)) {
      mkdirSync(reportsDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `deployment-${deploymentId}-${timestamp}.json`;
    const filepath = join(reportsDir, filename);

    writeFileSync(filepath, JSON.stringify(report, null, 2));
    console.log(`📊 Deployment report saved: ${filepath}`);
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  
  // Parse command line arguments
  const options: DeploymentOptions = {
    strategy: 'feature_flag',
    environment: 'staging',
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    if (arg.startsWith('--strategy=')) {
      options.strategy = arg.split('=')[1] as DeploymentOptions['strategy'];
    } else if (arg.startsWith('--environment=')) {
      options.environment = arg.split('=')[1] as DeploymentOptions['environment'];
    } else if (arg.startsWith('--percentage=')) {
      options.percentage = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--traffic=')) {
      options.traffic = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--version=')) {
      options.version = arg.split('=')[1];
    } else if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg === '--skip-tests') {
      options.skipTests = true;
    } else if (arg === '--skip-health-checks') {
      options.skipHealthChecks = true;
    } else if (arg === '--no-auto-rollback') {
      options.autoRollback = false;
    } else if (arg === '--help' || arg === '-h') {
      printHelp();
      return;
    }
  }

  try {
    const deployer = new RoutesV2Deployer(options);
    await deployer.deploy();
  } catch (error) {
    console.error('❌ Deployment failed:', error);
    process.exit(1);
  }
}

function printHelp() {
  console.log(`
Routes V2 Deployment Script

Usage: npm run deploy:routes-v2 [options]

Options:
  --strategy=STRATEGY        Deployment strategy (feature-flag, canary, rolling, blue-green)
  --environment=ENV          Target environment (staging, production)
  --percentage=NUM           Rollout percentage for feature flag strategy (default: 1)
  --traffic=NUM              Traffic percentage for canary strategy (default: 5)
  --version=VERSION          Version to deploy (default: current version)
  --dry-run                  Validate configuration without deploying
  --skip-tests               Skip test execution
  --skip-health-checks       Skip health check validation
  --no-auto-rollback         Disable automatic rollback on issues
  --help, -h                 Show this help message

Examples:
  npm run deploy:routes-v2 --strategy=feature-flag --percentage=5
  npm run deploy:routes-v2 --strategy=canary --traffic=10 --environment=production
  npm run deploy:routes-v2 --strategy=blue-green --environment=production --dry-run
  npm run deploy:routes-v2 --strategy=rolling --skip-tests --no-auto-rollback

Deployment Strategies:
  feature-flag: Gradual rollout using feature flags (recommended)
  canary: Deploy to subset of infrastructure with traffic splitting
  rolling: Rolling update across all instances
  blue-green: Deploy to parallel environment and switch traffic
`);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { RoutesV2Deployer, type DeploymentOptions };
