#!/usr/bin/env tsx

/**
 * Routes V2 Migration Test Runner
 * 
 * Comprehensive test runner for validating the Google Routes API v2 migration.
 * Runs all test suites and generates detailed reports.
 */

import { execSync } from 'child_process';
import { writeFileSync, mkdirSync, existsSync } from 'fs';
import { join } from 'path';

interface TestSuite {
  name: string;
  description: string;
  testFiles: string[];
  timeout: number;
  critical: boolean;
}

interface TestResult {
  suite: string;
  passed: number;
  failed: number;
  skipped: number;
  duration: number;
  coverage?: number;
  errors: string[];
}

interface TestReport {
  timestamp: string;
  environment: Record<string, string>;
  results: TestResult[];
  summary: {
    totalTests: number;
    totalPassed: number;
    totalFailed: number;
    totalSkipped: number;
    overallDuration: number;
    overallCoverage: number;
    success: boolean;
  };
}

// Test suite configuration
const TEST_SUITES: TestSuite[] = [
  {
    name: 'Unit Tests',
    description: 'Core functionality and service layer tests',
    testFiles: [
      'app/lib/__tests__/routes-v2-migration.test.ts',
    ],
    timeout: 30000,
    critical: true,
  },
  {
    name: 'Component Tests',
    description: 'React component integration tests',
    testFiles: [
      'app/components/route-planner/__tests__/enhanced-components.test.tsx',
    ],
    timeout: 45000,
    critical: true,
  },
  {
    name: 'Performance Tests',
    description: 'Performance and load testing',
    testFiles: [
      'app/lib/__tests__/routes-v2-performance.test.ts',
    ],
    timeout: 60000,
    critical: false,
  },
  {
    name: 'E2E Integration Tests',
    description: 'End-to-end workflow validation',
    testFiles: [
      'app/__tests__/e2e/routes-v2-integration.test.tsx',
    ],
    timeout: 90000,
    critical: true,
  },
];

// Environment configurations for testing
const TEST_ENVIRONMENTS = {
  'routes-v2-enabled': {
    VITE_USE_ROUTES_V2: 'true',
    VITE_ROUTES_V2_PERCENTAGE: '100',
    VITE_ENABLE_ROUTES_FALLBACK: 'true',
    VITE_ROUTES_DEBUG: 'true',
    VITE_GOOGLE_MAPS_API_KEY: 'test-api-key',
  },
  'routes-v2-disabled': {
    VITE_USE_ROUTES_V2: 'false',
    VITE_ROUTES_V2_PERCENTAGE: '0',
    VITE_ENABLE_ROUTES_FALLBACK: 'true',
    VITE_ROUTES_DEBUG: 'false',
    VITE_GOOGLE_MAPS_API_KEY: 'test-api-key',
  },
  'routes-v2-partial': {
    VITE_USE_ROUTES_V2: 'true',
    VITE_ROUTES_V2_PERCENTAGE: '50',
    VITE_ENABLE_ROUTES_FALLBACK: 'true',
    VITE_ROUTES_DEBUG: 'true',
    VITE_GOOGLE_MAPS_API_KEY: 'test-api-key',
  },
};

class RoutesV2TestRunner {
  private results: TestResult[] = [];
  private startTime: number = 0;

  constructor(private environment: string = 'routes-v2-enabled') {
    this.setupEnvironment();
  }

  private setupEnvironment() {
    const envConfig = TEST_ENVIRONMENTS[this.environment as keyof typeof TEST_ENVIRONMENTS];
    if (!envConfig) {
      throw new Error(`Unknown environment: ${this.environment}`);
    }

    // Set environment variables
    Object.assign(process.env, envConfig);
    
    console.log(`🔧 Environment configured: ${this.environment}`);
    console.log(`   Routes V2: ${envConfig.VITE_USE_ROUTES_V2}`);
    console.log(`   Percentage: ${envConfig.VITE_ROUTES_V2_PERCENTAGE}%`);
    console.log(`   Fallback: ${envConfig.VITE_ENABLE_ROUTES_FALLBACK}`);
  }

  async runTestSuite(suite: TestSuite): Promise<TestResult> {
    console.log(`\n🧪 Running ${suite.name}...`);
    console.log(`   ${suite.description}`);
    
    const startTime = Date.now();
    let passed = 0;
    let failed = 0;
    let skipped = 0;
    const errors: string[] = [];

    try {
      for (const testFile of suite.testFiles) {
        console.log(`   📄 Testing: ${testFile}`);
        
        try {
          const command = `npx vitest run ${testFile} --reporter=json --timeout=${suite.timeout}`;
          const output = execSync(command, { 
            encoding: 'utf8',
            timeout: suite.timeout + 10000, // Add buffer
            env: { ...process.env, NODE_ENV: 'test' }
          });
          
          // Parse vitest JSON output
          const result = this.parseVitestOutput(output);
          passed += result.passed;
          failed += result.failed;
          skipped += result.skipped;
          
          if (result.errors.length > 0) {
            errors.push(...result.errors);
          }
          
        } catch (error) {
          console.error(`   ❌ Error running ${testFile}:`, error);
          failed++;
          errors.push(`${testFile}: ${error}`);
        }
      }
    } catch (error) {
      console.error(`   ❌ Suite failed:`, error);
      failed++;
      errors.push(`Suite error: ${error}`);
    }

    const duration = Date.now() - startTime;
    const result: TestResult = {
      suite: suite.name,
      passed,
      failed,
      skipped,
      duration,
      errors,
    };

    // Log results
    if (failed === 0) {
      console.log(`   ✅ ${suite.name} passed (${passed} tests, ${duration}ms)`);
    } else {
      console.log(`   ❌ ${suite.name} failed (${passed} passed, ${failed} failed, ${duration}ms)`);
      if (errors.length > 0) {
        console.log(`   Errors:`);
        errors.forEach(error => console.log(`     - ${error}`));
      }
    }

    return result;
  }

  private parseVitestOutput(output: string): { passed: number; failed: number; skipped: number; errors: string[] } {
    try {
      // Try to parse JSON output from vitest
      const lines = output.split('\n').filter(line => line.trim());
      const jsonLine = lines.find(line => line.startsWith('{') && line.includes('testResults'));
      
      if (jsonLine) {
        const result = JSON.parse(jsonLine);
        return {
          passed: result.numPassedTests || 0,
          failed: result.numFailedTests || 0,
          skipped: result.numPendingTests || 0,
          errors: result.testResults?.flatMap((tr: any) => 
            tr.assertionResults?.filter((ar: any) => ar.status === 'failed')
              .map((ar: any) => ar.failureMessages?.join('; ') || 'Unknown error')
          ) || [],
        };
      }
    } catch (error) {
      console.warn('Failed to parse vitest output, using fallback parsing');
    }

    // Fallback parsing
    const passedMatch = output.match(/(\d+) passed/);
    const failedMatch = output.match(/(\d+) failed/);
    const skippedMatch = output.match(/(\d+) skipped/);

    return {
      passed: passedMatch ? parseInt(passedMatch[1]) : 0,
      failed: failedMatch ? parseInt(failedMatch[1]) : 0,
      skipped: skippedMatch ? parseInt(skippedMatch[1]) : 0,
      errors: [],
    };
  }

  async runAllTests(): Promise<TestReport> {
    console.log('🚀 Starting Routes V2 Migration Test Suite');
    console.log(`📅 ${new Date().toISOString()}`);
    
    this.startTime = Date.now();
    this.results = [];

    // Run each test suite
    for (const suite of TEST_SUITES) {
      const result = await this.runTestSuite(suite);
      this.results.push(result);

      // Stop on critical failures if requested
      if (suite.critical && result.failed > 0) {
        console.log(`\n⚠️  Critical test suite failed: ${suite.name}`);
        console.log('   Stopping execution due to critical failure.');
        break;
      }
    }

    // Generate report
    const report = this.generateReport();
    this.saveReport(report);
    this.printSummary(report);

    return report;
  }

  private generateReport(): TestReport {
    const totalDuration = Date.now() - this.startTime;
    
    const summary = this.results.reduce(
      (acc, result) => ({
        totalTests: acc.totalTests + result.passed + result.failed + result.skipped,
        totalPassed: acc.totalPassed + result.passed,
        totalFailed: acc.totalFailed + result.failed,
        totalSkipped: acc.totalSkipped + result.skipped,
        overallDuration: totalDuration,
        overallCoverage: 0, // TODO: Calculate coverage
        success: acc.success && result.failed === 0,
      }),
      {
        totalTests: 0,
        totalPassed: 0,
        totalFailed: 0,
        totalSkipped: 0,
        overallDuration: totalDuration,
        overallCoverage: 0,
        success: true,
      }
    );

    return {
      timestamp: new Date().toISOString(),
      environment: TEST_ENVIRONMENTS[this.environment as keyof typeof TEST_ENVIRONMENTS],
      results: this.results,
      summary,
    };
  }

  private saveReport(report: TestReport) {
    const reportsDir = join(process.cwd(), 'test-reports');
    if (!existsSync(reportsDir)) {
      mkdirSync(reportsDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `routes-v2-test-report-${this.environment}-${timestamp}.json`;
    const filepath = join(reportsDir, filename);

    writeFileSync(filepath, JSON.stringify(report, null, 2));
    console.log(`\n📊 Test report saved: ${filepath}`);
  }

  private printSummary(report: TestReport) {
    console.log('\n' + '='.repeat(60));
    console.log('📋 ROUTES V2 MIGRATION TEST SUMMARY');
    console.log('='.repeat(60));
    
    console.log(`Environment: ${this.environment}`);
    console.log(`Duration: ${(report.summary.overallDuration / 1000).toFixed(2)}s`);
    console.log(`Total Tests: ${report.summary.totalTests}`);
    console.log(`✅ Passed: ${report.summary.totalPassed}`);
    console.log(`❌ Failed: ${report.summary.totalFailed}`);
    console.log(`⏭️  Skipped: ${report.summary.totalSkipped}`);
    
    if (report.summary.success) {
      console.log('\n🎉 ALL TESTS PASSED! Routes V2 migration is ready.');
    } else {
      console.log('\n⚠️  SOME TESTS FAILED. Review the results above.');
      
      // Show failed suites
      const failedSuites = report.results.filter(r => r.failed > 0);
      if (failedSuites.length > 0) {
        console.log('\nFailed Test Suites:');
        failedSuites.forEach(suite => {
          console.log(`  - ${suite.suite}: ${suite.failed} failures`);
        });
      }
    }
    
    console.log('='.repeat(60));
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const environment = args[0] || 'routes-v2-enabled';
  
  console.log('🧪 Routes V2 Migration Test Runner');
  console.log('==================================');
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('\nUsage: npm run test:routes-v2 [environment]');
    console.log('\nEnvironments:');
    Object.keys(TEST_ENVIRONMENTS).forEach(env => {
      console.log(`  - ${env}`);
    });
    console.log('\nOptions:');
    console.log('  --help, -h    Show this help message');
    console.log('\nExamples:');
    console.log('  npm run test:routes-v2');
    console.log('  npm run test:routes-v2 routes-v2-disabled');
    console.log('  npm run test:routes-v2 routes-v2-partial');
    return;
  }

  try {
    const runner = new RoutesV2TestRunner(environment);
    const report = await runner.runAllTests();
    
    // Exit with appropriate code
    process.exit(report.summary.success ? 0 : 1);
    
  } catch (error) {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { RoutesV2TestRunner, TEST_SUITES, TEST_ENVIRONMENTS };
