<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wanderlust Explorer</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .app-container {
            max-width: 1400px;
            margin: 0 auto;
            min-height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="3" fill="rgba(255,255,255,0.05)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(-100px); }
            100% { transform: translateX(calc(100vw + 100px)); }
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .city-hub {
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #dee2e6;
        }

        .city-cards {
            display: flex;
            gap: 15px;
            overflow-x: auto;
            padding: 10px 0;
            scrollbar-width: thin;
            scrollbar-color: #6c757d #f8f9fa;
        }

        .city-card {
            min-width: 200px;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .city-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .city-card:hover::before,
        .city-card.active::before {
            transform: scaleX(1);
        }

        .city-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .city-card.active {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-3px);
        }

        .city-card h3 {
            font-size: 1.2rem;
            margin-bottom: 8px;
        }

        .city-card p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 200px);
        }

        .map-container {
            height: 65%;
            position: relative;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 0 0 20px 20px;
            overflow: hidden;
            margin: 0 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .mock-map {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
            position: relative;
            overflow: hidden;
        }

        .map-markers {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .marker {
            position: absolute;
            width: 30px;
            height: 30px;
            background: #e74c3c;
            border-radius: 50% 50% 50% 0;
            transform: rotate(-45deg);
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(231, 76, 60, 0.4);
            animation: bounce 2s infinite;
        }

        .marker::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(45deg);
            width: 12px;
            height: 12px;
            background: white;
            border-radius: 50%;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: rotate(-45deg) translateY(0); }
            40% { transform: rotate(-45deg) translateY(-10px); }
            60% { transform: rotate(-45deg) translateY(-5px); }
        }

        .marker.food { background: #f39c12; box-shadow: 0 2px 10px rgba(243, 156, 18, 0.4); }
        .marker.landmark { background: #9b59b6; box-shadow: 0 2px 10px rgba(155, 89, 182, 0.4); }
        .marker.museum { background: #3498db; box-shadow: 0 2px 10px rgba(52, 152, 219, 0.4); }
        .marker.park { background: #27ae60; box-shadow: 0 2px 10px rgba(39, 174, 96, 0.4); }

        .marker:hover {
            transform: rotate(-45deg) scale(1.2);
            z-index: 10;
        }

        .map-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .control-btn {
            padding: 12px;
            background: white;
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .control-btn:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
        }

        .legend {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .explorer-log {
            height: 35%;
            padding: 20px;
            background: #f8f9fa;
            overflow-y: auto;
            border-top: 1px solid #dee2e6;
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .search-bar {
            flex: 1;
            min-width: 200px;
            padding: 12px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-bar:focus {
            border-color: #667eea;
        }

        .filter-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 16px;
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .places-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .place-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .place-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .place-card.highlighted {
            border: 2px solid #667eea;
            transform: translateY(-3px);
        }

        .place-image {
            width: 100%;
            height: 150px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            position: relative;
            overflow: hidden;
        }

        .place-content {
            padding: 20px;
        }

        .place-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .place-description {
            color: #6c757d;
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .place-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .place-category {
            padding: 4px 12px;
            background: #e9ecef;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
            color: #495057;
        }

        .place-rating {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #f39c12;
        }

        .place-actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .action-btn.primary {
            background: #667eea;
            color: white;
        }

        .action-btn.secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }

        .floating-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            max-width: 500px;
            width: 90%;
            z-index: 1000;
            backdrop-filter: blur(20px);
            display: none;
        }

        .floating-panel.show {
            display: block;
            animation: fadeInScale 0.3s ease;
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .panel-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #6c757d;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #f8f9fa;
            color: #495057;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
            backdrop-filter: blur(5px);
        }

        .overlay.show {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .city-cards {
                justify-content: flex-start;
            }

            .city-card {
                min-width: 160px;
            }

            .main-content {
                height: auto;
            }

            .map-container {
                height: 400px;
                margin: 0 10px;
            }

            .explorer-log {
                height: auto;
                min-height: 400px;
            }

            .places-grid {
                grid-template-columns: 1fr;
            }

            .log-header {
                flex-direction: column;
            }

            .search-bar {
                width: 100%;
            }
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #27ae60;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1001;
        }

        .notification.show {
            transform: translateX(0);
        }

        .itinerary-section {
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .itinerary-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            margin-bottom: 10px;
            border-radius: 10px;
            cursor: grab;
            transition: all 0.3s ease;
        }

        .itinerary-item:hover {
            background: #e9ecef;
        }

        .itinerary-item.dragging {
            opacity: 0.5;
            cursor: grabbing;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <header class="header">
            <h1>🗺️ Wanderlust Explorer</h1>
            <p>Your Personal Travel Log & Intelligent Recommendation Engine</p>
        </header>

        <div class="city-hub">
            <div class="city-cards" id="cityCards">
                <div class="city-card active" data-city="nyc">
                    <h3>🏙️ Greater NYC Area</h3>
                    <p>Urban adventures & cultural gems</p>
                </div>
                <div class="city-card" data-city="philadelphia">
                    <h3>🔔 Historic Philadelphia</h3>
                    <p>History, art & culinary delights</p>
                </div>
                <div class="city-card" data-city="nashville">
                    <h3>🎵 Music City Nashville</h3>
                    <p>Live music & southern charm</p>
                </div>
                <div class="city-card" data-city="portland">
                    <h3>🏔️ Portland, Oregon</h3>
                    <p>Outdoor beauty & local culture</p>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="map-container">
                <div class="mock-map" id="map">
                    <div class="map-markers" id="mapMarkers"></div>
                    <div class="map-controls">
                        <button class="control-btn" id="currentLocationBtn" title="My Location">📍</button>
                        <button class="control-btn" id="trafficBtn" title="Toggle Traffic">🚦</button>
                        <button class="control-btn" id="satelliteBtn" title="Satellite View">🛰️</button>
                    </div>
                    <div class="legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: #f39c12;"></div>
                            <span>Food & Drink</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #9b59b6;"></div>
                            <span>Landmarks</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #3498db;"></div>
                            <span>Museums</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #27ae60;"></div>
                            <span>Parks</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: #e74c3c;"></div>
                            <span>Other</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="explorer-log">
                <div class="log-header">
                    <input type="text" class="search-bar" id="searchBar" placeholder="🔍 Search places by name or description...">
                    <div class="filter-controls">
                        <button class="filter-btn active" data-filter="all">All</button>
                        <button class="filter-btn" data-filter="food">Food</button>
                        <button class="filter-btn" data-filter="landmark">Landmarks</button>
                        <button class="filter-btn" data-filter="museum">Museums</button>
                        <button class="filter-btn" data-filter="park">Parks</button>
                    </div>
                </div>
                <div class="places-grid" id="placesGrid"></div>
                
                <div class="itinerary-section" id="itinerarySection" style="display: none;">
                    <h3>📋 My Itinerary</h3>
                    <div id="itineraryList"></div>
                    <button class="action-btn primary" id="optimizeRouteBtn">🗺️ Optimize Route</button>
                    <button class="action-btn secondary" id="clearItineraryBtn">🗑️ Clear All</button>
                </div>
            </div>
        </div>
    </div>

    <div class="overlay" id="overlay"></div>
    <div class="floating-panel" id="directionsPanel">
        <div class="panel-header">
            <h3 class="panel-title">🗺️ Directions</h3>
            <button class="close-btn">&times;</button>
        </div>
        <div id="directionsContent">
            <div class="loading"></div>
            <p>Calculating optimal route...</p>
        </div>
    </div>

    <div class="notification" id="notification"></div>

    <script>
        // Sample data structure
        const visitedPlaces = {
            nyc: [
                {
                    id: 1,
                    name: "Central Park",
                    description: "A magnificent urban oasis in the heart of Manhattan with stunning landscapes and recreational activities.",
                    category: "park",
                    coordinates: { lat: 40.7829, lng: -73.9654 },
                    rating: 5,
                    keyTakeaway: "Perfect for morning jogs",
                    revisitPotential: "Highly Recommend",
                    icon: "🌳",
                    personalNotes: "Amazing sunrise views from Bow Bridge"
                },
                {
                    id: 2,
                    name: "Museum of Modern Art",
                    description: "World-renowned art museum featuring contemporary and modern art collections.",
                    category: "museum",
                    coordinates: { lat: 40.7614, lng: -73.9776 },
                    rating: 5,
                    keyTakeaway: "Must-see for art lovers",
                    revisitPotential: "Highly Recommend",
                    icon: "🎨",
                    personalNotes: "The Van Gogh exhibit was breathtaking"
                },
                {
                    id: 3,
                    name: "Joe's Pizza",
                    description: "Authentic New York-style pizza slices that locals swear by.",
                    category: "food",
                    coordinates: { lat: 40.7505, lng: -73.9934 },
                    rating: 4,
                    keyTakeaway: "Best late-night slice",
                    revisitPotential: "Worth a Look",
                    icon: "🍕",
                    personalNotes: "Cash only, but worth the wait"
                },
                {
                    id: 4,
                    name: "Statue of Liberty",
                    description: "Iconic symbol of freedom and democracy, accessible by ferry from Battery Park.",
                    category: "landmark",
                    coordinates: { lat: 40.6892, lng: -74.0445 },
                    rating: 5,
                    keyTakeaway: "Historic significance",
                    revisitPotential: "Highly Recommend",
                    icon: "🗽",
                    personalNotes: "Book crown access in advance"
                }
            ],
            philadelphia: [
                {
                    id: 5,
                    name: "Liberty Bell",
                    description: "Historic bell that symbolizes American independence and freedom.",
                    category: "landmark",
                    coordinates: { lat: 39.9496, lng: -75.1503 },
                    rating: 4,
                    keyTakeaway: "Historic significance",
                    revisitPotential: "Worth a Look",
                    icon: "🔔",
                    personalNotes: "Free but can get crowded"
                },
                {
                    id: 6,
                    name: "Reading Terminal Market",
                    description: "Historic farmers' market with diverse food vendors and local specialties.",
                    category: "food",
                    coordinates: { lat: 39.9526, lng: -75.1652 },
                    rating: 5,
                    keyTakeaway: "Food paradise",
                    revisitPotential: "Highly Recommend",
                    icon: "🥪",
                    personalNotes: "Try the roast pork sandwich at DiNic's"
                }
            ],
            nashville: [
                {
                    id: 7,
                    name: "Country Music Hall of Fame",
                    description: "Museum showcasing the history and legends of country music.",
                    category: "museum",
                    coordinates: { lat: 36.1581, lng: -86.7767 },
                    rating: 4,
                    keyTakeaway: "Music history",
                    revisitPotential: "Worth a Look",
                    icon: "🎸",
                    personalNotes: "Elvis exhibit was fascinating"
                },
                {
                    id: 8,
                    name: "Broadway District",
                    description: "Vibrant street with live music venues, honky-tonk bars, and street performers.",
                    category: "landmark",
                    coordinates: { lat: 36.1627, lng: -86.7816 },
                    rating: 5,
                    keyTakeaway: "Live music scene",
                    revisitPotential: "Highly Recommend",
                    icon: "🎵",
                    personalNotes: "Best after 8 PM when all venues are active"
                }
            ],
            portland: [
                {
                    id: 9,
                    name: "Powell's City of Books",
                    description: "World's largest independent bookstore occupying an entire city block.",
                    category: "landmark",
                    coordinates: { lat: 45.5230, lng: -122.6814 },
                    rating: 5,
                    keyTakeaway: "Book lover's paradise",
                    revisitPotential: "Highly Recommend",
                    icon: "📚",
                    personalNotes: "Easy to spend hours here"
                },
                {
                    id: 10,
                    name: "Washington Park",
                    description: "Large urban park featuring rose gardens, hiking trails, and cultural attractions.",
                    category: "park",
                    coordinates: { lat: 45.5105, lng: -122.7178 },
                    rating: 4,
                    keyTakeaway: "Great views",
                    revisitPotential: "Worth a Look",
                    icon: "🌹",
                    personalNotes: "Rose garden is stunning in June"
                }
            ]
        };

        let currentCity = 'nyc';
        let currentPlaces = visitedPlaces[currentCity];
        let filteredPlaces = [...currentPlaces];
        let selectedPlaces = [];
        let itinerary = [];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeCityCards();
            renderMap();
            renderPlaces();
            initializeSearch();
            initializeFilters();
            initializeMapControls();
        });

        function initializeCityCards() {
            const cityCards = document.querySelectorAll('.city-card');
            cityCards.forEach(card => {
                card.addEventListener('click', function() {
                    const city = this.dataset.city;
                    selectCity(city);
                });
            });
        }

        function selectCity(city) {
            currentCity = city;
            currentPlaces = visitedPlaces[city] || [];
            filteredPlaces = [...currentPlaces];

            // Update active city card
            document.querySelectorAll('.city-card').forEach(card => {
                card.classList.remove('active');
            });
            document.querySelector(`[data-city="${city}"]`).classList.add('active');

            // Re-render map and places
            renderMap();
            renderPlaces();
            showNotification(`Switched to ${city.toUpperCase()} area`);
        }

        function renderMap() {
            const mapMarkers = document.getElementById('mapMarkers');
            mapMarkers.innerHTML = '';

            currentPlaces.forEach((place, index) => {
                const marker = document.createElement('div');
                marker
                