# Route Planner & Venue Search Refactoring Strategy

## Executive Summary

This document outlines a comprehensive refactoring strategy to extract common functionality between route planning and venue search components while maintaining their distinct user experiences and following established architectural patterns.

## Current Architecture Analysis

### Shared Patterns Identified

#### 1. Map Integration Patterns

- **MapProvider Context**: Both components use the same context for map state management
- **Google Maps Loading**: Shared loading states and error handling via `GoogleMapsLoader`
- **Marker Rendering**: Common `MarkerLayer` with FIFA design system category colors
- **Map Interactions**: Similar click, drag, zoom event handling patterns
- **Styling**: Consistent map options and FIFA Club World Cup 2025™ design system

#### 2. Location Handling Patterns

- **Core Data Structure**: Both use `VisitedPlace` interface
- **Coordinate Transformation**: Converting between lat/lng formats
- **Place Selection**: Common selection/deselection workflows
- **Current Location**: Shared geolocation integration

#### 3. User Interaction Patterns

- **Search Functionality**: `SearchOverlay` and `PlaceSearchInput` components
- **Category Filtering**: `CategoryFilter` with consistent UI patterns
- **Place Management**: Add/remove place workflows
- **Loading States**: Shared skeleton states and error notifications

### Common Data Structures

```typescript
// Core shared interfaces
VisitedPlace          // Primary place representation
CityRegion           // Regional place grouping
RouteWaypoint        // Route planning waypoints
PlaceSearchResult    // Google Places API results
```

### API Integration Patterns

- **Google Places API**: `useGooglePlaces` hook with caching
- **Google Routes API v2**: `useRoutesV2` hook with enhanced features
- **Error Handling**: Consistent notification patterns
- **Caching**: Shared response caching strategies

## Proposed Refactoring Architecture

### Phase 1: Extract Core Map Abstractions

#### 1.1 Create Base Map Component (`BaseMapComponent`)

**Purpose**: Unified map rendering with configurable features
**Location**: `app/components/maps/BaseMapComponent.tsx`

```typescript
interface BaseMapProps {
  features: MapFeature[];
  onMapLoad?: (map: google.maps.Map) => void;
  onPlaceSelect?: (place: VisitedPlace) => void;
  customMarkerRenderer?: (place: VisitedPlace) => MarkerConfig;
  children?: React.ReactNode;
  className?: string;
}
```

**Features**:

- Configurable feature flags (markers, routes, search, traffic)
- Standardized loading states with FIFA design system
- Unified error handling and fallback states
- Consistent map options and styling
- Pluggable marker rendering system

#### 1.2 Enhanced MapProvider (`EnhancedMapProvider`)

**Purpose**: Extended context with venue search capabilities
**Location**: `app/components/maps/EnhancedMapProvider.tsx`

```typescript
interface EnhancedMapContextValue extends MapContextValue {
  // Venue search state
  searchQuery: string;
  searchResults: PlaceSearchResult[];
  isSearching: boolean;
  searchHistory: string[];

  // Place management
  availablePlaces: VisitedPlace[];
  filteredPlaces: VisitedPlace[];
  selectedCategory: string;

  // Actions
  searchPlaces: (query: string) => Promise<PlaceSearchResult[]>;
  filterPlaces: (category: string, query: string) => void;
  addPlace: (place: VisitedPlace) => void;
  removePlace: (placeId: string) => void;
}
```

### Phase 2: Create Shared Hooks and Utilities

#### 2.1 Unified Place Management Hook (`useUnifiedPlaceManagement`)

**Purpose**: Common place operations across both contexts
**Location**: `app/hooks/useUnifiedPlaceManagement.ts`

```typescript
interface UnifiedPlaceManagementOptions {
  mode: 'route-planning' | 'venue-discovery';
  enableWaypoints?: boolean;
  enableSearch?: boolean;
  maxPlaces?: number;
}

export function useUnifiedPlaceManagement(options: UnifiedPlaceManagementOptions) {
  // Unified place operations
  // Context-aware behavior based on mode
  // Shared validation and error handling
}
```

#### 2.2 Enhanced Search Hook (`useEnhancedPlaceSearch`)

**Purpose**: Unified search with context-aware filtering
**Location**: `app/hooks/useEnhancedPlaceSearch.ts`

```typescript
interface EnhancedSearchOptions {
  searchMode: 'local' | 'google' | 'hybrid';
  enableCategoryFilter: boolean;
  enableLocationBias: boolean;
  cacheResults: boolean;
}

export function useEnhancedPlaceSearch(
  regions: CityRegion[],
  options: EnhancedSearchOptions
) {
  // Unified search across local data and Google Places
  // Smart caching and result merging
  // Context-aware filtering
}
```

#### 2.3 Map Interaction Hook (`useMapInteractions`)

**Purpose**: Standardized map event handling
**Location**: `app/hooks/useMapInteractions.ts`

```typescript
export function useMapInteractions(config: MapInteractionConfig) {
  // Standardized click, drag, zoom handlers
  // Gesture recognition for mobile
  // Context-aware interaction modes
}
```

### Phase 3: Create Reusable UI Components

#### 3.1 Unified Place Card (`UnifiedPlaceCard`)

**Purpose**: Configurable place display component
**Location**: `app/components/shared/UnifiedPlaceCard.tsx`

```typescript
interface UnifiedPlaceCardProps {
  place: VisitedPlace;
  variant: 'search-result' | 'waypoint' | 'venue-detail';
  actions: PlaceCardAction[];
  showDetails?: boolean;
  className?: string;
}
```

#### 3.2 Enhanced Search Interface (`EnhancedSearchInterface`)

**Purpose**: Unified search UI with context switching
**Location**: `app/components/shared/EnhancedSearchInterface.tsx`

```typescript
interface EnhancedSearchInterfaceProps {
  searchMode: 'route-planning' | 'venue-discovery';
  onPlaceSelect: (place: VisitedPlace) => void;
  enableFilters?: boolean;
  enableHistory?: boolean;
  className?: string;
}
```

#### 3.3 Place Management Panel (`PlaceManagementPanel`)

**Purpose**: Configurable place list management
**Location**: `app/components/shared/PlaceManagementPanel.tsx`

```typescript
interface PlaceManagementPanelProps {
  mode: 'waypoints' | 'favorites' | 'search-results';
  places: VisitedPlace[];
  enableReordering?: boolean;
  enableGrouping?: boolean;
  maxHeight?: string;
  onPlaceAction: (action: PlaceAction, place: VisitedPlace) => void;
}
```

### Phase 4: Specialized Component Adaptations

#### 4.1 Route Planning Specialization

**Enhanced RouteMap**: Extends `BaseMapComponent` with route-specific features

```typescript
// app/components/route-planner/EnhancedRouteMap.tsx
export function EnhancedRouteMap() {
  return (
    <BaseMapComponent
      features={['markers', 'routes', 'search', 'traffic']}
      customMarkerRenderer={routeWaypointRenderer}
      onPlaceSelect={handleWaypointSelection}
    >
      <RouteLayer showTrafficOnPolylines />
      <WaypointManagementOverlay />
    </BaseMapComponent>
  );
}
```

**Smart Route Builder**: Integrates unified components

```typescript
// app/components/route-planner/SmartRouteBuilder.tsx
export function SmartRouteBuilder() {
  return (
    <div className="route-builder">
      <EnhancedSearchInterface
        searchMode="route-planning"
        onPlaceSelect={addWaypoint}
      />
      <PlaceManagementPanel
        mode="waypoints"
        places={waypoints}
        enableReordering
        onPlaceAction={handleWaypointAction}
      />
    </div>
  );
}
```

#### 4.2 Venue Discovery Specialization

**Venue Explorer Map**: Extends `BaseMapComponent` for venue discovery

```typescript
// app/components/venue-discovery/VenueExplorerMap.tsx
export function VenueExplorerMap() {
  return (
    <BaseMapComponent
      features={['markers', 'search', 'clustering']}
      customMarkerRenderer={venueMarkerRenderer}
      onPlaceSelect={handleVenueSelection}
    >
      <VenueClusterLayer />
      <VenueDetailsOverlay />
    </BaseMapComponent>
  );
}
```

**Venue Discovery Panel**: Specialized venue browsing

```typescript
// app/components/venue-discovery/VenueDiscoveryPanel.tsx
export function VenueDiscoveryPanel() {
  return (
    <div className="venue-discovery">
      <EnhancedSearchInterface
        searchMode="venue-discovery"
        onPlaceSelect={selectVenue}
        enableFilters
      />
      <PlaceManagementPanel
        mode="search-results"
        places={venues}
        enableGrouping
        onPlaceAction={handleVenueAction}
      />
    </div>
  );
}
```

## Implementation Plan

### Phase 1: Foundation (Week 1-2)

1. Create `BaseMapComponent` with configurable features
2. Extend `MapProvider` to `EnhancedMapProvider`
3. Extract common map interaction patterns
4. Implement unified loading and error states

### Phase 2: Shared Logic (Week 2-3)

1. Create `useUnifiedPlaceManagement` hook
2. Implement `useEnhancedPlaceSearch` with hybrid search
3. Extract `useMapInteractions` for standardized events
4. Create shared utility functions

### Phase 3: UI Components (Week 3-4)

1. Build `UnifiedPlaceCard` with variants
2. Create `EnhancedSearchInterface` component
3. Implement `PlaceManagementPanel` with modes
4. Design responsive layouts for mobile-first

### Phase 4: Integration (Week 4-5)

1. Refactor `RouteMap` to use `BaseMapComponent`
2. Update route planning components to use shared hooks
3. Create venue discovery components using shared abstractions
4. Implement comprehensive testing

### Phase 5: Optimization (Week 5-6)

1. Performance optimization and code splitting
2. Enhanced caching strategies
3. Mobile gesture improvements
4. Accessibility enhancements

## Benefits

### Code Reduction

- **Estimated 40% reduction** in map-related code duplication
- **Unified testing** strategies for common functionality
- **Consistent behavior** across different contexts

### Maintainability

- **Single source of truth** for map interactions
- **Centralized** Google Maps API integration
- **Standardized** error handling and loading states

### User Experience

- **Consistent** interaction patterns across features
- **Improved performance** through shared caching
- **Enhanced mobile** experience with unified gestures

### Developer Experience

- **Clear separation** of concerns
- **Reusable components** for future features
- **Type-safe** interfaces with comprehensive TypeScript support

## Migration Strategy

### Backward Compatibility

- Maintain existing component APIs during transition
- Gradual migration with feature flags
- Comprehensive testing at each phase

### Risk Mitigation

- Incremental rollout with rollback capabilities
- Extensive testing of shared components
- Performance monitoring during migration

### Documentation

- Updated component hierarchy diagrams
- Migration guides for developers
- Best practices documentation

## Success Metrics

### Technical Metrics

- Code duplication reduction: Target 40%
- Bundle size optimization: Target 15% reduction
- Test coverage: Maintain 90%+

### Performance Metrics

- Map loading time: Target <2s improvement
- Search response time: Target <500ms
- Memory usage: Target 20% reduction

### Developer Metrics

- Component reusability: Target 80% shared components
- Development velocity: Target 25% faster feature development
- Bug reduction: Target 30% fewer map-related issues

## Detailed Component Specifications

### BaseMapComponent Architecture

```typescript
// app/components/maps/BaseMapComponent.tsx
interface BaseMapComponentProps {
  // Core configuration
  features: MapFeature[];
  mode: 'route-planning' | 'venue-discovery' | 'general';

  // Map settings
  initialCenter?: { lat: number; lng: number };
  initialZoom?: number;
  mapType?: 'roadmap' | 'satellite';

  // Event handlers
  onMapLoad?: (map: google.maps.Map) => void;
  onPlaceSelect?: (place: VisitedPlace) => void;
  onMapClick?: (event: google.maps.MapMouseEvent) => void;

  // Customization
  customMarkerRenderer?: (place: VisitedPlace, context: MarkerContext) => MarkerConfig;
  customStyles?: google.maps.MapOptions;

  // Layout
  children?: React.ReactNode;
  className?: string;
  containerStyle?: React.CSSProperties;
}

interface MarkerContext {
  mode: 'route-planning' | 'venue-discovery' | 'general';
  isSelected: boolean;
  index?: number;
  totalCount: number;
}

interface MarkerConfig {
  icon: google.maps.Symbol | google.maps.Icon | string;
  animation?: google.maps.Animation;
  zIndex?: number;
  title?: string;
}
```

### Enhanced Search Integration

```typescript
// app/hooks/useEnhancedPlaceSearch.ts
interface SearchResult {
  source: 'local' | 'google';
  place: VisitedPlace;
  relevanceScore: number;
  distance?: number;
}

interface SearchState {
  query: string;
  results: SearchResult[];
  isSearching: boolean;
  selectedCategory: string;
  searchHistory: string[];
  recentSearches: string[];
}

export function useEnhancedPlaceSearch(
  regions: CityRegion[],
  options: EnhancedSearchOptions
) {
  // Hybrid search implementation
  const searchLocal = useCallback(async (query: string) => {
    // Search through local region data
  }, [regions]);

  const searchGoogle = useCallback(async (query: string) => {
    // Search using Google Places API
  }, []);

  const hybridSearch = useCallback(async (query: string) => {
    // Combine and rank results from both sources
    const [localResults, googleResults] = await Promise.all([
      searchLocal(query),
      searchGoogle(query)
    ]);

    return mergeAndRankResults(localResults, googleResults);
  }, [searchLocal, searchGoogle]);

  return {
    searchState,
    searchActions: {
      search: hybridSearch,
      clearResults,
      setCategory,
      addToHistory
    }
  };
}
```

### Unified Place Management

```typescript
// app/hooks/useUnifiedPlaceManagement.ts
interface PlaceAction {
  type: 'add' | 'remove' | 'reorder' | 'select' | 'favorite';
  payload: any;
}

interface PlaceManagementState {
  places: VisitedPlace[];
  selectedPlace: VisitedPlace | null;
  favorites: string[];
  recentlyAdded: string[];
}

export function useUnifiedPlaceManagement(options: UnifiedPlaceManagementOptions) {
  const { mode, enableWaypoints, enableSearch, maxPlaces } = options;

  // Context-aware place operations
  const addPlace = useCallback((place: VisitedPlace) => {
    if (mode === 'route-planning') {
      // Add as waypoint with validation
      return addWaypoint(place);
    } else {
      // Add to favorites or collection
      return addToCollection(place);
    }
  }, [mode]);

  const removePlace = useCallback((placeId: string) => {
    if (mode === 'route-planning') {
      return removeWaypoint(placeId);
    } else {
      return removeFromCollection(placeId);
    }
  }, [mode]);

  return {
    state: placeManagementState,
    actions: {
      addPlace,
      removePlace,
      reorderPlaces,
      selectPlace,
      toggleFavorite
    },
    validation: {
      canAddPlace,
      validatePlaceLimit,
      checkDuplicates
    }
  };
}
```

This refactoring strategy provides a comprehensive approach to extracting common functionality while preserving the distinct user experiences of route planning and venue discovery, following established patterns and maintaining the FIFA Club World Cup 2025™ design system consistency.
