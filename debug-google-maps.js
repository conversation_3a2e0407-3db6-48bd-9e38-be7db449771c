/**
 * Google Maps Debug Script
 * 
 * Run this in the browser console to diagnose Google Maps loading issues
 * after the recent refactoring to simplified architecture.
 */

console.log('🔍 Starting Google Maps Diagnostic...');

// 1. Check Environment Variables
console.group('📋 Environment Configuration');
const apiKey = import.meta?.env?.VITE_GOOGLE_MAPS_API_KEY || 
               process.env?.VITE_GOOGLE_MAPS_API_KEY ||
               window?.ENV?.VITE_GOOGLE_MAPS_API_KEY;

console.log('API Key Present:', !!apiKey);
console.log('API Key Length:', apiKey ? apiKey.length : 0);
console.log('API Key Preview:', apiKey ? `${apiKey.substring(0, 8)}...` : 'Not found');

// Check if running in development vs production
console.log('Environment:', import.meta?.env?.MODE || 'unknown');
console.log('Development Mode:', import.meta?.env?.DEV || false);
console.groupEnd();

// 2. Check Google Maps API Loading
console.group('🗺️ Google Maps API Status');
console.log('window.google exists:', !!window.google);
console.log('window.google.maps exists:', !!window.google?.maps);
console.log('Google Maps version:', window.google?.maps?.version || 'Not loaded');

// Check specific APIs we need
if (window.google?.maps) {
  console.log('Places API available:', !!window.google.maps.places);
  console.log('Geometry API available:', !!window.google.maps.geometry);
  console.log('Routes API available:', !!window.google.maps.routes);
  console.log('DirectionsService available:', !!window.google.maps.DirectionsService);
}
console.groupEnd();

// 3. Check Network Connectivity
console.group('🌐 Network Connectivity');
async function checkGoogleMapsConnectivity() {
  try {
    const response = await fetch('https://maps.googleapis.com/maps/api/js?key=' + apiKey, {
      method: 'HEAD',
      mode: 'no-cors'
    });
    console.log('Google Maps API reachable:', true);
  } catch (error) {
    console.error('Google Maps API connectivity issue:', error);
  }
}

if (apiKey) {
  checkGoogleMapsConnectivity();
} else {
  console.warn('Cannot test connectivity - no API key found');
}
console.groupEnd();

// 4. Check Console Errors
console.group('❌ Recent Console Errors');
// Store original console.error to capture errors
const originalError = console.error;
const recentErrors = [];

console.error = function(...args) {
  recentErrors.push({
    timestamp: new Date().toISOString(),
    message: args.join(' ')
  });
  originalError.apply(console, args);
};

// Show recent errors related to Google Maps
const mapsErrors = recentErrors.filter(error => 
  error.message.toLowerCase().includes('google') ||
  error.message.toLowerCase().includes('maps') ||
  error.message.toLowerCase().includes('loader')
);

console.log('Recent Google Maps related errors:', mapsErrors);
console.groupEnd();

// 5. Check Component State
console.group('⚛️ React Component State');
// Try to find GoogleMapsLoader context
const mapElements = document.querySelectorAll('[data-testid*="map"], [class*="map"], [class*="google"]');
console.log('Map-related DOM elements found:', mapElements.length);

mapElements.forEach((element, index) => {
  console.log(`Element ${index}:`, {
    tagName: element.tagName,
    className: element.className,
    id: element.id,
    textContent: element.textContent?.substring(0, 100)
  });
});
console.groupEnd();

// 6. Test Dynamic Import
console.group('📦 Dynamic Import Test');
async function testGoogleMapsLoader() {
  try {
    console.log('Testing dynamic import of @googlemaps/js-api-loader...');
    const module = await import('@googlemaps/js-api-loader');
    console.log('Dynamic import successful:', !!module);
    console.log('Loader available:', !!module.Loader);
    console.log('Module structure:', Object.keys(module));
    
    if (module.Loader && apiKey) {
      console.log('Testing Loader instantiation...');
      const loader = new module.Loader({
        apiKey: apiKey,
        version: 'weekly',
        libraries: ['places', 'geometry', 'routes']
      });
      console.log('Loader created successfully:', !!loader);
    }
  } catch (error) {
    console.error('Dynamic import failed:', error);
  }
}

testGoogleMapsLoader();
console.groupEnd();

// 7. Check Error Boundary State
console.group('🛡️ Error Boundary Analysis');
const errorBoundaryElements = document.querySelectorAll('[class*="error"], [class*="fallback"], [class*="demo"]');
console.log('Error boundary/fallback elements:', errorBoundaryElements.length);

errorBoundaryElements.forEach((element, index) => {
  console.log(`Fallback element ${index}:`, {
    className: element.className,
    textContent: element.textContent?.substring(0, 50),
    visible: element.offsetParent !== null
  });
});
console.groupEnd();

// 8. Performance Timing
console.group('⏱️ Performance Analysis');
if (window.performance && window.performance.getEntriesByType) {
  const navigationEntries = window.performance.getEntriesByType('navigation');
  const resourceEntries = window.performance.getEntriesByType('resource');
  
  console.log('Page load time:', navigationEntries[0]?.loadEventEnd || 'Unknown');
  
  const googleMapsResources = resourceEntries.filter(entry => 
    entry.name.includes('googleapis.com') || 
    entry.name.includes('google.com/maps')
  );
  
  console.log('Google Maps resources loaded:', googleMapsResources.length);
  googleMapsResources.forEach(resource => {
    console.log(`Resource: ${resource.name} - Duration: ${resource.duration}ms`);
  });
}
console.groupEnd();

// 9. Recommendations
console.group('💡 Debugging Recommendations');
console.log('1. Check browser Network tab for failed requests to googleapis.com');
console.log('2. Verify API key has Maps JavaScript API enabled in Google Cloud Console');
console.log('3. Check if domain is authorized in API key restrictions');
console.log('4. Look for CORS errors in console');
console.log('5. Verify environment variables are loaded correctly');
console.log('6. Check if ad blockers are interfering with Google Maps');
console.groupEnd();

console.log('🔍 Google Maps Diagnostic Complete');
