# Shared Map Abstractions Developer Guide

## Overview

This guide documents the shared abstractions created to unify route planning and venue search functionality while maintaining their distinct user experiences. The architecture follows established patterns from the existing codebase and preserves the FIFA Club World Cup 2025™ design system.

## Architecture Principles

### 1. Separation of Concerns

- **BaseMapComponent**: Core map rendering with configurable features
- **Enhanced Hooks**: Unified business logic with context-aware behavior
- **Specialized Components**: Domain-specific implementations using shared abstractions

### 2. Progressive Enhancement

- Start with basic map functionality
- Layer on features through configuration
- Maintain backward compatibility

### 3. Mobile-First Design

- Responsive layouts with flexible CSS
- Touch-friendly interactions
- Progressive disclosure patterns

## Core Components

### BaseMapComponent

The foundation component that provides unified map rendering with configurable features.

```typescript
// app/components/maps/BaseMapComponent.tsx
interface BaseMapComponentProps {
  // Core configuration
  features: MapFeature[];
  mode: 'route-planning' | 'venue-discovery' | 'general';

  // Map settings
  initialCenter?: { lat: number; lng: number };
  initialZoom?: number;
  mapType?: 'roadmap' | 'satellite';

  // Event handlers
  onMapLoad?: (map: google.maps.Map) => void;
  onPlaceSelect?: (place: VisitedPlace) => void;
  onMapClick?: (event: google.maps.MapMouseEvent) => void;

  // Customization
  customMarkerRenderer?: (place: VisitedPlace, context: MarkerContext) => MarkerConfig;
  customStyles?: google.maps.MapOptions;

  // Layout
  children?: React.ReactNode;
  className?: string;
  containerStyle?: React.CSSProperties;
}
```

**Key Features:**

- Configurable feature flags (markers, routes, search, traffic, clustering)
- Standardized loading states with FIFA design system
- Unified error handling and fallback states
- Consistent map options and styling
- Pluggable marker rendering system

**Usage Example:**

```typescript
<BaseMapComponent
  features={['markers', 'search', 'traffic']}
  mode="route-planning"
  onPlaceSelect={handlePlaceSelection}
  customMarkerRenderer={routeWaypointRenderer}
>
  <RouteLayer showTrafficOnPolylines />
  <SearchOverlay />
</BaseMapComponent>
```

### EnhancedMapProvider

Extended context provider that adds venue search capabilities to the existing MapProvider.

```typescript
// app/components/maps/EnhancedMapProvider.tsx
interface EnhancedMapContextValue extends MapContextValue {
  // Venue search state
  searchQuery: string;
  searchResults: PlaceSearchResult[];
  isSearching: boolean;
  searchHistory: string[];

  // Place management
  availablePlaces: VisitedPlace[];
  filteredPlaces: VisitedPlace[];
  selectedCategory: string;

  // Actions
  searchPlaces: (query: string) => Promise<PlaceSearchResult[]>;
  filterPlaces: (category: string, query: string) => void;
  addPlace: (place: VisitedPlace) => void;
  removePlace: (placeId: string) => void;
}
```

**Integration Pattern:**

```typescript
<EnhancedMapProvider features={['markers', 'search', 'routes']}>
  <BaseMapComponent mode="route-planning">
    {/* Route planning specific components */}
  </BaseMapComponent>
</EnhancedMapProvider>
```

## Shared Hooks

### useUnifiedPlaceManagement

Provides context-aware place operations that adapt behavior based on the current mode.

```typescript
// app/hooks/useUnifiedPlaceManagement.ts
interface UnifiedPlaceManagementOptions {
  mode: 'route-planning' | 'venue-discovery';
  enableWaypoints?: boolean;
  enableSearch?: boolean;
  maxPlaces?: number;
  enableReordering?: boolean;
}

export function useUnifiedPlaceManagement(options: UnifiedPlaceManagementOptions) {
  const { mode, enableWaypoints, enableSearch, maxPlaces } = options;

  // Context-aware place operations
  const addPlace = useCallback((place: VisitedPlace) => {
    if (mode === 'route-planning') {
      // Add as waypoint with route validation
      return addWaypoint(place);
    } else {
      // Add to venue collection
      return addToCollection(place);
    }
  }, [mode]);

  return {
    state: {
      places,
      selectedPlace,
      canAddMore: places.length < maxPlaces,
      isReorderingEnabled: enableReordering && mode === 'route-planning'
    },
    actions: {
      addPlace,
      removePlace,
      reorderPlaces,
      selectPlace,
      clearPlaces
    },
    validation: {
      canAddPlace,
      validatePlaceLimit,
      checkDuplicates
    }
  };
}
```

**Usage Examples:**

Route Planning:

```typescript
const placeManagement = useUnifiedPlaceManagement({
  mode: 'route-planning',
  enableWaypoints: true,
  enableReordering: true,
  maxPlaces: 25
});
```

Venue Discovery:

```typescript
const placeManagement = useUnifiedPlaceManagement({
  mode: 'venue-discovery',
  enableSearch: true,
  maxPlaces: 100
});
```

### useEnhancedPlaceSearch

Unified search hook that combines local data search with Google Places API.

```typescript
// app/hooks/useEnhancedPlaceSearch.ts
interface EnhancedSearchOptions {
  searchMode: 'local' | 'google' | 'hybrid';
  enableCategoryFilter: boolean;
  enableLocationBias: boolean;
  cacheResults: boolean;
  debounceMs?: number;
}

interface SearchResult {
  source: 'local' | 'google';
  place: VisitedPlace;
  relevanceScore: number;
  distance?: number;
}

export function useEnhancedPlaceSearch(
  regions: CityRegion[],
  options: EnhancedSearchOptions
) {
  // Hybrid search implementation
  const hybridSearch = useCallback(async (query: string) => {
    const [localResults, googleResults] = await Promise.all([
      searchLocal(query),
      searchGoogle(query)
    ]);

    return mergeAndRankResults(localResults, googleResults);
  }, []);

  return {
    searchState: {
      query,
      results,
      isSearching,
      selectedCategory,
      searchHistory
    },
    searchActions: {
      search: hybridSearch,
      clearResults,
      setCategory,
      addToHistory
    }
  };
}
```

### useMapInteractions

Standardized map event handling with mobile gesture support.

```typescript
// app/hooks/useMapInteractions.ts
interface MapInteractionConfig {
  enableGestures: boolean;
  enableSelection: boolean;
  enableContextMenu: boolean;
  onPlaceSelect?: (place: VisitedPlace) => void;
  onMapClick?: (event: google.maps.MapMouseEvent) => void;
}

export function useMapInteractions(config: MapInteractionConfig) {
  // Gesture recognition for mobile
  const { gestureRef, gestureState } = useGestures({
    onSwipe: handleSwipe,
    onPinch: handlePinch,
    onTap: handleTap
  });

  // Standardized event handlers
  const handleMapClick = useCallback((event: google.maps.MapMouseEvent) => {
    if (gestureState.isActive) return; // Ignore during gestures

    config.onMapClick?.(event);
  }, [config.onMapClick, gestureState]);

  return {
    gestureRef,
    eventHandlers: {
      onMapClick: handleMapClick,
      onMapDrag: handleMapDrag,
      onZoomChanged: handleZoomChanged
    },
    gestureState
  };
}
```

## Reusable UI Components

### UnifiedPlaceCard

Configurable place display component that adapts to different contexts.

```typescript
// app/components/shared/UnifiedPlaceCard.tsx
interface UnifiedPlaceCardProps {
  place: VisitedPlace;
  variant: 'search-result' | 'waypoint' | 'venue-detail';
  actions: PlaceCardAction[];
  showDetails?: boolean;
  index?: number;
  className?: string;
}

interface PlaceCardAction {
  type: 'add' | 'remove' | 'reorder' | 'favorite' | 'navigate';
  label: string;
  icon: React.ComponentType;
  onClick: (place: VisitedPlace) => void;
  disabled?: boolean;
}
```

**Variant Behaviors:**

- **search-result**: Shows add button, rating, distance
- **waypoint**: Shows reorder handles, remove button, route index
- **venue-detail**: Shows full details, favorite button, navigation

### EnhancedSearchInterface

Unified search UI that adapts to different search modes.

```typescript
// app/components/shared/EnhancedSearchInterface.tsx
interface EnhancedSearchInterfaceProps {
  searchMode: 'route-planning' | 'venue-discovery';
  onPlaceSelect: (place: VisitedPlace) => void;
  enableFilters?: boolean;
  enableHistory?: boolean;
  placeholder?: string;
  className?: string;
}
```

**Features:**

- Debounced search input with FIFA design system styling
- Category filtering with visual indicators
- Search history with quick access
- Results display with relevance scoring
- Loading states and error handling

### PlaceManagementPanel

Configurable place list management with different modes.

```typescript
// app/components/shared/PlaceManagementPanel.tsx
interface PlaceManagementPanelProps {
  mode: 'waypoints' | 'favorites' | 'search-results';
  places: VisitedPlace[];
  enableReordering?: boolean;
  enableGrouping?: boolean;
  maxHeight?: string;
  onPlaceAction: (action: PlaceAction, place: VisitedPlace) => void;
}
```

**Mode Behaviors:**

- **waypoints**: Drag-and-drop reordering, route optimization
- **favorites**: Grouping by category, bulk operations
- **search-results**: Relevance sorting, quick add actions

## Integration Examples

### Route Planning Implementation

```typescript
// app/components/route-planner/EnhancedRouteMap.tsx
export function EnhancedRouteMap({ regions }: { regions: CityRegion[] }) {
  const placeManagement = useUnifiedPlaceManagement({
    mode: 'route-planning',
    enableWaypoints: true,
    enableReordering: true,
    maxPlaces: 25
  });

  const search = useEnhancedPlaceSearch(regions, {
    searchMode: 'hybrid',
    enableCategoryFilter: true,
    enableLocationBias: true,
    cacheResults: true
  });

  return (
    <EnhancedMapProvider features={['markers', 'routes', 'search', 'traffic']}>
      <BaseMapComponent
        mode="route-planning"
        onPlaceSelect={placeManagement.actions.addPlace}
        customMarkerRenderer={routeWaypointRenderer}
      >
        <RouteLayer showTrafficOnPolylines />
        <SearchOverlay />
      </BaseMapComponent>

      <PlaceManagementPanel
        mode="waypoints"
        places={placeManagement.state.places}
        enableReordering
        onPlaceAction={handleWaypointAction}
      />
    </EnhancedMapProvider>
  );
}
```

### Venue Discovery Implementation

```typescript
// app/components/venue-discovery/VenueExplorerMap.tsx
export function VenueExplorerMap({ regions }: { regions: CityRegion[] }) {
  const placeManagement = useUnifiedPlaceManagement({
    mode: 'venue-discovery',
    enableSearch: true,
    maxPlaces: 100
  });

  const search = useEnhancedPlaceSearch(regions, {
    searchMode: 'google',
    enableCategoryFilter: true,
    enableLocationBias: true,
    cacheResults: true
  });

  return (
    <EnhancedMapProvider features={['markers', 'search', 'clustering']}>
      <BaseMapComponent
        mode="venue-discovery"
        onPlaceSelect={placeManagement.actions.selectPlace}
        customMarkerRenderer={venueMarkerRenderer}
      >
        <VenueClusterLayer />
        <VenueDetailsOverlay />
      </BaseMapComponent>

      <EnhancedSearchInterface
        searchMode="venue-discovery"
        onPlaceSelect={placeManagement.actions.addPlace}
        enableFilters
        enableHistory
      />
    </EnhancedMapProvider>
  );
}
```

## Migration Guide

### Phase 1: Extract BaseMapComponent

1. **Create BaseMapComponent**:

   ```bash
   # Create the base component
   touch app/components/maps/BaseMapComponent.tsx
   ```

2. **Migrate RouteMap**:

   ```typescript
   // Before
   <GoogleMapComponent>
     <MarkerLayer />
     <RouteLayer />
   </GoogleMapComponent>

   // After
   <BaseMapComponent features={['markers', 'routes']}>
     <RouteLayer />
   </BaseMapComponent>
   ```

### Phase 2: Implement Shared Hooks

1. **Create useUnifiedPlaceManagement**:

   ```typescript
   // Replace existing waypoint management
   const { addWaypoint, removeWaypoint } = useWaypointManagement();

   // With unified management
   const { actions: { addPlace, removePlace } } = useUnifiedPlaceManagement({
     mode: 'route-planning'
   });
   ```

2. **Enhance Search Functionality**:

   ```typescript
   // Combine local and Google search
   const search = useEnhancedPlaceSearch(regions, {
     searchMode: 'hybrid',
     enableCategoryFilter: true
   });
   ```

### Phase 3: Create Reusable Components

1. **Replace PlacesList with UnifiedPlaceCard**:

   ```typescript
   // Before
   <PlacesList places={places} onAddPlace={addPlace} />

   // After
   {places.map(place => (
     <UnifiedPlaceCard
       key={place.id}
       place={place}
       variant="search-result"
       actions={[{ type: 'add', onClick: addPlace }]}
     />
   ))}
   ```

### Phase 4: Testing Strategy

1. **Unit Tests**: Test shared hooks in isolation
2. **Integration Tests**: Test component interactions
3. **E2E Tests**: Test complete user workflows

```typescript
// Example test for useUnifiedPlaceManagement
describe('useUnifiedPlaceManagement', () => {
  it('should add waypoints in route-planning mode', () => {
    const { result } = renderHook(() =>
      useUnifiedPlaceManagement({ mode: 'route-planning' })
    );

    act(() => {
      result.current.actions.addPlace(mockPlace);
    });

    expect(result.current.state.places).toContain(mockPlace);
  });
});
```

## Performance Considerations

### Code Splitting

```typescript
// Lazy load venue discovery components
const VenueExplorerMap = lazy(() => import('./VenueExplorerMap'));

// Use React.Suspense for loading states
<Suspense fallback={<MapLoadingSkeleton />}>
  <VenueExplorerMap />
</Suspense>
```

### Memoization

```typescript
// Memoize expensive computations
const filteredPlaces = useMemo(() =>
  filterPlacesByCategory(places, selectedCategory),
  [places, selectedCategory]
);

// Memoize event handlers
const handlePlaceSelect = useCallback((place: VisitedPlace) => {
  // Handle selection
}, [dependencies]);
```

### Caching Strategy

```typescript
// Implement smart caching for search results
const searchCache = new Map<string, SearchResult[]>();

const cachedSearch = useCallback(async (query: string) => {
  const cacheKey = `${query}_${selectedCategory}`;

  if (searchCache.has(cacheKey)) {
    return searchCache.get(cacheKey);
  }

  const results = await performSearch(query);
  searchCache.set(cacheKey, results);

  return results;
}, [selectedCategory]);
```

## Best Practices

### 1. Type Safety

- Use strict TypeScript interfaces
- Leverage discriminated unions for variant props
- Implement proper error boundaries

### 2. Accessibility

- Ensure keyboard navigation support
- Implement proper ARIA labels
- Provide screen reader announcements

### 3. Mobile Optimization

- Use touch-friendly hit targets (44px minimum)
- Implement proper gesture handling
- Optimize for various screen sizes

### 4. Error Handling

- Implement graceful degradation
- Provide meaningful error messages
- Use error boundaries for component isolation

This guide provides the foundation for implementing shared abstractions while maintaining the distinct user experiences of route planning and venue discovery components.
