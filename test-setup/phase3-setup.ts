/**
 * Phase 3 Test Setup
 *
 * Comprehensive test setup for Phase 3 map abstractions testing.
 * Includes proper mocking for all dependencies and TypeScript path resolution.
 */

import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Global test environment setup
Object.defineProperty(window, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntries: vi.fn(() => []),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
    clearMarks: vi.fn(),
    clearMeasures: vi.fn(),
  },
});

// Mock Google Maps API
Object.defineProperty(window, 'google', {
  value: {
    maps: {
      Map: vi.fn(),
      Marker: vi.fn(),
      InfoWindow: vi.fn(),
      LatLng: vi.fn(),
      places: {
        PlacesService: vi.fn(),
        AutocompleteService: vi.fn(),
        PlacesServiceStatus: {
          OK: 'OK',
          ZERO_RESULTS: 'ZERO_RESULTS',
          OVER_QUERY_LIMIT: 'OVER_QUERY_LIMIT',
          REQUEST_DENIED: 'REQUEST_DENIED',
          INVALID_REQUEST: 'INVALID_REQUEST',
          NOT_FOUND: 'NOT_FOUND',
        },
      },
      Animation: {
        BOUNCE: 1,
        DROP: 2,
      },
    },
  },
});

// Mock React components that use JSX
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
    Profiler: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock Framer Motion
vi.mock('framer-motion', () => ({
  motion: {
    div: 'div',
    span: 'span',
  },
  AnimatePresence: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  Loader2: () => 'Loader2',
  AlertTriangle: () => 'AlertTriangle',
  RefreshCw: () => 'RefreshCw',
  MapPin: () => 'MapPin',
  Route: () => 'Route',
  Search: () => 'Search',
  Star: () => 'Star',
  Target: () => 'Target',
  ChevronDown: () => 'ChevronDown',
  ChevronUp: () => 'ChevronUp',
  X: () => 'X',
  Plus: () => 'Plus',
  Minus: () => 'Minus',
}));

// Mock UI components
vi.mock('~/components/ui/skeleton', () => ({
  Skeleton: ({ className }: { className?: string }) => `<div class="${className}">Skeleton</div>`,
}));

vi.mock('~/components/ui/alert', () => ({
  Alert: ({ children }: { children: React.ReactNode }) => children,
  AlertDescription: ({ children }: { children: React.ReactNode }) => children,
}));

vi.mock('~/components/ui/button', () => ({
  Button: ({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) =>
    `<button onclick="${onClick}">${children}</button>`,
}));

vi.mock('~/components/ui/card', () => ({
  Card: ({ children }: { children: React.ReactNode }) => children,
  CardContent: ({ children }: { children: React.ReactNode }) => children,
  CardHeader: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock react-error-boundary
vi.mock('react-error-boundary', () => ({
  ErrorBoundary: ({ children, FallbackComponent, onError }: {
    children: React.ReactNode;
    FallbackComponent?: any;
    onError?: (error: Error) => void;
  }) => children,
}));

// Mock notification system
vi.mock('~/components/wanderlust/NotificationSystem', () => ({
  showSuccess: vi.fn(),
  showError: vi.fn(),
  showWarning: vi.fn(),
  showInfo: vi.fn(),
}));

// Mock stores and contexts - these will be overridden in individual tests
vi.mock('~/stores/wanderlust', () => ({
  useWanderlustStore: vi.fn(() => ({
    itinerary: [],
    addToItinerary: vi.fn(),
    removeFromItinerary: vi.fn(),
    reorderItinerary: vi.fn(),
    clearItinerary: vi.fn(),
    selectedPlace: null,
    setSelectedPlace: vi.fn(),
    currentRoute: null,
    setCurrentRoute: vi.fn(),
  })),
}));

vi.mock('~/components/maps/EnhancedMapProvider', () => ({
  useEnhancedMapContext: vi.fn(() => ({
    places: [],
    addPlace: vi.fn(),
    removePlace: vi.fn(),
    selectedPlace: null,
    setSelectedPlace: vi.fn(),
    setInteracting: vi.fn(),
    isMapReady: true,
    getMap: vi.fn(() => ({
      getZoom: vi.fn(() => 14),
      setZoom: vi.fn(),
      panTo: vi.fn(),
    })),
  })),
}));

// Mock hooks - these will be the default mocks that can be overridden in individual tests
vi.mock('~/hooks/useGooglePlaces', () => ({
  useGooglePlaces: vi.fn(() => ({
    searchPlaces: vi.fn().mockResolvedValue([]),
    isSearching: false,
    clearSearchResults: vi.fn(),
    getPlaceDetails: vi.fn(),
    getAutocompleteSuggestions: vi.fn(),
  })),
}));

vi.mock('~/hooks/usePlaceSearch', () => ({
  usePlaceSearch: vi.fn(() => ({
    setSearchQuery: vi.fn(),
    selectedCategory: 'all',
    setSelectedCategory: vi.fn(),
    filteredPlaces: [],
    categories: ['all', 'food', 'landmark'],
    allPlaces: [],
    getCategoryIcon: vi.fn(() => '📍'),
    resetFilters: vi.fn(),
  })),
}));

vi.mock('~/hooks/useGestures', () => ({
  useGestures: vi.fn(() => ({
    ref: { current: null },
    gestureState: {
      isActive: false,
      isDragging: false,
      isLongPressing: false,
      lastTapTime: 0,
    },
  })),
}));

// Mock the actual hook implementations that are being tested
vi.mock('~/hooks/useEnhancedPlaceSearch', async () => {
  const actual = await vi.importActual('~/hooks/useEnhancedPlaceSearch');
  return actual;
});

vi.mock('~/hooks/useUnifiedPlaceManagement', async () => {
  const actual = await vi.importActual('~/hooks/useUnifiedPlaceManagement');
  return actual;
});

vi.mock('~/hooks/useMapInteractions', async () => {
  const actual = await vi.importActual('~/hooks/useMapInteractions');
  return actual;
});

// Mock map features
vi.mock('~/components/maps/MapFeatures/MarkerLayer', () => ({
  MarkerLayer: () => 'MarkerLayer',
}));

vi.mock('~/components/maps/MapFeatures/RouteLayer', () => ({
  RouteLayer: () => 'RouteLayer',
}));

vi.mock('~/components/maps/MapFeatures/SearchOverlay', () => ({
  SearchOverlay: () => 'SearchOverlay',
}));

// Mock route planner components
vi.mock('~/components/route-planner/EnhancedRouteMap', () => ({
  EnhancedRouteMap: () => 'EnhancedRouteMap',
}));

vi.mock('~/components/route-planner/EnhancedSmartRouteCard', () => ({
  EnhancedSmartRouteCard: () => 'EnhancedSmartRouteCard',
}));

vi.mock('~/components/route-planner/RouteAlternatives', () => ({
  RouteAlternatives: () => 'RouteAlternatives',
}));

vi.mock('~/components/route-planner/TurnByTurnDirections', () => ({
  TurnByTurnDirections: () => 'TurnByTurnDirections',
}));

vi.mock('~/components/route-planner/WaypointPanel', () => ({
  WaypointPanel: () => 'WaypointPanel',
}));

vi.mock('~/components/route-planner/RouteVisualizationTest', () => ({
  RouteVisualizationTest: () => 'RouteVisualizationTest',
}));

// Mock venue discovery components
vi.mock('~/components/venue-discovery/VenueExplorerMap', () => ({
  VenueExplorerMap: () => 'VenueExplorerMap',
}));

// Mock base map components
vi.mock('~/components/maps/BaseMapComponent', () => ({
  BaseMapComponent: () => 'BaseMapComponent',
}));

vi.mock('~/components/maps/EnhancedMapProvider', () => ({
  EnhancedMapProvider: ({ children }: { children: React.ReactNode }) => children,
  useEnhancedMapContext: vi.fn(() => ({
    places: [],
    addPlace: vi.fn(),
    removePlace: vi.fn(),
    selectedPlace: null,
    setSelectedPlace: vi.fn(),
    setInteracting: vi.fn(),
    isMapReady: true,
    getMap: vi.fn(() => ({
      getZoom: vi.fn(() => 14),
      setZoom: vi.fn(),
      panTo: vi.fn(),
    })),
  })),
}));

// Global test utilities
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock fetch for API calls
global.fetch = vi.fn();

// Console methods for testing
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeEach(() => {
  vi.clearAllMocks();
  // Suppress console errors/warnings in tests unless explicitly needed
  console.error = vi.fn();
  console.warn = vi.fn();
});

afterEach(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Export test utilities
export const testUtils = {
  mockGoogleMapsApi: () => window.google,
  mockPerformance: () => window.performance,
  createMockMapEvent: (lat: number = 40.7128, lng: number = -74.0060) => ({
    latLng: {
      lat: () => lat,
      lng: () => lng,
    },
    domEvent: new MouseEvent('click'),
    stop: vi.fn(),
  }),
};
