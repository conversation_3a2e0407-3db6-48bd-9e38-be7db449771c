/**
 * Test Setup for Routes API v2 Migration
 * 
 * Configures the testing environment with necessary mocks,
 * polyfills, and global setup for Routes V2 testing.
 */

import { vi, beforeAll, afterAll, beforeEach, afterEach, expect } from 'vitest';
import { default as userEvent } from '@testing-library/user-event';
import '@testing-library/jest-dom';

// Mock Google Maps API
const mockGoogleMaps = {
  maps: {
    Map: vi.fn().mockImplementation(() => ({
      setCenter: vi.fn(),
      setZoom: vi.fn(),
      fitBounds: vi.fn(),
      addListener: vi.fn(),
      removeListener: vi.fn(),
    })),
    Marker: vi.fn().mockImplementation(() => ({
      setMap: vi.fn(),
      setPosition: vi.fn(),
      setTitle: vi.fn(),
      addListener: vi.fn(),
    })),
    InfoWindow: vi.fn().mockImplementation(() => ({
      setContent: vi.fn(),
      open: vi.fn(),
      close: vi.fn(),
    })),
    Polyline: vi.fn().mockImplementation(() => ({
      setMap: vi.fn(),
      setPath: vi.fn(),
      setOptions: vi.fn(),
      addListener: vi.fn(),
    })),
    DirectionsService: vi.fn().mockImplementation(() => ({
      route: vi.fn(),
    })),
    DirectionsRenderer: vi.fn().mockImplementation(() => ({
      setMap: vi.fn(),
      setDirections: vi.fn(),
      setOptions: vi.fn(),
    })),
    geometry: {
      encoding: {
        decodePath: vi.fn().mockReturnValue([
          { lat: () => 36.1627, lng: () => -86.7816 },
          { lat: () => 36.1263, lng: () => -86.6782 },
        ]),
      },
    },
    LatLng: vi.fn().mockImplementation((lat, lng) => ({
      lat: () => lat,
      lng: () => lng,
    })),
    LatLngBounds: vi.fn().mockImplementation(() => ({
      extend: vi.fn(),
      contains: vi.fn(),
      getNorthEast: vi.fn(),
      getSouthWest: vi.fn(),
    })),
    Size: vi.fn(),
    Point: vi.fn(),
    SymbolPath: {
      FORWARD_CLOSED_ARROW: 1,
      FORWARD_OPEN_ARROW: 2,
      BACKWARD_CLOSED_ARROW: 3,
      BACKWARD_OPEN_ARROW: 4,
      CIRCLE: 0,
    },
    TravelMode: {
      DRIVING: 'DRIVING',
      WALKING: 'WALKING',
      BICYCLING: 'BICYCLING',
      TRANSIT: 'TRANSIT',
    },
    DirectionsStatus: {
      OK: 'OK',
      NOT_FOUND: 'NOT_FOUND',
      ZERO_RESULTS: 'ZERO_RESULTS',
      MAX_WAYPOINTS_EXCEEDED: 'MAX_WAYPOINTS_EXCEEDED',
      INVALID_REQUEST: 'INVALID_REQUEST',
      OVER_QUERY_LIMIT: 'OVER_QUERY_LIMIT',
      REQUEST_DENIED: 'REQUEST_DENIED',
      UNKNOWN_ERROR: 'UNKNOWN_ERROR',
    },
  },
};

// Mock fetch for API calls
const mockFetch = vi.fn();

// Mock performance API
const mockPerformance = {
  now: vi.fn(() => Date.now()),
  mark: vi.fn(),
  measure: vi.fn(),
  memory: {
    usedJSHeapSize: 10000000,
    totalJSHeapSize: 20000000,
    jsHeapSizeLimit: *********,
  },
};

// Mock console methods for cleaner test output
const mockConsole = {
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
};

// Mock ResizeObserver
const mockResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
const mockIntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock matchMedia
const mockMatchMedia = vi.fn().mockImplementation((query) => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: vi.fn(),
  removeListener: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};

// Mock sessionStorage
const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};

// Setup global mocks
beforeAll(() => {
  (global as any).google = {
    maps: {
      DirectionsService: vi.fn().mockImplementation(() => ({
        route: vi.fn((request, callback) => {
          callback(
            {
              routes: [
                {
                  legs: [
                    {
                      distance: { text: '1 km', value: 1000 },
                      duration: { text: '1 min', value: 60 },
                      start_address: 'Mock Start Address',
                      end_address: 'Mock End Address',
                      start_location: { lat: () => 0, lng: () => 0 },
                      end_location: { lat: () => 0, lng: () => 0 },
                      steps: [],
                    },
                  ],
                  overview_polyline: { points: '' },
                  bounds: {
                    getNorthEast: () => ({ lat: () => 0, lng: () => 0 }),
                    getSouthWest: () => ({ lat: () => 0, lng: () => 0 }),
                  },
                  warnings: [],
                  copyrights: 'Mock Copyrights',
                },
              ],
              status: 'OK',
            },
            'OK'
          );
        }),
      })),
      DirectionsRenderer: vi.fn().mockImplementation(() => ({
        setMap: vi.fn(),
        setDirections: vi.fn(),
      })),
      LatLng: vi.fn().mockImplementation((lat, lng) => ({
        lat: () => lat,
        lng: () => lng,
      })),
      TravelMode: {
        DRIVING: 'DRIVING',
      },
      UnitSystem: {
        METRIC: 0,
      },
      DirectionsStatus: {
        OK: 'OK',
      },
    },
  };
  // Global objects
  global.fetch = mockFetch;
  global.performance = mockPerformance as any;
  global.ResizeObserver = mockResizeObserver;
  global.IntersectionObserver = mockIntersectionObserver;
  
  // Window objects
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: mockMatchMedia,
  });
  
  Object.defineProperty(window, 'localStorage', {
    writable: true,
    value: mockLocalStorage,
  });
  
  Object.defineProperty(window, 'sessionStorage', {
    writable: true,
    value: mockSessionStorage,
  });

  // Mock console for cleaner test output
  Object.assign(console, mockConsole);

  // Mock environment variables
  Object.assign(import.meta.env, {
    VITE_USE_ROUTES_V2: 'true',
    VITE_ROUTES_V2_PERCENTAGE: '100',
    VITE_ENABLE_ROUTES_FALLBACK: 'true',
    VITE_ROUTES_DEBUG: 'true',
    VITE_GOOGLE_MAPS_API_KEY: 'test-api-key',
  });

  console.log('🧪 Routes V2 test environment initialized');
});

// Reset mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
  
  // Reset fetch mock to default successful response
  mockFetch.mockResolvedValue({
    ok: true,
    status: 200,
    json: () => Promise.resolve({
      routes: [{
        legs: [{
          distanceMeters: 15000,
          duration: '1200s',
          staticDuration: '1200s',
          polyline: { encodedPolyline: 'mock_polyline_data' },
          startLocation: { latLng: { latitude: 36.1627, longitude: -86.7816 } },
          endLocation: { latLng: { latitude: 36.1263, longitude: -86.6782 } },
          steps: [],
          localizedValues: {
            distance: { text: '15.0 km' },
            duration: { text: '20 min' },
          },
        }],
        distanceMeters: 15000,
        duration: '1200s',
        staticDuration: '1200s',
        polyline: { encodedPolyline: 'mock_polyline_data' },
        description: 'Test Route',
        warnings: [],
        viewport: {
          low: { latitude: 36.1, longitude: -86.8 },
          high: { latitude: 36.2, longitude: -86.7 },
        },
        travelAdvisory: {},
        localizedValues: {
          distance: { text: '15.0 km' },
          duration: { text: '20 min' },
        },
      }],
    }),
  });

  // Reset performance mock
  mockPerformance.now.mockReturnValue(Date.now());

  // Reset storage mocks
  mockLocalStorage.getItem.mockReturnValue(null);
  mockSessionStorage.getItem.mockReturnValue(null);
});

// Cleanup after each test
afterEach(() => {
  // Clear any timers
  vi.clearAllTimers();
  
  // Clear any intervals
  vi.useRealTimers();
});

// Cleanup after all tests
afterAll(() => {
  console.log('🧹 Routes V2 test environment cleaned up');
});

// Export test utilities
export const testUtils = {
  mockGoogleMaps,
  mockFetch,
  mockPerformance,
  mockConsole,
  mockLocalStorage,
  mockSessionStorage,
  
  setup: () => {
   const user = userEvent.setup();
   return user;
 },

  // Helper functions
  createMockRoute: (overrides = {}) => ({
    id: 'test-route-1',
    name: 'Test Route',
    waypoints: [],
    optimized: false,
    travelMode: 'DRIVING' as const,
    estimatedDuration: '25 min',
    estimatedDistance: '15.2 km',
    polyline: 'mock_polyline',
    legs: [],
    warnings: [],
    copyrights: 'Map data ©2024 Google',
    createdAt: new Date().toISOString(),
    lastOptimized: new Date().toISOString(),
    ...overrides,
  }),
  
  createMockWaypoints: (count = 2) => Array(count).fill(null).map((_, index) => ({
    location: { 
      lat: 36.1627 + (index * 0.01), 
      lng: -86.7816 + (index * 0.01) 
    },
    stopover: index > 0 && index < count - 1,
    placeId: `waypoint-${index}`,
  })),
  
  createMockRoutesV2Response: (routeCount = 1) => ({
    routes: Array(routeCount).fill(null).map((_, index) => ({
      legs: [{
        distanceMeters: 15000 + index * 1000,
        duration: `${1200 + index * 300}s`,
        staticDuration: `${1200 + index * 300}s`,
        polyline: { encodedPolyline: `mock_polyline_${index}` },
        startLocation: { latLng: { latitude: 36.1627, longitude: -86.7816 } },
        endLocation: { latLng: { latitude: 36.1263, longitude: -86.6782 } },
        steps: [],
        localizedValues: {
          distance: { text: `${15 + index} km` },
          duration: { text: `${20 + index * 5} min` },
        },
      }],
      distanceMeters: 15000 + index * 1000,
      duration: `${1200 + index * 300}s`,
      staticDuration: `${1200 + index * 300}s`,
      polyline: { encodedPolyline: `mock_polyline_${index}` },
      description: `Route ${index + 1}`,
      warnings: [],
      viewport: {
        low: { latitude: 36.1, longitude: -86.8 },
        high: { latitude: 36.2, longitude: -86.7 },
      },
      travelAdvisory: {},
      localizedValues: {
        distance: { text: `${15 + index} km` },
        duration: { text: `${20 + index * 5} min` },
      },
    })),
  }),
  
  waitForAsync: (ms = 0) => new Promise(resolve => setTimeout(resolve, ms)),
  
  expectNoConsoleErrors: () => {
    expect(mockConsole.error).not.toHaveBeenCalled();
  },
  
  expectPerformanceWithinThreshold: (startTime: number, threshold: number) => {
    const endTime = mockPerformance.now();
    const duration = endTime - startTime;
    expect(duration).toBeLessThan(threshold);
  },
};
