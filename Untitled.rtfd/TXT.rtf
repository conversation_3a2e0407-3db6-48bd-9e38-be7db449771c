{\rtf1\ansi\ansicpg1252\cocoartf2822
\cocoatextscaling0\cocoaplatform0{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
{\*\expandedcolortbl;;}
\paperw11900\paperh16840\margl1440\margr1440\vieww11520\viewh8400\viewkind0
\pard\tx566\tx1133\tx1700\tx2267\tx2834\tx3401\tx3968\tx4535\tx5102\tx5669\tx6236\tx6803\pardirnatural\partightenfactor0

\f0\fs24 \cf0 export interface ItineraryData \{\
  date: string;\
  title: string;\
  summary: string;\
  weather: \{\
    forecast: string;\
    temperature: string;\
    wind: string;\
    icon: string;\
  \};\
  reminders: string[];\
  docs: string[];\
  items: Activity[];\
\}\
\
export interface Activity \{\
  id: string;\
  type: 'transport' | 'match' | 'meal' | 'hotel'| 'activity';\
  title: string;\
  time: string;\
  duration?: string;\
  location?: \{\
    name: string;\
    lat: number;\
    lng: number;\
    address?: string;\
    contact?: string;\
    confirmationNumber?: string;\
  \};\
  transport?: \{\
    mode: string;\
    carrier?: string;\
    bookingReference?: string;\
    seatMap?: \{ [key: string]: string \};\
    pickup_time?: string;\
    pickup_location?: string;\
    estimated_cost?: number;\
    shared_with?: string[];\
    notes?: string;\
  \};\
  notes?: string;\
  attachments?: string[];\
  status: 'pending' | 'confirmed' | 'completed';\
  important?: boolean;\
  requiresConfirmation?: boolean;\
  isGroupEvent?: boolean;\
\}\
}