/**
 * End-to-End Integration Tests for Routes API v2
 * 
 * Tests complete user workflows from waypoint addition through
 * route visualization, covering the entire Routes V2 experience.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import * as userEvent from '@testing-library/user-event';
import { EnhancedSmartRouteCard } from '~/components/route-planner/EnhancedSmartRouteCard';
import { RoutesV2Test } from '~/components/route-planner/RoutesV2Test';
import { testUtils } from 'test-setup/routes-v2-setup';
import type { CityRegion } from '~/types/wanderlust';

const { setup } = testUtils;

// Mock Google Maps API
const mockGoogleMaps = {
  geometry: {
    encoding: {
      decodePath: vi.fn().mockReturnValue([
        { lat: () => 36.1627, lng: () => -86.7816 },
        { lat: () => 36.1263, lng: () => -86.6782 },
      ]),
    },
  },
  DirectionsService: vi.fn().mockImplementation(() => ({
    route: vi.fn().mockImplementation((request, callback) => {
      setTimeout(() => {
        callback({
          routes: [{
            legs: [{
              distance: { text: '15.2 km', value: 15200 },
              duration: { text: '25 min', value: 1500 },
              start_address: 'Nashville, TN',
              end_address: 'Nashville Airport, TN',
              start_location: { lat: () => 36.1627, lng: () => -86.7816 },
              end_location: { lat: () => 36.1263, lng: () => -86.6782 },
              steps: [],
            }],
            overview_polyline: { points: 'mock_polyline_data' },
            bounds: {
              getNorthEast: () => ({ lat: () => 36.2, lng: () => -86.7 }),
              getSouthWest: () => ({ lat: () => 36.1, lng: () => -86.8 }),
            },
            warnings: [],
            copyrights: 'Map data ©2024 Google',
          }],
          status: 'OK',
        }, 'OK');
      }, 100);
    }),
  })),
};

// Mock environment variables for different test scenarios
const mockEnvironments = {
  routesV2Enabled: {
    VITE_USE_ROUTES_V2: 'true',
    VITE_ROUTES_V2_PERCENTAGE: '100',
    VITE_ENABLE_ROUTES_FALLBACK: 'true',
    VITE_ROUTES_DEBUG: 'true',
    VITE_GOOGLE_MAPS_API_KEY: 'test-api-key',
  },
  routesV2Disabled: {
    VITE_USE_ROUTES_V2: 'false',
    VITE_ROUTES_V2_PERCENTAGE: '0',
    VITE_ENABLE_ROUTES_FALLBACK: 'true',
    VITE_ROUTES_DEBUG: 'false',
    VITE_GOOGLE_MAPS_API_KEY: 'test-api-key',
  },
  routesV2Partial: {
    VITE_USE_ROUTES_V2: 'true',
    VITE_ROUTES_V2_PERCENTAGE: '50',
    VITE_ENABLE_ROUTES_FALLBACK: 'true',
    VITE_ROUTES_DEBUG: 'true',
    VITE_GOOGLE_MAPS_API_KEY: 'test-api-key',
  },
};

// Mock fetch for Routes V2 API
const mockRoutesV2Fetch = vi.fn().mockResolvedValue({
  ok: true,
  json: () => Promise.resolve({
    routes: [{
      legs: [{
        distanceMeters: 15200,
        duration: '1500s',
        staticDuration: '1500s',
        polyline: { encodedPolyline: 'mock_polyline_data' },
        startLocation: { latLng: { latitude: 36.1627, longitude: -86.7816 } },
        endLocation: { latLng: { latitude: 36.1263, longitude: -86.6782 } },
        steps: [],
        localizedValues: {
          distance: { text: '15.2 km' },
          duration: { text: '25 min' },
        },
      }],
      distanceMeters: 15200,
      duration: '1500s',
      staticDuration: '1500s',
      polyline: { encodedPolyline: 'mock_polyline_data' },
      description: 'Optimal Route',
      warnings: [],
      viewport: {
        low: { latitude: 36.1, longitude: -86.8 },
        high: { latitude: 36.2, longitude: -86.7 },
      },
      travelAdvisory: {
        tollInfo: {
          estimatedPrice: [{ currencyCode: 'USD', units: '4', nanos: ********* }],
        },
      },
      localizedValues: {
        distance: { text: '15.2 km' },
        duration: { text: '25 min' },
      },
    }],
  }),
});

// Mock stores and hooks
vi.mock('~/stores/wanderlust', () => ({
  useWanderlustStore: () => ({
    itinerary: [
      {
        id: 'place-1',
        name: 'Nashville Downtown',
        coordinates: { latitude: 36.1627, longitude: -86.7816 },
        category: 'landmark',
        visitedAt: new Date().toISOString(),
        notes: '',
        rating: 5,
        photos: [],
      },
      {
        id: 'place-2',
        name: 'Nashville Airport',
        coordinates: { latitude: 36.1263, longitude: -86.6782 },
        category: 'transport',
        visitedAt: new Date().toISOString(),
        notes: '',
        rating: 4,
        photos: [],
      },
    ],
    currentRoute: null,
    routePlanning: {
      isPlanning: false,
      planningError: null,
      optimizationOptions: {
        optimizeWaypointOrder: true,
        avoidTolls: false,
        avoidHighways: false,
        avoidFerries: false,
      },
    },
    addToItinerary: vi.fn(),
    removeFromItinerary: vi.fn(),
    clearItinerary: vi.fn(),
    setCurrentRoute: vi.fn(),
    clearRoute: vi.fn(),
    clearRouteAndItinerary: vi.fn(),
  }),
}));

// Mock notification system
vi.mock('~/components/wanderlust/NotificationSystem', () => ({
  showSuccess: vi.fn(),
  showError: vi.fn(),
  showInfo: vi.fn(),
}));

// Setup global mocks
beforeEach(() => {
  global.google = mockGoogleMaps as any;
  global.fetch = mockRoutesV2Fetch;
  vi.clearAllMocks();
});

describe('Routes V2 End-to-End Integration', () => {
  const user = setup();

  describe('Complete Route Planning Workflow', () => {
    beforeEach(() => {
      Object.assign(import.meta.env, mockEnvironments.routesV2Enabled);
    });

    it('should complete full route planning workflow with Routes V2', async () => {
      const mockRegions = [
        {
          id: 'nashville',
          name: 'Nashville',
          coordinates: { latitude: 36.1627, longitude: -86.7816 },
          places: [],
          description: 'Mock description',
          icon: 'mock-icon',
          bounds: {
            north: 36.2,
            south: 36.1,
            east: -86.7,
            west: -86.8,
          },
          center: { latitude: 36.15, longitude: -86.75 },
        },
      ];

      render(
        <EnhancedSmartRouteCard 
          regions={mockRegions}
          enableAdvancedFeatures={true}
          showAlternatives={true}
        />
      );

      // 1. Verify initial state
      expect(screen.getByText('Enhanced Route Planner')).toBeInTheDocument();
      expect(screen.getByText('2 waypoints added')).toBeInTheDocument();

      // 2. Expand the route card
      const expandButton = screen.getByRole('button');
      await user.click(expandButton);

      // 3. Configure route options
      await waitFor(() => {
        expect(screen.getByText('Enhanced Route Calculation')).toBeInTheDocument();
      });

      // Select traffic-aware routing
      const routingSelect = screen.getByDisplayValue('Traffic Aware');
      expect(routingSelect).toBeInTheDocument();

      // Enable alternatives
      const alternativesToggle = screen.getByLabelText('Show Alternatives');
      await user.click(alternativesToggle);

      // 4. Calculate enhanced route
      const calculateButton = screen.getByText('Calculate Enhanced Route');
      await user.click(calculateButton);

      // 5. Verify route calculation
      await waitFor(() => {
        expect(mockRoutesV2Fetch).toHaveBeenCalledWith(
          'https://routes.googleapis.com/directions/v2:computeRoutes',
          expect.objectContaining({
            method: 'POST',
            headers: expect.objectContaining({
              'Content-Type': 'application/json',
              'X-Goog-Api-Key': 'test-api-key',
            }),
          })
        );
      });

      // 6. Verify route summary appears
      await waitFor(() => {
        expect(screen.getByText('Route Summary')).toBeInTheDocument();
        expect(screen.getByText('25 min')).toBeInTheDocument();
        expect(screen.getByText('15.2 km')).toBeInTheDocument();
      });

      // 7. Check for V2 badge
      expect(screen.getByText('V2')).toBeInTheDocument();
    });

    it('should handle alternative routes workflow', async () => {
      const mockRegions: CityRegion[] = [];

      render(
        <EnhancedSmartRouteCard 
          regions={mockRegions}
          enableAdvancedFeatures={true}
          showAlternatives={true}
        />
      );

      // Expand and navigate to alternatives
      const expandButton = screen.getByRole('button');
      await user.click(expandButton);

      // Calculate route first
      await waitFor(() => {
        const calculateButton = screen.getByText('Calculate Enhanced Route');
        user.click(calculateButton);
      });

      // Wait for route calculation
      await waitFor(() => {
        expect(screen.getByText('Route Summary')).toBeInTheDocument();
      });

      // Navigate to alternatives tab
      const alternativesTab = screen.getByText('Routes');
      await user.click(alternativesTab);

      // Find alternative routes button
      const findAlternativesButton = screen.getByText('Find Alternative Routes');
      await user.click(findAlternativesButton);

      // Verify alternatives are requested
      await waitFor(() => {
        expect(mockRoutesV2Fetch).toHaveBeenCalledTimes(2); // Initial + alternatives
      });
    });

    it('should handle travel mode changes', async () => {
      const mockRegions: CityRegion[] = [];

      render(
        <EnhancedSmartRouteCard 
          regions={mockRegions}
          enableAdvancedFeatures={true}
          showAlternatives={false}
        />
      );

      const expandButton = screen.getByRole('button');
      await user.click(expandButton);

      // Change travel mode to walking
      await waitFor(() => {
        const walkingButton = screen.getByText('Walking');
        user.click(walkingButton);
      });

      // Calculate route with new mode
      const calculateButton = screen.getByText('Calculate Enhanced Route');
      await user.click(calculateButton);

      // Verify request includes walking mode
      await waitFor(() => {
        const lastCall = mockRoutesV2Fetch.mock.calls[mockRoutesV2Fetch.mock.calls.length - 1];
        const requestBody = JSON.parse(lastCall[1].body);
        expect(requestBody.travelMode).toBe('WALK');
      });
    });
  });

  describe('Fallback Behavior', () => {
    beforeEach(() => {
      Object.assign(import.meta.env, mockEnvironments.routesV2Disabled);
    });

    it('should fall back to legacy API when Routes V2 is disabled', async () => {
      const mockRegions: CityRegion[] = [];

      render(
        <EnhancedSmartRouteCard 
          regions={mockRegions}
          enableAdvancedFeatures={false}
          showAlternatives={false}
        />
      );

      const expandButton = screen.getByRole('button');
      await user.click(expandButton);

      // Should show basic calculate button instead of enhanced
      await waitFor(() => {
        expect(screen.getByText('Calculate Route')).toBeInTheDocument();
        expect(screen.queryByText('Calculate Enhanced Route')).not.toBeInTheDocument();
      });

      const calculateButton = screen.getByText('Calculate Route');
      await user.click(calculateButton);

      // Should use Google Maps DirectionsService instead of fetch
      await waitFor(() => {
        expect(mockGoogleMaps.DirectionsService).toHaveBeenCalled();
        expect(mockRoutesV2Fetch).not.toHaveBeenCalled();
      });
    });

    it('should handle API errors gracefully', async () => {
      Object.assign(import.meta.env, mockEnvironments.routesV2Enabled);
      
      // Mock API failure
      mockRoutesV2Fetch.mockRejectedValueOnce(new Error('API Error'));

      const mockRegions: CityRegion[] = [];

      render(
        <EnhancedSmartRouteCard 
          regions={mockRegions}
          enableAdvancedFeatures={true}
          showAlternatives={false}
        />
      );

      const expandButton = screen.getByRole('button');
      await user.click(expandButton);

      const calculateButton = screen.getByText('Calculate Enhanced Route');
      await user.click(calculateButton);

      // Should handle error gracefully
      await waitFor(() => {
        expect(mockRoutesV2Fetch).toHaveBeenCalled();
        // Error handling would be shown via notification system
      });
    });
  });

  describe('Testing Interface', () => {
    beforeEach(() => {
      Object.assign(import.meta.env, mockEnvironments.routesV2Enabled);
    });

    it('should provide comprehensive testing interface', async () => {
      render(<RoutesV2Test />);

      // Verify test interface elements
      expect(screen.getByText('Routes API v2 Testing Suite')).toBeInTheDocument();
      expect(screen.getByText('Test Basic Route')).toBeInTheDocument();
      expect(screen.getByText('Test Enhanced Route')).toBeInTheDocument();
      expect(screen.getByText('Test Alternatives')).toBeInTheDocument();

      // Test basic route calculation
      const basicTestButton = screen.getByText('Test Basic Route');
      await user.click(basicTestButton);

      await waitFor(() => {
        expect(mockRoutesV2Fetch).toHaveBeenCalled();
      });

      // Verify test results appear
      await waitFor(() => {
        expect(screen.getByText(/ms/)).toBeInTheDocument(); // Timing information
      });
    });

    it('should show environment configuration', () => {
      render(<RoutesV2Test />);

      // Verify environment info is displayed
      expect(screen.getByText('Routes V2: true')).toBeInTheDocument();
      expect(screen.getByText('Percentage: 100%')).toBeInTheDocument();
      expect(screen.getByText('Fallback: true')).toBeInTheDocument();
      expect(screen.getByText('Debug: true')).toBeInTheDocument();
    });

    it('should handle cache operations', async () => {
      render(<RoutesV2Test />);

      // Test cache stats
      const cacheStatsButton = screen.getByText('Update Cache Stats');
      await user.click(cacheStatsButton);

      // Test cache clearing
      const clearCacheButton = screen.getByText('Clear Cache');
      await user.click(clearCacheButton);

      // Verify cache operations work
      expect(screen.getByText('Cache: 0 entries')).toBeInTheDocument();
    });
  });

  describe('Performance Validation', () => {
    beforeEach(() => {
      Object.assign(import.meta.env, mockEnvironments.routesV2Enabled);
    });

    it('should complete route calculation within performance threshold', async () => {
      const mockRegions: CityRegion[] = [];

      render(
        <EnhancedSmartRouteCard 
          regions={mockRegions}
          enableAdvancedFeatures={true}
          showAlternatives={false}
        />
      );

      const expandButton = screen.getByRole('button');
      await user.click(expandButton);

      const startTime = performance.now();
      
      const calculateButton = screen.getByText('Calculate Enhanced Route');
      await user.click(calculateButton);

      await waitFor(() => {
        expect(screen.getByText('Route Summary')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within 5 seconds (including UI updates)
      expect(duration).toBeLessThan(5000);
    });

    it('should handle rapid user interactions', async () => {
      const mockRegions: CityRegion[] = [];

      render(
        <EnhancedSmartRouteCard 
          regions={mockRegions}
          enableAdvancedFeatures={true}
          showAlternatives={true}
        />
      );

      const expandButton = screen.getByRole('button');
      await user.click(expandButton);

      // Rapid mode changes
      const drivingButton = screen.getByText('Driving');
      const walkingButton = screen.getByText('Walking');
      const cyclingButton = screen.getByText('Cycling');

      await user.click(walkingButton);
      await user.click(cyclingButton);
      await user.click(drivingButton);

      // Should handle rapid changes without errors
      expect(screen.getByText('Driving')).toBeInTheDocument();
    });
  });

  describe('Accessibility and UX', () => {
    it('should be accessible with keyboard navigation', async () => {
      const mockRegions: CityRegion[] = [];

      render(
        <EnhancedSmartRouteCard 
          regions={mockRegions}
          enableAdvancedFeatures={true}
          showAlternatives={false}
        />
      );

      // Test keyboard navigation
      const expandButton = screen.getByRole('button');
      expandButton.focus();
      fireEvent.keyDown(expandButton, { key: 'Enter' });

      await waitFor(() => {
        expect(screen.getByText('Enhanced Route Calculation')).toBeInTheDocument();
      });

      // Test tab navigation through controls
      fireEvent.keyDown(document.activeElement!, { key: 'Tab' });
      fireEvent.keyDown(document.activeElement!, { key: 'Tab' });

      // Should be able to navigate through all interactive elements
      expect(document.activeElement).toBeInTheDocument();
    });

    it('should provide appropriate loading states', async () => {
      const mockRegions: CityRegion[] = [];

      // Mock slow API response
      mockRoutesV2Fetch.mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: () => Promise.resolve({
              routes: [{
                legs: [],
                distanceMeters: 15200,
                duration: '1500s',
                staticDuration: '1500s',
                polyline: { encodedPolyline: 'mock_polyline_data' },
                description: 'Test Route',
                warnings: [],
                viewport: { low: { latitude: 36.1, longitude: -86.8 }, high: { latitude: 36.2, longitude: -86.7 } },
                travelAdvisory: {},
                localizedValues: { distance: { text: '15.2 km' }, duration: { text: '25 min' } },
              }],
            }),
          }), 2000)
        )
      );

      render(
        <EnhancedSmartRouteCard 
          regions={mockRegions}
          enableAdvancedFeatures={true}
          showAlternatives={false}
        />
      );

      const expandButton = screen.getByRole('button');
      await user.click(expandButton);

      const calculateButton = screen.getByText('Calculate Enhanced Route');
      await user.click(calculateButton);

      // Should show loading state
      expect(screen.getByText('Calculating...')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /calculating/i })).toBeDisabled();

      // Should show spinner animation
      expect(document.querySelector('.animate-spin')).toBeInTheDocument();
    });
  });
});
