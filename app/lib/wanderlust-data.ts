import type { VisitedPlace, CityRegion } from '~/types/wanderlust';

// Raw data interface from existing visited_places.json
interface RawPlace {
  location_name: string;
  city: string;
  description: {
    en: string;
    fr: string;
    ar: string;
  };
  coordinates: {
    latitude: number;
    longitude: number;
  };
}

// Category mapping based on place names and descriptions
const categoryMapping: Record<string, VisitedPlace['category']> = {
  'airport': 'transport',
  'station': 'transport',
  'terminal': 'transport',
  'path': 'transport',
  'restaurant': 'food',
  'café': 'food',
  'diner': 'food',
  'food market': 'food',
  'bistro': 'food',
  'chicken': 'food',
  'food court': 'food',
  'park': 'park',
  'garden': 'park',
  'walkway': 'park',
  'museum': 'museum',
  'theater': 'entertainment',
  'stadium': 'entertainment',
  'broadway': 'entertainment',
  'rooftop': 'entertainment',
  'building': 'landmark',
  'cathedral': 'landmark',
  'statue': 'landmark',
  'bridge': 'landmark',
  'avenue': 'shopping',
  'shopping market': 'shopping',
  'accommodation': 'accommodation',
  'hotel': 'accommodation',
};

// Icon mapping for categories
const categoryIcons: Record<VisitedPlace['category'], string> = {
  food: '🍕',
  landmark: '🏛️',
  museum: '🎨',
  park: '🌳',
  accommodation: '🏨',
  transport: '🚇',
  entertainment: '🎭',
  shopping: '🛍️',
};

// Region definitions
const regionDefinitions: Record<string, Omit<CityRegion, 'places'>> = {
  'nyc': {
    id: 'nyc',
    name: '🏙️ Greater NYC Area',
    description: 'Urban adventures & cultural gems',
    icon: '🏙️',
    center: { latitude: 40.7589, longitude: -73.9851 },
    bounds: {
      north: 40.9176,
      south: 40.4774,
      east: -73.7004,
      west: -74.2591,
    },
  },
  'nashville': {
    id: 'nashville',
    name: '🎵 Music City Nashville',
    description: 'Live music & southern charm',
    icon: '🎵',
    center: { latitude: 36.1627, longitude: -86.7816 },
    bounds: {
      north: 36.4207,
      south: 35.9042,
      east: -86.4637,
      west: -87.0996,
    },
  },
  'newjersey': {
    id: 'newjersey',
    name: '🌉 New Jersey Area',
    description: 'Gateway to NYC & scenic waterfronts',
    icon: '🌉',
    center: { latitude: 40.7282, longitude: -74.0776 },
    bounds: {
      north: 40.9176,
      south: 40.4774,
      east: -73.9004,
      west: -74.2591,
    },
  },
  'international': {
    id: 'international',
    name: '✈️ International Destinations',
    description: 'Global travel experiences',
    icon: '✈️',
    center: { latitude: 50.9010, longitude: 4.4844 },
    bounds: {
      north: 51.2,
      south: 50.6,
      east: 4.8,
      west: 4.1,
    },
  },
};

function determineCategory(place: RawPlace): VisitedPlace['category'] {
  const searchText = `${place.location_name} ${place.description.en}`.toLowerCase();

  for (const [keyword, category] of Object.entries(categoryMapping)) {
    if (searchText.includes(keyword)) {
      return category;
    }
  }

  return 'landmark'; // default category
}

function determineRegion(place: RawPlace): string {
  const city = place.city.toLowerCase();

  if (city.includes('new york') || city.includes('manhattan') || city.includes('brooklyn') || city.includes('bronx')) {
    return 'nyc';
  }
  if (city.includes('nashville')) {
    return 'nashville';
  }
  if (city.includes('jersey') || city.includes('hoboken') || city.includes('elizabeth') || city.includes('newark')) {
    return 'newjersey';
  }

  return 'international';
}

function generateRevisitPotential(): VisitedPlace['revisitPotential'] {
  const options: VisitedPlace['revisitPotential'][] = ['Highly Recommend', 'Worth a Look', 'Skip'];
  const weights = [0.6, 0.35, 0.05]; // Bias towards positive recommendations

  const random = Math.random();
  let cumulative = 0;

  for (let i = 0; i < options.length; i++) {
    cumulative += weights[i];
    if (random <= cumulative) {
      return options[i];
    }
  }

  return 'Worth a Look';
}

function generateRating(): number {
  // Generate ratings between 3-5 with bias towards higher ratings
  return Math.floor(Math.random() * 3) + 3;
}

export function convertRawDataToVisitedPlaces(rawData: RawPlace[]): VisitedPlace[] {
  return rawData.map((place, index) => {
    const category = determineCategory(place);
    const region = determineRegion(place);

    return {
      id: `place-${index + 1}`,
      name: place.location_name,
      description: place.description,
      category,
      coordinates: place.coordinates,
      city: place.city,
      region,
      rating: generateRating(),
      revisitPotential: generateRevisitPotential(),
      keyTakeaway: generateKeyTakeaway(category),
      personalNotes: '',
      icon: categoryIcons[category],
    };
  });
}

function generateKeyTakeaway(category: VisitedPlace['category']): string {
  const takeaways: Record<VisitedPlace['category'], string[]> = {
    food: ['Must-try local cuisine', 'Great for food lovers', 'Authentic flavors', 'Perfect for dining'],
    landmark: ['Historic significance', 'Iconic architecture', 'Cultural importance', 'Must-see attraction'],
    museum: ['Educational experience', 'Art and culture', 'Interactive exhibits', 'Rich collections'],
    park: ['Nature escape', 'Perfect for relaxation', 'Great for walks', 'Scenic beauty'],
    accommodation: ['Comfortable stay', 'Great location', 'Good amenities', 'Value for money'],
    transport: ['Convenient access', 'Transportation hub', 'Easy connections', 'Travel essential'],
    entertainment: ['Live entertainment', 'Vibrant atmosphere', 'Cultural experience', 'Night life'],
    shopping: ['Shopping paradise', 'Unique finds', 'Local products', 'Great deals'],
  };

  const options = takeaways[category];
  return options[Math.floor(Math.random() * options.length)];
}

export function groupPlacesByRegion(places: VisitedPlace[]): CityRegion[] {
  const regionGroups: Record<string, VisitedPlace[]> = {};

  places.forEach(place => {
    if (!regionGroups[place.region]) {
      regionGroups[place.region] = [];
    }
    regionGroups[place.region].push(place);
  });

  return Object.entries(regionGroups).map(([regionId, regionPlaces]) => ({
    ...regionDefinitions[regionId],
    places: regionPlaces,
  }));
}

// Sample data for development
export const sampleRegions: CityRegion[] = [
  {
    ...regionDefinitions.nyc,
    places: [],
  },
  {
    ...regionDefinitions.nashville,
    places: [],
  },
  {
    ...regionDefinitions.newjersey,
    places: [],
  },
  {
    ...regionDefinitions.international,
    places: [],
  },
];
