/**
 * Google Maps utilities for error handling and API management
 */

// Google Maps API error types
export enum GoogleMapsErrorType {
  API_KEY_MISSING = 'API_KEY_MISSING',
  API_KEY_INVALID = 'API_KEY_INVALID',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INITIALIZATION_ERROR = 'INITIALIZATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface GoogleMapsError {
  type: GoogleMapsErrorType;
  message: string;
  originalError?: any;
}

/**
 * Parse Google Maps API errors and return structured error information
 */
export function parseGoogleMapsError(error: any): GoogleMapsError {
  const errorMessage = error?.message || error?.toString() || 'Unknown error';
  
  // Check for common Google Maps API errors
  if (errorMessage.includes('InvalidKeyMapError') || errorMessage.includes('API key')) {
    return {
      type: GoogleMapsErrorType.API_KEY_INVALID,
      message: 'Invalid Google Maps API key. Please check your API key configuration.',
      originalError: error,
    };
  }
  
  if (errorMessage.includes('QuotaExceededError') || errorMessage.includes('quota')) {
    return {
      type: GoogleMapsErrorType.QUOTA_EXCEEDED,
      message: 'Google Maps API quota exceeded. Please try again later.',
      originalError: error,
    };
  }
  
  if (errorMessage.includes('NetworkError') || errorMessage.includes('network')) {
    return {
      type: GoogleMapsErrorType.NETWORK_ERROR,
      message: 'Network error loading Google Maps. Please check your internet connection.',
      originalError: error,
    };
  }
  
  if (errorMessage.includes('Cannot read properties of undefined')) {
    return {
      type: GoogleMapsErrorType.INITIALIZATION_ERROR,
      message: 'Google Maps failed to initialize properly. This may be due to browser extensions or network issues.',
      originalError: error,
    };
  }
  
  return {
    type: GoogleMapsErrorType.UNKNOWN_ERROR,
    message: `Google Maps error: ${errorMessage}`,
    originalError: error,
  };
}

/**
 * Check if Google Maps API key is available and valid
 */
export function validateGoogleMapsApiKey(): { isValid: boolean; error?: string } {
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
  
  if (!apiKey) {
    return {
      isValid: false,
      error: 'Google Maps API key is not configured. Set VITE_GOOGLE_MAPS_API_KEY in your environment variables.',
    };
  }
  
  if (apiKey.length < 20) {
    return {
      isValid: false,
      error: 'Google Maps API key appears to be invalid (too short).',
    };
  }
  
  return { isValid: true };
}

/**
 * Suppress Google Maps console warnings in development
 */
export function suppressGoogleMapsWarnings() {
  if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
    return;
  }
  
  const originalWarn = console.warn;
  const originalError = console.error;
  
  console.warn = (...args) => {
    const message = args[0];
    if (typeof message === 'string') {
      // Suppress known Google Maps deprecation warnings
      if (
        message.includes('google.maps.Marker is deprecated') ||
        message.includes('AdvancedMarkerElement') ||
        message.includes('Performance warning! LoadScript has been reloaded') ||
        message.includes('Google Maps JavaScript API warning')
      ) {
        return;
      }
    }
    originalWarn.apply(console, args);
  };
  
  console.error = (...args) => {
    const message = args[0];
    if (typeof message === 'string') {
      // Suppress certain Google Maps errors that are not critical
      if (
        message.includes('Cannot read properties of undefined') &&
        message.includes('maps')
      ) {
        console.log('Google Maps initialization error (suppressed):', message);
        return;
      }
    }
    originalError.apply(console, args);
  };
}

/**
 * Clean up browser extension attributes that cause hydration issues
 */
export function cleanupBrowserExtensionAttributes() {
  if (typeof document === 'undefined') return;
  
  const extensionAttributes = [
    'data-lt-installed',
    'data-qb-installed',
    'data-new-gr-c-s-check-loaded',
    'data-gr-ext-installed',
    'data-new-gr-c-s-loaded',
    'data-grammarly-shadow-root',
    'cz-shortcut-listen',
    'data-darkreader-mode',
    'data-darkreader-scheme',
  ];
  
  [document.documentElement, document.body].forEach(element => {
    if (element) {
      extensionAttributes.forEach(attr => {
        if (element.hasAttribute(attr)) {
          element.removeAttribute(attr);
        }
      });
    }
  });
}

/**
 * Initialize Google Maps with error handling
 */
export function initializeGoogleMaps(): Promise<void> {
  return new Promise((resolve, reject) => {
    // Check API key first
    const validation = validateGoogleMapsApiKey();
    if (!validation.isValid) {
      reject(new Error(validation.error));
      return;
    }
    
    // Suppress warnings
    suppressGoogleMapsWarnings();
    
    // Clean up browser extension attributes
    cleanupBrowserExtensionAttributes();
    
    // Check if Google Maps is already loaded
    if (typeof window !== 'undefined' && window.google?.maps) {
      resolve();
      return;
    }
    
    // Set up error handling for script loading
    const handleError = (error: any) => {
      const parsedError = parseGoogleMapsError(error);
      reject(new Error(parsedError.message));
    };
    
    // Listen for Google Maps API errors
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        if (event.filename?.includes('maps.googleapis.com')) {
          handleError(event.error);
        }
      });
    }
    
    resolve();
  });
}

/**
 * Get user-friendly error message for Google Maps errors
 */
export function getGoogleMapsErrorMessage(error: GoogleMapsError): string {
  switch (error.type) {
    case GoogleMapsErrorType.API_KEY_MISSING:
      return 'Google Maps is not configured. Using demo mode instead.';
    case GoogleMapsErrorType.API_KEY_INVALID:
      return 'Google Maps configuration issue. Using demo mode instead.';
    case GoogleMapsErrorType.QUOTA_EXCEEDED:
      return 'Google Maps usage limit reached. Using demo mode instead.';
    case GoogleMapsErrorType.NETWORK_ERROR:
      return 'Network issue loading maps. Using demo mode instead.';
    case GoogleMapsErrorType.INITIALIZATION_ERROR:
      return 'Maps initialization failed. Using demo mode instead.';
    default:
      return 'Maps unavailable. Using demo mode instead.';
  }
}
