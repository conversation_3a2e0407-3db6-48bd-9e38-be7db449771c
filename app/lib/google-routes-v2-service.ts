/**
 * Google Routes API v2 Service
 *
 * Provides modern REST API integration for Google Routes API v2
 * with enhanced features like traffic-aware routing, alternative routes,
 * and improved performance over the legacy Directions API.
 */

export interface RoutesV2Config {
  apiKey: string;
  endpoint: string;
  defaultLanguage?: string;
  defaultUnits?: 'IMPERIAL' | 'METRIC';
}

export interface LocationV2 {
  latLng: {
    latitude: number;
    longitude: number;
  };
}

export interface WaypointV2 {
  location: LocationV2;
  via?: boolean;
  vehicleStopover?: boolean;
  sideOfRoad?: boolean;
}

export interface RouteModifiers {
  avoidTolls?: boolean;
  avoidHighways?: boolean;
  avoidFerries?: boolean;
  avoidIndoor?: boolean;
}

export interface ComputeRoutesRequest {
  origin: WaypointV2;
  destination: WaypointV2;
  intermediates?: WaypointV2[];
  travelMode: 'DRIVE' | 'WALK' | 'BICYCLE' | 'TRANSIT';
  routingPreference?: 'TRAFFIC_UNAWARE' | 'TRAFFIC_AWARE' | 'TRAFFIC_AWARE_OPTIMAL';
  polylineQuality?: 'HIGH_QUALITY' | 'OVERVIEW';
  polylineEncoding?: 'ENCODED_POLYLINE' | 'GEO_JSON_LINESTRING';
  departureTime?: string; // RFC3339 timestamp
  arrivalTime?: string; // RFC3339 timestamp
  computeAlternativeRoutes?: boolean;
  routeModifiers?: RouteModifiers;
  languageCode?: string;
  regionCode?: string;
  units?: 'IMPERIAL' | 'METRIC';
  optimizeWaypointOrder?: boolean;
  requestedReferenceTime?: string;
}

export interface PolylineV2 {
  encodedPolyline?: string;
  geoJsonLinestring?: any;
}

export interface LocationInfoV2 {
  latLng: {
    latitude: number;
    longitude: number;
  };
  heading?: number;
}

export interface TravelAdvisory {
  tollInfo?: {
    estimatedPrice?: Array<{
      currencyCode: string;
      units: string;
      nanos: number;
    }>;
  };
  speedReadingIntervals?: any[];
  fuelConsumptionMicroliters?: string;
  routeRestrictionsPartiallyIgnored?: boolean;
  transitFare?: any;
}

export interface RouteStepV2 {
  distanceMeters: number;
  staticDuration: string;
  polyline: PolylineV2;
  startLocation: LocationInfoV2;
  endLocation: LocationInfoV2;
  navigationInstruction?: {
    maneuver: string;
    instructions: string;
  };
  localizedValues?: {
    distance?: {
      text: string;
    };
    staticDuration?: {
      text: string;
    };
  };
  travelMode: string;
}

export interface RouteLegV2 {
  distanceMeters: number;
  duration: string;
  staticDuration: string;
  polyline: PolylineV2;
  startLocation: LocationInfoV2;
  endLocation: LocationInfoV2;
  steps: RouteStepV2[];
  travelAdvisory?: TravelAdvisory;
  localizedValues?: {
    distance?: {
      text: string;
    };
    duration?: {
      text: string;
    };
    staticDuration?: {
      text: string;
    };
  };
}

export interface Viewport {
  low: {
    latitude: number;
    longitude: number;
  };
  high: {
    latitude: number;
    longitude: number;
  };
}

export interface RouteV2 {
  legs: RouteLegV2[];
  distanceMeters: number;
  duration: string;
  staticDuration: string;
  polyline: PolylineV2;
  description: string;
  warnings: string[];
  viewport: Viewport;
  travelAdvisory: TravelAdvisory;
  optimizedIntermediateWaypointIndex?: number[];
  localizedValues?: {
    distance?: {
      text: string;
    };
    duration?: {
      text: string;
    };
    staticDuration?: {
      text: string;
    };
  };
  routeToken?: string;
}

export interface FallbackInfo {
  routingMode: string;
  reason: string;
}

export interface RoutesV2Response {
  routes: RouteV2[];
  fallbackInfo?: FallbackInfo;
  geocodingResults?: any;
}

export interface RoutesV2Error {
  error: {
    code: number;
    message: string;
    status: string;
    details?: any[];
  };
}

/**
 * Rate limiting configuration for Routes API v2
 */
const ROUTES_V2_RATE_LIMITS = {
  requestsPerMinute: 300, // Higher limit than legacy API
  requestsPerSecond: 10,
  maxRetries: 3,
  retryDelay: 1000, // 1 second
};

/**
 * Cache configuration
 */
const CACHE_CONFIG = {
  maxSize: 100,
  ttlMinutes: 30,
};

interface CacheEntry {
  response: RoutesV2Response;
  timestamp: number;
  ttl: number;
}

/**
 * Google Routes API v2 Service Class
 */
export class GoogleRoutesV2Service {
  private config: RoutesV2Config;
  private requestCount: Map<string, number> = new Map();
  private cache: Map<string, CacheEntry> = new Map();

  constructor(config: RoutesV2Config) {
    this.config = {
      defaultLanguage: 'en',
      defaultUnits: 'METRIC',
      ...config,
    };
  }

  /**
   * Compute routes using Google Routes API v2
   */
  async computeRoutes(request: ComputeRoutesRequest): Promise<RoutesV2Response> {
    // Check rate limits
    this.checkRateLimit();

    // Check cache
    const cacheKey = this.generateCacheKey(request);
    const cachedResponse = this.getFromCache(cacheKey);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Prepare request with defaults
    const fullRequest: ComputeRoutesRequest = {
      languageCode: this.config.defaultLanguage,
      units: this.config.defaultUnits,
      polylineQuality: 'HIGH_QUALITY',
      polylineEncoding: 'ENCODED_POLYLINE',
      ...request,
    };

    try {
      const response = await this.makeRequest(fullRequest);

      // Cache successful response
      this.setCache(cacheKey, response);

      return response;
    } catch (error) {
      console.error('Routes API v2 request failed:', error);
      throw this.handleError(error);
    }
  }

  /**
   * Make HTTP request to Routes API v2
   */
  private async makeRequest(request: ComputeRoutesRequest): Promise<RoutesV2Response> {
    const headers = {
      'Content-Type': 'application/json',
      'X-Goog-Api-Key': this.config.apiKey,
      'X-Goog-FieldMask': this.getFieldMask(),
    };

    const response = await fetch(this.config.endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Routes API v2 error: ${response.status} - ${JSON.stringify(errorData)}`);
    }

    return response.json();
  }

  /**
   * Generate field mask for optimized responses
   */
  private getFieldMask(): string {
    return [
      'routes.duration',
      'routes.distanceMeters',
      'routes.polyline.encodedPolyline',
      'routes.legs.duration',
      'routes.legs.distanceMeters',
      'routes.legs.polyline.encodedPolyline',
      'routes.legs.startLocation',
      'routes.legs.endLocation',
      'routes.legs.steps.distanceMeters',
      'routes.legs.steps.staticDuration',
      'routes.legs.steps.polyline.encodedPolyline',
      'routes.legs.steps.startLocation',
      'routes.legs.steps.endLocation',
      'routes.legs.steps.navigationInstruction',
      'routes.legs.steps.localizedValues',
      'routes.legs.localizedValues',
      'routes.localizedValues',
      'routes.viewport',
      'routes.travelAdvisory',
      'routes.optimizedIntermediateWaypointIndex',
      'routes.description',
      'routes.warnings',
    ].join(',');
  }

  /**
   * Check rate limits
   */
  private checkRateLimit(): void {
    const now = Date.now();
    const minute = Math.floor(now / 60000);
    const minuteKey = `minute_${minute}`;

    const currentCount = this.requestCount.get(minuteKey) || 0;
    if (currentCount >= ROUTES_V2_RATE_LIMITS.requestsPerMinute) {
      throw new Error('Rate limit exceeded. Please try again in a minute.');
    }

    this.requestCount.set(minuteKey, currentCount + 1);

    // Clean up old entries
    this.requestCount.forEach((_, key) => {
      const keyMinute = parseInt(key.split('_')[1]);
      if (minute - keyMinute > 2) {
        this.requestCount.delete(key);
      }
    });
  }

  /**
   * Generate cache key
   */
  private generateCacheKey(request: ComputeRoutesRequest): string {
    const key = {
      origin: request.origin,
      destination: request.destination,
      intermediates: request.intermediates,
      travelMode: request.travelMode,
      routingPreference: request.routingPreference,
      routeModifiers: request.routeModifiers,
    };
    return btoa(JSON.stringify(key));
  }

  /**
   * Get from cache
   */
  private getFromCache(key: string): RoutesV2Response | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.response;
  }

  /**
   * Set cache
   */
  private setCache(key: string, response: RoutesV2Response): void {
    const now = Date.now();
    const ttl = CACHE_CONFIG.ttlMinutes * 60 * 1000;

    // Limit cache size
    if (this.cache.size >= CACHE_CONFIG.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, {
      response,
      timestamp: now,
      ttl,
    });
  }

  /**
   * Handle API errors
   */
  private handleError(error: any): Error {
    if (error.message?.includes('Quota exceeded')) {
      return new Error('Rate limit exceeded. Please try again in a minute.');
    }

    if (error.message?.includes('API key')) {
      return new Error('Invalid API key or insufficient permissions.');
    }

    return new Error(`Routes API v2 error: ${error.message || 'Unknown error'}`);
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; hitRate: number } {
    return {
      size: this.cache.size,
      hitRate: 0, // TODO: Implement hit rate tracking
    };
  }
}

/**
 * Create singleton instance
 */
let routesV2ServiceInstance: GoogleRoutesV2Service | null = null;

export function createRoutesV2Service(config: RoutesV2Config): GoogleRoutesV2Service {
  if (!routesV2ServiceInstance) {
    routesV2ServiceInstance = new GoogleRoutesV2Service(config);
  }
  return routesV2ServiceInstance;
}

export function getRoutesV2Service(): GoogleRoutesV2Service {
  if (!routesV2ServiceInstance) {
    throw new Error('Routes V2 service not initialized. Call createRoutesV2Service first.');
  }
  return routesV2ServiceInstance;
}
