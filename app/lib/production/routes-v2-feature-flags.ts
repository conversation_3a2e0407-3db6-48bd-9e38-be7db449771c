/**
 * Feature Flag Management System for Routes API v2
 * 
 * Provides sophisticated feature flag management with gradual rollout,
 * A/B testing, user targeting, and real-time flag updates.
 */

import { getProductionConfig } from './routes-v2-config';
import { getRoutesV2Monitor } from './routes-v2-monitoring';

export interface FeatureFlagRule {
  id: string;
  name: string;
  enabled: boolean;
  percentage: number;
  conditions: FeatureFlagCondition[];
  metadata: Record<string, any>;
  createdAt: number;
  updatedAt: number;
  createdBy: string;
}

export interface FeatureFlagCondition {
  type: 'user_id' | 'email' | 'country' | 'browser' | 'device' | 'custom';
  operator: 'equals' | 'contains' | 'starts_with' | 'in' | 'not_in' | 'greater_than' | 'less_than';
  value: string | string[] | number;
}

export interface UserContext {
  userId?: string;
  email?: string;
  country?: string;
  browser?: string;
  device?: string;
  customAttributes?: Record<string, any>;
}

export interface FeatureFlagEvaluation {
  flagId: string;
  enabled: boolean;
  reason: string;
  ruleId?: string;
  timestamp: number;
  userContext: UserContext;
}

export interface RolloutStrategy {
  type: 'percentage' | 'user_list' | 'gradual' | 'canary';
  config: {
    percentage?: number;
    userList?: string[];
    gradualSteps?: { percentage: number; duration: number }[];
    canaryGroups?: string[];
  };
}

class FeatureFlagManager {
  private config = getProductionConfig();
  private monitor = getRoutesV2Monitor();
  private flags: Map<string, FeatureFlagRule> = new Map();
  private evaluationCache: Map<string, FeatureFlagEvaluation> = new Map();
  private cacheExpiry: number = 5 * 60 * 1000; // 5 minutes
  private lastFlagUpdate: number = 0;

  constructor() {
    this.initializeDefaultFlags();
    this.startFlagRefresh();
  }

  private initializeDefaultFlags() {
    const defaultFlags: FeatureFlagRule[] = [
      {
        id: 'routes_v2_enabled',
        name: 'Routes API v2 Enabled',
        enabled: this.config.getConfig().enableRoutesV2,
        percentage: this.config.getConfig().routesV2Percentage,
        conditions: [],
        metadata: {
          description: 'Enable Routes API v2 for route calculations',
          category: 'core',
          impact: 'high',
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
        createdBy: 'system',
      },
      {
        id: 'enhanced_routing',
        name: 'Enhanced Routing Features',
        enabled: this.config.isFeatureEnabled('enhancedRouting'),
        percentage: 100,
        conditions: [
          {
            type: 'custom',
            operator: 'equals',
            value: 'routes_v2_enabled',
          },
        ],
        metadata: {
          description: 'Enable enhanced routing features like traffic-aware routing',
          category: 'feature',
          impact: 'medium',
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
        createdBy: 'system',
      },
      {
        id: 'alternative_routes',
        name: 'Alternative Routes',
        enabled: this.config.isFeatureEnabled('alternativeRoutes'),
        percentage: 80,
        conditions: [],
        metadata: {
          description: 'Show alternative route options to users',
          category: 'feature',
          impact: 'low',
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
        createdBy: 'system',
      },
      {
        id: 'real_time_traffic',
        name: 'Real-time Traffic Data',
        enabled: this.config.isFeatureEnabled('realTimeTraffic'),
        percentage: 50,
        conditions: [
          {
            type: 'country',
            operator: 'in',
            value: ['US', 'CA', 'GB', 'DE', 'FR'],
          },
        ],
        metadata: {
          description: 'Use real-time traffic data for route calculations',
          category: 'feature',
          impact: 'medium',
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
        createdBy: 'system',
      },
      {
        id: 'ai_route_suggestions',
        name: 'AI Route Suggestions',
        enabled: this.config.isFeatureEnabled('aiRouteSuggestions'),
        percentage: 10,
        conditions: [
          {
            type: 'custom',
            operator: 'equals',
            value: 'beta_user',
          },
        ],
        metadata: {
          description: 'AI-powered route suggestions (experimental)',
          category: 'experimental',
          impact: 'low',
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
        createdBy: 'system',
      },
    ];

    defaultFlags.forEach(flag => {
      this.flags.set(flag.id, flag);
    });
  }

  // Flag Evaluation
  isEnabled(flagId: string, userContext: UserContext = {}): boolean {
    const evaluation = this.evaluateFlag(flagId, userContext);
    return evaluation.enabled;
  }

  evaluateFlag(flagId: string, userContext: UserContext = {}): FeatureFlagEvaluation {
    const cacheKey = `${flagId}_${JSON.stringify(userContext)}`;
    const cached = this.evaluationCache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached;
    }

    const flag = this.flags.get(flagId);
    if (!flag) {
      const evaluation: FeatureFlagEvaluation = {
        flagId,
        enabled: false,
        reason: 'Flag not found',
        timestamp: Date.now(),
        userContext,
      };
      
      this.monitor.recordError(`Feature flag not found: ${flagId}`, {
        flagId,
        userContext,
      }, 'low');
      
      return evaluation;
    }

    let enabled = false;
    let reason = 'Flag disabled';
    let ruleId: string | undefined;

    // Check if flag is globally enabled
    if (!flag.enabled) {
      reason = 'Flag globally disabled';
    } else {
      // Check conditions
      const conditionsMet = this.evaluateConditions(flag.conditions, userContext);
      
      if (!conditionsMet.met) {
        reason = `Conditions not met: ${conditionsMet.reason}`;
      } else {
        // Check percentage rollout
        const userHash = this.getUserHash(userContext, flagId);
        const userPercentile = userHash % 100;
        
        if (userPercentile < flag.percentage) {
          enabled = true;
          reason = `Percentage rollout: ${userPercentile} < ${flag.percentage}`;
          ruleId = flag.id;
        } else {
          reason = `Percentage rollout: ${userPercentile} >= ${flag.percentage}`;
        }
      }
    }

    const evaluation: FeatureFlagEvaluation = {
      flagId,
      enabled,
      reason,
      ruleId,
      timestamp: Date.now(),
      userContext,
    };

    // Cache evaluation
    this.evaluationCache.set(cacheKey, evaluation);

    // Record metrics
    this.monitor.recordMetric('feature_flag.evaluation', 1, {
      flagId,
      enabled: enabled.toString(),
      reason: reason.substring(0, 50), // Truncate for metrics
    });

    // Log evaluation for debugging
    if (this.config.getConfig().enableDebugMode) {
      console.log(`🚩 Feature flag evaluation: ${flagId} = ${enabled} (${reason})`);
    }

    return evaluation;
  }

  private evaluateConditions(conditions: FeatureFlagCondition[], userContext: UserContext): { met: boolean; reason: string } {
    if (conditions.length === 0) {
      return { met: true, reason: 'No conditions' };
    }

    for (const condition of conditions) {
      const result = this.evaluateCondition(condition, userContext);
      if (!result.met) {
        return result;
      }
    }

    return { met: true, reason: 'All conditions met' };
  }

  private evaluateCondition(condition: FeatureFlagCondition, userContext: UserContext): { met: boolean; reason: string } {
    let contextValue: any;

    switch (condition.type) {
      case 'user_id':
        contextValue = userContext.userId;
        break;
      case 'email':
        contextValue = userContext.email;
        break;
      case 'country':
        contextValue = userContext.country;
        break;
      case 'browser':
        contextValue = userContext.browser;
        break;
      case 'device':
        contextValue = userContext.device;
        break;
      case 'custom':
        // For custom conditions, check if another flag is enabled
        if (typeof condition.value === 'string' && this.flags.has(condition.value)) {
          const dependentFlag = this.evaluateFlag(condition.value, userContext);
          contextValue = dependentFlag.enabled;
        } else {
          contextValue = userContext.customAttributes?.[condition.value as string];
        }
        break;
      default:
        return { met: false, reason: `Unknown condition type: ${condition.type}` };
    }

    if (contextValue === undefined || contextValue === null) {
      return { met: false, reason: `Missing context value for ${condition.type}` };
    }

    const met = this.evaluateOperator(contextValue, condition.operator, condition.value);
    return {
      met,
      reason: met ? 'Condition met' : `Condition not met: ${contextValue} ${condition.operator} ${condition.value}`,
    };
  }

  private evaluateOperator(contextValue: any, operator: FeatureFlagCondition['operator'], conditionValue: any): boolean {
    switch (operator) {
      case 'equals':
        return contextValue === conditionValue;
      case 'contains':
        return String(contextValue).includes(String(conditionValue));
      case 'starts_with':
        return String(contextValue).startsWith(String(conditionValue));
      case 'in':
        return Array.isArray(conditionValue) && conditionValue.includes(contextValue);
      case 'not_in':
        return Array.isArray(conditionValue) && !conditionValue.includes(contextValue);
      case 'greater_than':
        return Number(contextValue) > Number(conditionValue);
      case 'less_than':
        return Number(contextValue) < Number(conditionValue);
      default:
        return false;
    }
  }

  private getUserHash(userContext: UserContext, flagId: string): number {
    const identifier = userContext.userId || userContext.email || 'anonymous';
    const hashInput = `${identifier}_${flagId}`;
    
    // Simple hash function for consistent user bucketing
    let hash = 0;
    for (let i = 0; i < hashInput.length; i++) {
      const char = hashInput.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash);
  }

  // Flag Management
  updateFlag(flagId: string, updates: Partial<FeatureFlagRule>) {
    const flag = this.flags.get(flagId);
    if (!flag) {
      throw new Error(`Flag not found: ${flagId}`);
    }

    const updatedFlag: FeatureFlagRule = {
      ...flag,
      ...updates,
      updatedAt: Date.now(),
    };

    this.flags.set(flagId, updatedFlag);
    this.clearEvaluationCache();

    this.monitor.recordMetric('feature_flag.update', 1, {
      flagId,
      enabled: updatedFlag.enabled.toString(),
    });

    console.log(`🚩 Feature flag updated: ${flagId}`, updates);
  }

  createFlag(flag: Omit<FeatureFlagRule, 'createdAt' | 'updatedAt'>) {
    const newFlag: FeatureFlagRule = {
      ...flag,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    this.flags.set(flag.id, newFlag);

    this.monitor.recordMetric('feature_flag.create', 1, {
      flagId: flag.id,
    });

    console.log(`🚩 Feature flag created: ${flag.id}`);
  }

  deleteFlag(flagId: string) {
    if (!this.flags.has(flagId)) {
      throw new Error(`Flag not found: ${flagId}`);
    }

    this.flags.delete(flagId);
    this.clearEvaluationCache();

    this.monitor.recordMetric('feature_flag.delete', 1, {
      flagId,
    });

    console.log(`🚩 Feature flag deleted: ${flagId}`);
  }

  // Gradual Rollout
  async startGradualRollout(flagId: string, strategy: RolloutStrategy) {
    const flag = this.flags.get(flagId);
    if (!flag) {
      throw new Error(`Flag not found: ${flagId}`);
    }

    if (strategy.type === 'gradual' && strategy.config.gradualSteps) {
      for (const step of strategy.config.gradualSteps) {
        this.updateFlag(flagId, { percentage: step.percentage });
        
        console.log(`🚩 Gradual rollout step: ${flagId} -> ${step.percentage}%`);
        
        // Wait for step duration
        await new Promise(resolve => setTimeout(resolve, step.duration));
        
        // Check for issues
        const healthStatus = this.monitor.getHealthStatus();
        const hasIssues = Object.values(healthStatus).some(status => status.status === 'unhealthy');
        
        if (hasIssues) {
          console.warn(`🚩 Health issues detected during rollout, pausing: ${flagId}`);
          break;
        }
      }
    }
  }

  // Utilities
  getAllFlags(): FeatureFlagRule[] {
    return Array.from(this.flags.values());
  }

  getFlagStatus(flagId: string): FeatureFlagRule | null {
    return this.flags.get(flagId) || null;
  }

  clearEvaluationCache() {
    this.evaluationCache.clear();
  }

  private startFlagRefresh() {
    // In production, this would fetch flags from a remote service
    setInterval(() => {
      if (this.config.isProductionEnvironment()) {
        this.refreshFlagsFromRemote();
      }
    }, 60000); // Check every minute
  }

  private async refreshFlagsFromRemote() {
    try {
      // In a real implementation, this would fetch from a feature flag service
      console.log('🚩 Refreshing feature flags from remote service...');
      
      // For now, just clear cache to ensure fresh evaluations
      this.clearEvaluationCache();
      this.lastFlagUpdate = Date.now();
    } catch (error) {
      this.monitor.recordError('Failed to refresh feature flags', {
        operation: 'flag_refresh',
        error: error instanceof Error ? error.message : 'Unknown error',
      }, 'medium');
    }
  }

  // A/B Testing Support
  getVariant(experimentId: string, userContext: UserContext, variants: string[]): string {
    const userHash = this.getUserHash(userContext, experimentId);
    const variantIndex = userHash % variants.length;
    const selectedVariant = variants[variantIndex];

    this.monitor.recordMetric('ab_test.assignment', 1, {
      experimentId,
      variant: selectedVariant,
    });

    return selectedVariant;
  }
}

// Singleton instance
let featureFlagManager: FeatureFlagManager | null = null;

export function getFeatureFlagManager(): FeatureFlagManager {
  if (!featureFlagManager) {
    featureFlagManager = new FeatureFlagManager();
  }
  return featureFlagManager;
}

export function resetFeatureFlagManager(): void {
  featureFlagManager = null;
}

// Export types
