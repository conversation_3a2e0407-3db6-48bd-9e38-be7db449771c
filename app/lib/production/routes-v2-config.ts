/**
 * Production Configuration Management for Routes API v2
 * 
 * Handles environment-specific configuration, feature flags,
 * and production deployment settings for Routes V2.
 */

export interface ProductionConfig {
  // Feature flags
  enableRoutesV2: boolean;
  routesV2Percentage: number;
  enableFallback: boolean;
  enableDebugMode: boolean;
  
  // API configuration
  apiKey: string;
  endpoint: string;
  timeout: number;
  retryAttempts: number;
  
  // Rate limiting
  requestsPerMinute: number;
  requestsPerHour: number;
  burstLimit: number;
  
  // Caching
  cacheEnabled: boolean;
  cacheTtlMinutes: number;
  maxCacheSize: number;
  
  // Monitoring
  enableMetrics: boolean;
  enableErrorTracking: boolean;
  enablePerformanceMonitoring: boolean;
  metricsEndpoint?: string;
  
  // Deployment
  environment: 'development' | 'staging' | 'production';
  version: string;
  deploymentId: string;
  region: string;
}

export interface FeatureFlags {
  // Core features
  routesV2Enabled: boolean;
  enhancedRouting: boolean;
  alternativeRoutes: boolean;
  trafficAwareRouting: boolean;
  
  // UI features
  enhancedControls: boolean;
  routeComparison: boolean;
  environmentalInfo: boolean;
  routeOptimization: boolean;
  
  // Advanced features
  realTimeTraffic: boolean;
  tollCalculation: boolean;
  ecoFriendlyRoutes: boolean;
  routeSharing: boolean;
  
  // Experimental features
  aiRouteSuggestions: boolean;
  predictiveRouting: boolean;
  multiModalRouting: boolean;
  routePersonalization: boolean;
}

export interface MonitoringConfig {
  // Error tracking
  sentryDsn?: string;
  errorSampleRate: number;
  
  // Performance monitoring
  performanceTracking: boolean;
  performanceSampleRate: number;
  
  // Analytics
  analyticsEnabled: boolean;
  analyticsEndpoint?: string;
  
  // Logging
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  logToConsole: boolean;
  logToRemote: boolean;
  
  // Health checks
  healthCheckInterval: number;
  healthCheckTimeout: number;
  
  // Alerts
  alertThresholds: {
    errorRate: number;
    responseTime: number;
    memoryUsage: number;
    cacheHitRate: number;
  };
}

class ProductionConfigManager {
  private config: ProductionConfig;
  private featureFlags: FeatureFlags;
  private monitoring: MonitoringConfig;
  private configCache: Map<string, any> = new Map();
  private lastConfigUpdate: number = 0;
  private configUpdateInterval: number = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.config = this.loadProductionConfig();
    this.featureFlags = this.loadFeatureFlags();
    this.monitoring = this.loadMonitoringConfig();
    
    // Start periodic config refresh
    this.startConfigRefresh();
  }

  private loadProductionConfig(): ProductionConfig {
    const env = import.meta.env;
    
    return {
      // Feature flags from environment
      enableRoutesV2: env.VITE_USE_ROUTES_V2 === 'true',
      routesV2Percentage: parseInt(env.VITE_ROUTES_V2_PERCENTAGE || '0'),
      enableFallback: env.VITE_ENABLE_ROUTES_FALLBACK === 'true',
      enableDebugMode: env.VITE_ROUTES_DEBUG === 'true',
      
      // API configuration
      apiKey: env.VITE_GOOGLE_MAPS_API_KEY || '',
      endpoint: env.VITE_ROUTES_V2_ENDPOINT || 'https://routes.googleapis.com/directions/v2:computeRoutes',
      timeout: parseInt(env.VITE_API_TIMEOUT || '30000'),
      retryAttempts: parseInt(env.VITE_API_RETRY_ATTEMPTS || '3'),
      
      // Rate limiting
      requestsPerMinute: parseInt(env.VITE_RATE_LIMIT_PER_MINUTE || '300'),
      requestsPerHour: parseInt(env.VITE_RATE_LIMIT_PER_HOUR || '10000'),
      burstLimit: parseInt(env.VITE_BURST_LIMIT || '10'),
      
      // Caching
      cacheEnabled: env.VITE_CACHE_ENABLED !== 'false',
      cacheTtlMinutes: parseInt(env.VITE_CACHE_TTL_MINUTES || '30'),
      maxCacheSize: parseInt(env.VITE_MAX_CACHE_SIZE || '100'),
      
      // Monitoring
      enableMetrics: env.VITE_ENABLE_METRICS === 'true',
      enableErrorTracking: env.VITE_ENABLE_ERROR_TRACKING === 'true',
      enablePerformanceMonitoring: env.VITE_ENABLE_PERFORMANCE_MONITORING === 'true',
      metricsEndpoint: env.VITE_METRICS_ENDPOINT,
      
      // Deployment
      environment: (env.VITE_ENVIRONMENT || 'development') as ProductionConfig['environment'],
      version: env.VITE_APP_VERSION || '1.0.0',
      deploymentId: env.VITE_DEPLOYMENT_ID || 'local',
      region: env.VITE_DEPLOYMENT_REGION || 'us-east-1',
    };
  }

  private loadFeatureFlags(): FeatureFlags {
    const env = import.meta.env;
    
    return {
      // Core features
      routesV2Enabled: env.VITE_FEATURE_ROUTES_V2 === 'true',
      enhancedRouting: env.VITE_FEATURE_ENHANCED_ROUTING === 'true',
      alternativeRoutes: env.VITE_FEATURE_ALTERNATIVE_ROUTES === 'true',
      trafficAwareRouting: env.VITE_FEATURE_TRAFFIC_AWARE === 'true',
      
      // UI features
      enhancedControls: env.VITE_FEATURE_ENHANCED_CONTROLS === 'true',
      routeComparison: env.VITE_FEATURE_ROUTE_COMPARISON === 'true',
      environmentalInfo: env.VITE_FEATURE_ENVIRONMENTAL_INFO === 'true',
      routeOptimization: env.VITE_FEATURE_ROUTE_OPTIMIZATION === 'true',
      
      // Advanced features
      realTimeTraffic: env.VITE_FEATURE_REAL_TIME_TRAFFIC === 'true',
      tollCalculation: env.VITE_FEATURE_TOLL_CALCULATION === 'true',
      ecoFriendlyRoutes: env.VITE_FEATURE_ECO_FRIENDLY === 'true',
      routeSharing: env.VITE_FEATURE_ROUTE_SHARING === 'true',
      
      // Experimental features
      aiRouteSuggestions: env.VITE_FEATURE_AI_SUGGESTIONS === 'true',
      predictiveRouting: env.VITE_FEATURE_PREDICTIVE_ROUTING === 'true',
      multiModalRouting: env.VITE_FEATURE_MULTI_MODAL === 'true',
      routePersonalization: env.VITE_FEATURE_PERSONALIZATION === 'true',
    };
  }

  private loadMonitoringConfig(): MonitoringConfig {
    const env = import.meta.env;
    
    return {
      // Error tracking
      sentryDsn: env.VITE_SENTRY_DSN,
      errorSampleRate: parseFloat(env.VITE_ERROR_SAMPLE_RATE || '1.0'),
      
      // Performance monitoring
      performanceTracking: env.VITE_PERFORMANCE_TRACKING === 'true',
      performanceSampleRate: parseFloat(env.VITE_PERFORMANCE_SAMPLE_RATE || '0.1'),
      
      // Analytics
      analyticsEnabled: env.VITE_ANALYTICS_ENABLED === 'true',
      analyticsEndpoint: env.VITE_ANALYTICS_ENDPOINT,
      
      // Logging
      logLevel: (env.VITE_LOG_LEVEL || 'info') as MonitoringConfig['logLevel'],
      logToConsole: env.VITE_LOG_TO_CONSOLE !== 'false',
      logToRemote: env.VITE_LOG_TO_REMOTE === 'true',
      
      // Health checks
      healthCheckInterval: parseInt(env.VITE_HEALTH_CHECK_INTERVAL || '60000'),
      healthCheckTimeout: parseInt(env.VITE_HEALTH_CHECK_TIMEOUT || '5000'),
      
      // Alert thresholds
      alertThresholds: {
        errorRate: parseFloat(env.VITE_ALERT_ERROR_RATE || '0.05'),
        responseTime: parseInt(env.VITE_ALERT_RESPONSE_TIME || '5000'),
        memoryUsage: parseFloat(env.VITE_ALERT_MEMORY_USAGE || '0.8'),
        cacheHitRate: parseFloat(env.VITE_ALERT_CACHE_HIT_RATE || '0.7'),
      },
    };
  }

  private startConfigRefresh() {
    setInterval(() => {
      this.refreshConfig();
    }, this.configUpdateInterval);
  }

  private async refreshConfig() {
    try {
      // In production, this would fetch from a config service
      // For now, we'll just reload from environment
      const now = Date.now();
      if (now - this.lastConfigUpdate > this.configUpdateInterval) {
        this.config = this.loadProductionConfig();
        this.featureFlags = this.loadFeatureFlags();
        this.monitoring = this.loadMonitoringConfig();
        this.lastConfigUpdate = now;
        
        console.log('🔄 Configuration refreshed', {
          timestamp: new Date().toISOString(),
          environment: this.config.environment,
          version: this.config.version,
        });
      }
    } catch (error) {
      console.error('❌ Failed to refresh configuration:', error);
    }
  }

  // Public API
  getConfig(): ProductionConfig {
    return { ...this.config };
  }

  getFeatureFlags(): FeatureFlags {
    return { ...this.featureFlags };
  }

  getMonitoringConfig(): MonitoringConfig {
    return { ...this.monitoring };
  }

  isFeatureEnabled(feature: keyof FeatureFlags): boolean {
    return this.featureFlags[feature] === true;
  }

  getConfigValue<T>(key: string, defaultValue: T): T {
    if (this.configCache.has(key)) {
      return this.configCache.get(key);
    }
    
    // Navigate nested config object
    const keys = key.split('.');
    let value: any = this.config;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        value = defaultValue;
        break;
      }
    }
    
    this.configCache.set(key, value);
    return value;
  }

  updateFeatureFlag(feature: keyof FeatureFlags, enabled: boolean) {
    this.featureFlags[feature] = enabled;
    console.log(`🚩 Feature flag updated: ${feature} = ${enabled}`);
  }

  isProductionEnvironment(): boolean {
    return this.config.environment === 'production';
  }

  isStagingEnvironment(): boolean {
    return this.config.environment === 'staging';
  }

  isDevelopmentEnvironment(): boolean {
    return this.config.environment === 'development';
  }

  getEnvironmentInfo() {
    return {
      environment: this.config.environment,
      version: this.config.version,
      deploymentId: this.config.deploymentId,
      region: this.config.region,
      routesV2Enabled: this.config.enableRoutesV2,
      routesV2Percentage: this.config.routesV2Percentage,
    };
  }
}

// Singleton instance
let configManager: ProductionConfigManager | null = null;

export function getProductionConfig(): ProductionConfigManager {
  if (!configManager) {
    configManager = new ProductionConfigManager();
  }
  return configManager;
}

export function resetProductionConfig(): void {
  configManager = null;
}

// Export types and utilities
export { ProductionConfigManager };
