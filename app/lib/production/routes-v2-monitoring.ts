/**
 * Monitoring and Observability System for Routes API v2
 * 
 * Provides comprehensive monitoring, metrics collection, error tracking,
 * and performance monitoring for the Routes V2 implementation.
 */

import { getProductionConfig } from './routes-v2-config';

export interface MetricData {
  name: string;
  value: number;
  timestamp: number;
  tags: Record<string, string>;
  unit?: string;
}

export interface ErrorData {
  message: string;
  stack?: string;
  context: Record<string, any>;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  sessionId?: string;
}

export interface PerformanceData {
  operation: string;
  duration: number;
  timestamp: number;
  success: boolean;
  metadata: Record<string, any>;
}

export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: number;
  responseTime: number;
  details: Record<string, any>;
}

class RoutesV2Monitor {
  private config = getProductionConfig();
  private metrics: MetricData[] = [];
  private errors: ErrorData[] = [];
  private performance: PerformanceData[] = [];
  private healthChecks: Map<string, HealthCheckResult> = new Map();
  private sessionId: string;
  private userId?: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.startHealthChecks();
    this.startMetricsFlush();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Metrics Collection
  recordMetric(name: string, value: number, tags: Record<string, string> = {}, unit?: string) {
    const metric: MetricData = {
      name,
      value,
      timestamp: Date.now(),
      tags: {
        ...tags,
        environment: this.config.getConfig().environment,
        version: this.config.getConfig().version,
        region: this.config.getConfig().region,
      },
      unit,
    };

    this.metrics.push(metric);
    
    if (this.config.getMonitoringConfig().logToConsole) {
      console.log(`📊 Metric: ${name} = ${value}${unit ? ` ${unit}` : ''}`, tags);
    }

    // Flush metrics if buffer is getting large
    if (this.metrics.length > 100) {
      this.flushMetrics();
    }
  }

  // Error Tracking
  recordError(error: Error | string, context: Record<string, any> = {}, severity: ErrorData['severity'] = 'medium') {
    const errorData: ErrorData = {
      message: typeof error === 'string' ? error : error.message,
      stack: typeof error === 'object' ? error.stack : undefined,
      context: {
        ...context,
        sessionId: this.sessionId,
        userId: this.userId,
        timestamp: new Date().toISOString(),
      },
      timestamp: Date.now(),
      severity,
      userId: this.userId,
      sessionId: this.sessionId,
    };

    this.errors.push(errorData);

    if (this.config.getMonitoringConfig().logToConsole) {
      console.error(`🚨 Error [${severity}]:`, errorData.message, errorData.context);
    }

    // Send critical errors immediately
    if (severity === 'critical') {
      this.sendErrorToRemote(errorData);
    }

    // Check error rate threshold
    this.checkErrorRateThreshold();
  }

  // Performance Monitoring
  recordPerformance(operation: string, duration: number, success: boolean, metadata: Record<string, any> = {}) {
    const performanceData: PerformanceData = {
      operation,
      duration,
      timestamp: Date.now(),
      success,
      metadata: {
        ...metadata,
        sessionId: this.sessionId,
        userId: this.userId,
      },
    };

    this.performance.push(performanceData);

    // Record as metric
    this.recordMetric(`performance.${operation}.duration`, duration, {
      success: success.toString(),
      operation,
    }, 'ms');

    if (this.config.getMonitoringConfig().logToConsole) {
      console.log(`⏱️ Performance: ${operation} took ${duration}ms (${success ? 'success' : 'failure'})`);
    }

    // Check performance threshold
    this.checkPerformanceThreshold(operation, duration);
  }

  // Health Checks
  async performHealthCheck(service: string, checkFunction: () => Promise<any>): Promise<HealthCheckResult> {
    const startTime = Date.now();
    let status: HealthCheckResult['status'] = 'healthy';
    let details: Record<string, any> = {};

    try {
      const result = await Promise.race([
        checkFunction(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Health check timeout')), 
          this.config.getMonitoringConfig().healthCheckTimeout)
        ),
      ]);

      details = { result };
    } catch (error) {
      status = 'unhealthy';
      details = { 
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      };
    }

    const responseTime = Date.now() - startTime;
    
    // Determine status based on response time
    if (status === 'healthy' && responseTime > this.config.getMonitoringConfig().healthCheckTimeout * 0.8) {
      status = 'degraded';
    }

    const healthCheck: HealthCheckResult = {
      service,
      status,
      timestamp: Date.now(),
      responseTime,
      details,
    };

    this.healthChecks.set(service, healthCheck);
    
    this.recordMetric(`health.${service}.response_time`, responseTime, {
      service,
      status,
    }, 'ms');

    return healthCheck;
  }

  // Routes V2 Specific Monitoring
  recordRouteCalculation(
    success: boolean,
    duration: number,
    apiVersion: 'v1' | 'v2',
    waypointCount: number,
    travelMode: string,
    error?: string
  ) {
    this.recordPerformance('route_calculation', duration, success, {
      apiVersion,
      waypointCount,
      travelMode,
      error,
    });

    this.recordMetric('routes.calculation.count', 1, {
      success: success.toString(),
      apiVersion,
      travelMode,
    });

    if (!success && error) {
      this.recordError(error, {
        operation: 'route_calculation',
        apiVersion,
        waypointCount,
        travelMode,
      }, 'medium');
    }
  }

  recordCacheOperation(operation: 'hit' | 'miss' | 'set' | 'evict', cacheSize: number) {
    this.recordMetric(`cache.${operation}`, 1, {
      operation,
    });

    this.recordMetric('cache.size', cacheSize);

    // Calculate cache hit rate
    const recentCacheOps = this.metrics
      .filter(m => m.name.startsWith('cache.') && Date.now() - m.timestamp < 60000)
      .reduce((acc, m) => {
        const op = m.tags.operation;
        acc[op] = (acc[op] || 0) + m.value;
        return acc;
      }, {} as Record<string, number>);

    const hits = recentCacheOps.hit || 0;
    const misses = recentCacheOps.miss || 0;
    const total = hits + misses;
    
    if (total > 0) {
      const hitRate = hits / total;
      this.recordMetric('cache.hit_rate', hitRate, {}, 'ratio');
      
      // Check cache hit rate threshold
      if (hitRate < this.config.getMonitoringConfig().alertThresholds.cacheHitRate) {
        this.recordError(`Low cache hit rate: ${(hitRate * 100).toFixed(1)}%`, {
          operation: 'cache_monitoring',
          hitRate,
          threshold: this.config.getMonitoringConfig().alertThresholds.cacheHitRate,
        }, 'medium');
      }
    }
  }

  recordApiUsage(endpoint: string, method: string, statusCode: number, duration: number) {
    this.recordMetric('api.request.count', 1, {
      endpoint,
      method,
      statusCode: statusCode.toString(),
    });

    this.recordMetric('api.request.duration', duration, {
      endpoint,
      method,
    }, 'ms');

    if (statusCode >= 400) {
      this.recordError(`API error: ${statusCode}`, {
        endpoint,
        method,
        statusCode,
        duration,
      }, statusCode >= 500 ? 'high' : 'medium');
    }
  }

  // User Experience Monitoring
  recordUserInteraction(action: string, component: string, metadata: Record<string, any> = {}) {
    this.recordMetric('user.interaction', 1, {
      action,
      component,
    });

    if (this.config.getMonitoringConfig().analyticsEnabled) {
      // Send to analytics service
      this.sendAnalyticsEvent('user_interaction', {
        action,
        component,
        ...metadata,
        sessionId: this.sessionId,
        userId: this.userId,
      });
    }
  }

  recordPageLoad(page: string, duration: number) {
    this.recordPerformance('page_load', duration, true, { page });
    
    this.recordMetric('page.load_time', duration, {
      page,
    }, 'ms');
  }

  // Threshold Monitoring
  private checkErrorRateThreshold() {
    const recentErrors = this.errors.filter(e => Date.now() - e.timestamp < 60000);
    const recentMetrics = this.metrics.filter(m => Date.now() - m.timestamp < 60000);
    
    const totalRequests = recentMetrics
      .filter(m => m.name === 'routes.calculation.count')
      .reduce((sum, m) => sum + m.value, 0);

    if (totalRequests > 0) {
      const errorRate = recentErrors.length / totalRequests;
      
      if (errorRate > this.config.getMonitoringConfig().alertThresholds.errorRate) {
        this.recordError(`High error rate detected: ${(errorRate * 100).toFixed(1)}%`, {
          operation: 'error_rate_monitoring',
          errorRate,
          threshold: this.config.getMonitoringConfig().alertThresholds.errorRate,
          recentErrors: recentErrors.length,
          totalRequests,
        }, 'high');
      }
    }
  }

  private checkPerformanceThreshold(operation: string, duration: number) {
    const threshold = this.config.getMonitoringConfig().alertThresholds.responseTime;
    
    if (duration > threshold) {
      this.recordError(`Slow operation detected: ${operation}`, {
        operation: 'performance_monitoring',
        duration,
        threshold,
        operationName: operation,
      }, 'medium');
    }
  }

  // Data Transmission
  private async flushMetrics() {
    if (this.metrics.length === 0) return;

    const metricsToSend = [...this.metrics];
    this.metrics = [];

    if (this.config.getMonitoringConfig().logToRemote) {
      try {
        await this.sendMetricsToRemote(metricsToSend);
      } catch (error) {
        console.error('Failed to send metrics:', error);
        // Put metrics back in queue
        this.metrics.unshift(...metricsToSend);
      }
    }
  }

  private async sendMetricsToRemote(metrics: MetricData[]) {
    const endpoint = this.config.getMonitoringConfig().analyticsEndpoint;
    if (!endpoint) return;

    await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'metrics',
        data: metrics,
        metadata: {
          sessionId: this.sessionId,
          userId: this.userId,
          timestamp: Date.now(),
        },
      }),
    });
  }

  private async sendErrorToRemote(error: ErrorData) {
    const sentryDsn = this.config.getMonitoringConfig().sentryDsn;
    if (!sentryDsn) return;

    // In a real implementation, this would use Sentry SDK
    console.log('Would send error to Sentry:', error);
  }

  private async sendAnalyticsEvent(event: string, data: Record<string, any>) {
    const endpoint = this.config.getMonitoringConfig().analyticsEndpoint;
    if (!endpoint) return;

    await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: 'event',
        event,
        data,
        metadata: {
          sessionId: this.sessionId,
          userId: this.userId,
          timestamp: Date.now(),
        },
      }),
    });
  }

  // Periodic Tasks
  private startHealthChecks() {
    const interval = this.config.getMonitoringConfig().healthCheckInterval;
    
    setInterval(async () => {
      // Routes V2 API health check
      await this.performHealthCheck('routes_v2_api', async () => {
        const response = await fetch('https://routes.googleapis.com/directions/v2:computeRoutes', {
          method: 'OPTIONS',
        });
        return { status: response.status };
      });

      // Cache health check
      await this.performHealthCheck('cache', async () => {
        const cacheSize = this.metrics.filter(m => m.name === 'cache.size').slice(-1)[0]?.value || 0;
        return { size: cacheSize };
      });

      // Memory health check - Removed for Node.js compatibility
      // await this.performHealthCheck('memory', async () => {
      //   if (performance.memory) {
      //     const memoryUsage = performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit;
          
      //     if (memoryUsage > this.config.getMonitoringConfig().alertThresholds.memoryUsage) {
      //       throw new Error(`High memory usage: ${(memoryUsage * 100).toFixed(1)}%`);
      //     }
          
      //     return { memoryUsage };
      //   }
      //   return { memoryUsage: 'unknown' };
      // });
    }, interval);
  }

  private startMetricsFlush() {
    // Flush metrics every 30 seconds
    setInterval(() => {
      this.flushMetrics();
    }, 30000);

    // Flush on page unload
    window.addEventListener('beforeunload', () => {
      this.flushMetrics();
    });
  }

  // Public API
  setUserId(userId: string) {
    this.userId = userId;
  }

  getHealthStatus(): Record<string, HealthCheckResult> {
    return Object.fromEntries(this.healthChecks);
  }

  getMetricsSummary() {
    const now = Date.now();
    const recentMetrics = this.metrics.filter(m => now - m.timestamp < 300000); // Last 5 minutes

    return {
      totalMetrics: recentMetrics.length,
      errorCount: this.errors.filter(e => now - e.timestamp < 300000).length,
      performanceCount: this.performance.filter(p => now - p.timestamp < 300000).length,
      healthChecks: this.healthChecks.size,
      sessionId: this.sessionId,
      userId: this.userId,
    };
  }
}

// Singleton instance
let monitor: RoutesV2Monitor | null = null;

export function getRoutesV2Monitor(): RoutesV2Monitor {
  if (!monitor) {
    monitor = new RoutesV2Monitor();
  }
  return monitor;
}

export function resetRoutesV2Monitor(): void {
  monitor = null;
}

// Export types
