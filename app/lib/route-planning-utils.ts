/**
 * Route Planning Utilities - Routes API v2 Only
 *
 * Simplified route planning using only Google Routes API v2
 * with enhanced features and performance.
 */

import type {
  TravelRoute,
  RouteWaypoint,
  VisitedPlace
} from '~/types/wanderlust';

import type {
  EnhancedRouteOptions,
  EnhancedTravelRoute
} from '~/types/routes-v2';

import {
  GoogleRoutesV2Service,
  createRoutesV2Service,
  type RoutesV2Config
} from './google-routes-v2-service';

import {
  RoutesV2Transformer
} from './routes-v2-transformer';

// Routes V2 configuration
const ROUTES_V2_CONFIG: RoutesV2Config = {
  apiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY as string,
  endpoint: 'https://routes.googleapis.com/directions/v2:computeRoutes',
  defaultLanguage: 'en',
  defaultUnits: 'METRIC',
};

// Singleton service instance
let routesV2Service: GoogleRoutesV2Service | null = null;

function getRoutesService(): GoogleRoutesV2Service {
  if (!routesV2Service) {
    if (!ROUTES_V2_CONFIG.apiKey) {
      throw new Error('Google Maps API key is required. Set VITE_GOOGLE_MAPS_API_KEY environment variable.');
    }
    routesV2Service = createRoutesV2Service(ROUTES_V2_CONFIG);
  }
  return routesV2Service;
}

// Cache configuration
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes
const routeCache = new Map<string, { route: EnhancedTravelRoute; timestamp: number }>();

/**
 * Generate cache key for route requests
 */
function generateCacheKey(
  waypoints: RouteWaypoint[],
  travelMode: TravelRoute['travelMode'],
  options: Partial<EnhancedRouteOptions>
): string {
  const waypointKey = waypoints
    .map(wp => `${wp.location.lat.toFixed(6)},${wp.location.lng.toFixed(6)}`)
    .join('|');

  const optionsKey = Object.entries(options)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([key, value]) => `${key}:${value}`)
    .join('&');

  return `${waypointKey}_${travelMode}_${optionsKey}`;
}

/**
 * Get cached route if available and not expired
 */
function getCachedRoute(cacheKey: string): EnhancedTravelRoute | null {
  const cached = routeCache.get(cacheKey);

  if (!cached) return null;

  const isExpired = Date.now() - cached.timestamp > CACHE_DURATION;

  if (isExpired) {
    routeCache.delete(cacheKey);
    return null;
  }

  return cached.route;
}

/**
 * Cache a calculated route
 */
function cacheRoute(cacheKey: string, route: EnhancedTravelRoute): void {
  routeCache.set(cacheKey, {
    route,
    timestamp: Date.now(),
  });

  // Cleanup old cache entries (keep max 100 entries)
  if (routeCache.size > 100) {
    const oldestKey = Array.from(routeCache.keys())[0];
    routeCache.delete(oldestKey);
  }
}

/**
 * Convert VisitedPlace array to RouteWaypoint array
 */
export function placesToWaypoints(places: VisitedPlace[]): RouteWaypoint[] {
  return places.map((place, index) => ({
    location: {
      lat: place.coordinates.latitude,
      lng: place.coordinates.longitude,
    },
    stopover: index > 0 && index < places.length - 1, // First and last are not stopovers
    placeId: place.id,
  }));
}

/**
 * Calculate route using Routes API v2
 */
export async function calculateRoute(
  waypoints: RouteWaypoint[],
  travelMode: TravelRoute['travelMode'] = 'DRIVING',
  options: Partial<EnhancedRouteOptions> = {}
): Promise<EnhancedTravelRoute> {
  if (waypoints.length < 2) {
    throw new Error('At least 2 waypoints are required to calculate a route');
  }

  if (waypoints.length > 25) {
    throw new Error('Maximum 25 waypoints allowed per route');
  }

  const service = getRoutesService();
  const enhancedOptions: Partial<EnhancedRouteOptions> = {
    routingPreference: 'TRAFFIC_AWARE',
    polylineQuality: 'HIGH_QUALITY',
    computeTollInfo: true,
    ...options,
  };

  // Check cache first
  const cacheKey = generateCacheKey(waypoints, travelMode, enhancedOptions);
  const cachedRoute = getCachedRoute(cacheKey);
  if (cachedRoute) {
    return cachedRoute;
  }

  const request = RoutesV2Transformer.toComputeRoutesRequest(
    waypoints,
    travelMode,
    enhancedOptions
  );

  const response = await service.computeRoutes(request);
  const route = RoutesV2Transformer.fromRoutesV2Response(
    response,
    waypoints,
    travelMode,
    enhancedOptions
  );

  // Cache the result
  cacheRoute(cacheKey, route);

  return route;
}

/**
 * Get route alternatives using Routes API v2
 */
export async function getRouteAlternatives(
  waypoints: RouteWaypoint[],
  travelMode: TravelRoute['travelMode'] = 'DRIVING',
  maxAlternatives: number = 3
): Promise<EnhancedTravelRoute[]> {
  const options: Partial<EnhancedRouteOptions> = {
    includeAlternativeRoutes: true,
    maxAlternativeRoutes: maxAlternatives,
    routingPreference: 'TRAFFIC_AWARE',
  };

  const service = getRoutesService();
  const request = RoutesV2Transformer.toComputeRoutesRequest(
    waypoints,
    travelMode,
    options
  );

  const response = await service.computeRoutes(request);
  return RoutesV2Transformer.fromRoutesV2ResponseMultiple(
    response,
    waypoints,
    travelMode,
    options
  );
}

/**
 * Calculate route from VisitedPlaces with enhanced features
 */
export async function calculateRouteFromPlaces(
  places: VisitedPlace[],
  travelMode: TravelRoute['travelMode'] = 'DRIVING',
  options: Partial<EnhancedRouteOptions> = {}
): Promise<EnhancedTravelRoute> {
  const waypoints = placesToWaypoints(places);
  return calculateRoute(waypoints, travelMode, options);
}

/**
 * Clear route cache
 */
export function clearRouteCache(): void {
  routeCache.clear();
  getRoutesService().clearCache();
}

/**
 * Get cache statistics
 */
export function getCacheStats() {
  return {
    size: routeCache.size,
    entries: Array.from(routeCache.keys()),
  };
}

/**
 * Calculate route with enhanced Routes V2 features
 */
export async function calculateEnhancedRoute(
  waypoints: RouteWaypoint[],
  travelMode: TravelRoute['travelMode'] = 'DRIVING',
  options: Partial<EnhancedRouteOptions> = {}
): Promise<{
  primaryRoute: EnhancedTravelRoute;
  alternativeRoutes: EnhancedTravelRoute[];
  metadata: {
    apiVersion: 'v2';
    calculationTime: number;
    cacheHit: boolean;
  };
}> {
  const startTime = performance.now();

  // Enhanced options with defaults
  const enhancedOptions: Partial<EnhancedRouteOptions> = {
    routingPreference: 'TRAFFIC_AWARE',
    includeAlternativeRoutes: true,
    maxAlternativeRoutes: 3,
    polylineQuality: 'HIGH_QUALITY',
    computeTollInfo: true,
    ...options,
  };

  // Check cache first
  const cacheKey = generateCacheKey(waypoints, travelMode, enhancedOptions);
  const cachedRoute = getCachedRoute(cacheKey);

  if (cachedRoute) {
    return {
      primaryRoute: cachedRoute,
      alternativeRoutes: [],
      metadata: {
        apiVersion: 'v2',
        calculationTime: performance.now() - startTime,
        cacheHit: true,
      },
    };
  }

  const service = getRoutesService();
  const request = RoutesV2Transformer.toComputeRoutesRequest(waypoints, travelMode, enhancedOptions);
  const response = await service.computeRoutes(request);

  // Convert all routes
  const allRoutes = RoutesV2Transformer.fromRoutesV2ResponseMultiple(
    response,
    waypoints,
    travelMode,
    enhancedOptions
  );

  const primaryRoute = allRoutes[0];
  const alternativeRoutes = allRoutes.slice(1);

  // Cache primary route
  cacheRoute(cacheKey, primaryRoute);

  return {
    primaryRoute,
    alternativeRoutes,
    metadata: {
      apiVersion: 'v2',
      calculationTime: performance.now() - startTime,
      cacheHit: false,
    },
  };
}
