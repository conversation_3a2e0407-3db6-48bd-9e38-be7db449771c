/**
 * Gesture Utilities for WL-004 Mobile Navigation Patterns
 *
 * Provides helper functions for gesture calculations, touch event normalization,
 * snap point calculations, and animation helpers following the established
 * mobile-first architecture and performance standards.
 */

export interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

export interface SwipeGesture {
  direction: 'left' | 'right' | 'up' | 'down';
  distance: number;
  velocity: number;
  duration: number;
}

export interface DragGesture {
  deltaX: number;
  deltaY: number;
  velocity: number;
  isActive: boolean;
}

export interface SnapPoint {
  value: number;
  label?: string;
}

/**
 * Normalizes touch events across different browsers and devices
 */
export function normalizeTouchEvent(event: TouchEvent | MouseEvent): TouchPoint {
  const touch = 'touches' in event ? event.touches[0] || event.changedTouches[0] : event;

  return {
    x: touch.clientX,
    y: touch.clientY,
    timestamp: Date.now(),
  };
}

/**
 * Calculates swipe gesture from start and end touch points
 */
export function calculateSwipeGesture(start: TouchPoint, end: TouchPoint): SwipeGesture | null {
  const deltaX = end.x - start.x;
  const deltaY = end.y - start.y;
  const duration = end.timestamp - start.timestamp;

  // Minimum distance threshold for swipe detection (44px for touch accessibility)
  const minDistance = 44;
  const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

  if (distance < minDistance || duration === 0) {
    return null;
  }

  const velocity = distance / duration;

  // Determine primary direction
  const absX = Math.abs(deltaX);
  const absY = Math.abs(deltaY);

  let direction: SwipeGesture['direction'];
  if (absX > absY) {
    direction = deltaX > 0 ? 'right' : 'left';
  } else {
    direction = deltaY > 0 ? 'down' : 'up';
  }

  return {
    direction,
    distance,
    velocity,
    duration,
  };
}

/**
 * Finds the closest snap point to a given value
 */
export function findClosestSnapPoint(value: number, snapPoints: number[]): number {
  if (snapPoints.length === 0) return value;

  return snapPoints.reduce((closest, point) => {
    return Math.abs(point - value) < Math.abs(closest - value) ? point : closest;
  });
}

/**
 * Calculates snap point with velocity consideration for natural feel
 */
export function calculateSnapWithVelocity(
  currentValue: number,
  velocity: number,
  snapPoints: number[],
  velocityThreshold = 0.5
): number {
  if (snapPoints.length === 0) return currentValue;

  // If velocity is high enough, snap in the direction of movement
  if (Math.abs(velocity) > velocityThreshold) {
    const direction = velocity > 0 ? 1 : -1;
    const targetPoints = snapPoints.filter(point =>
      direction > 0 ? point > currentValue : point < currentValue
    );

    if (targetPoints.length > 0) {
      return direction > 0
        ? Math.min(...targetPoints)
        : Math.max(...targetPoints);
    }
  }

  // Otherwise, snap to closest point
  return findClosestSnapPoint(currentValue, snapPoints);
}

/**
 * Creates a spring animation configuration for gesture-driven interactions
 */
export function createSpringConfig(stiffness = 300, damping = 30) {
  return {
    transition: `transform 300ms cubic-bezier(0.34, 1.56, 0.64, 1)`,
    stiffness,
    damping,
  };
}

/**
 * Clamps a value between min and max bounds
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * Interpolates between two values
 */
export function lerp(start: number, end: number, factor: number): number {
  return start + (end - start) * factor;
}

/**
 * Converts a value from one range to another
 */
export function mapRange(
  value: number,
  inMin: number,
  inMax: number,
  outMin: number,
  outMax: number
): number {
  return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;
}

/**
 * Debounces a function call to improve performance
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttles a function call to improve performance during gestures
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Checks if the device supports touch events
 */
export function isTouchDevice(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

/**
 * Checks if the user prefers reduced motion
 */
export function prefersReducedMotion(): boolean {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Map-specific gesture utilities for WL-004B
 */

/**
 * Handles gesture conflicts between map and app-level gestures
 */
export function handleMapGestureConflict(isMapActive: boolean): void {
  if (typeof document === 'undefined') return;

  if (isMapActive) {
    // Disable app-level gesture handling
    document.body.style.touchAction = 'none';
    document.body.style.userSelect = 'none';
    document.body.style.webkitUserSelect = 'none';
  } else {
    // Re-enable app-level gesture handling
    document.body.style.touchAction = 'auto';
    document.body.style.userSelect = 'auto';
    document.body.style.webkitUserSelect = 'auto';
  }
}

/**
 * Debounces map events to improve performance
 */
export function debounceMapEvent<T extends (...args: any[]) => any>(
  func: T,
  delay: number = 300
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

/**
 * Throttles map events for better performance during rapid interactions
 */
export function throttleMapEvent<T extends (...args: any[]) => any>(
  func: T,
  limit: number = 100
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Calculates optimal touch target size for map controls
 */
export function getOptimalTouchTargetSize(): number {
  // Minimum 44px for accessibility, but scale based on device
  const baseSize = 44;
  const devicePixelRatio = window.devicePixelRatio || 1;

  // Scale up on high-DPI displays for better touch accuracy
  return Math.max(baseSize, baseSize * (devicePixelRatio > 1 ? 1.2 : 1));
}

/**
 * Detects if the device supports haptic feedback
 */
export function supportsHapticFeedback(): boolean {
  return 'vibrate' in navigator;
}

/**
 * Provides haptic feedback for map interactions
 */
export function triggerHapticFeedback(pattern: number | number[] = 50): void {
  if (supportsHapticFeedback()) {
    navigator.vibrate(pattern);
  }
}

/**
 * Calculates gesture velocity for map interactions
 */
export function calculateMapGestureVelocity(
  startPoint: TouchPoint,
  endPoint: TouchPoint
): number {
  const distance = Math.sqrt(
    Math.pow(endPoint.x - startPoint.x, 2) +
    Math.pow(endPoint.y - startPoint.y, 2)
  );

  const duration = endPoint.timestamp - startPoint.timestamp;

  return duration > 0 ? distance / duration : 0;
}

/**
 * Determines if a gesture should trigger map zoom
 */
export function shouldTriggerMapZoom(
  gesture: SwipeGesture,
  threshold: number = 100
): boolean {
  return gesture.distance > threshold &&
         (gesture.direction === 'up' || gesture.direction === 'down') &&
         gesture.velocity > 0.5;
}

/**
 * Calculates zoom level change based on gesture
 */
export function calculateZoomChange(gesture: SwipeGesture): number {
  const baseChange = 1;
  const velocityMultiplier = Math.min(gesture.velocity / 2, 2);

  if (gesture.direction === 'up') {
    return baseChange * velocityMultiplier; // Zoom in
  } else if (gesture.direction === 'down') {
    return -baseChange * velocityMultiplier; // Zoom out
  }

  return 0;
}

/**
 * Gets the appropriate event names for the current device
 */
export function getEventNames() {
  const isTouch = isTouchDevice();

  return {
    start: isTouch ? 'touchstart' : 'mousedown',
    move: isTouch ? 'touchmove' : 'mousemove',
    end: isTouch ? 'touchend' : 'mouseup',
    cancel: isTouch ? 'touchcancel' : 'mouseleave',
  };
}

/**
 * Creates a CSS transform string for hardware acceleration
 */
export function createTransform(x = 0, y = 0, scale = 1): string {
  return `translate3d(${x}px, ${y}px, 0) scale(${scale})`;
}

/**
 * Animates a value using requestAnimationFrame
 */
export function animateValue(
  from: number,
  to: number,
  duration: number,
  onUpdate: (value: number) => void,
  onComplete?: () => void,
  easing = (t: number) => t * t * (3 - 2 * t) // smoothstep
): () => void {
  const startTime = performance.now();
  let animationId: number;

  function animate(currentTime: number) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    const easedProgress = easing(progress);

    const currentValue = lerp(from, to, easedProgress);
    onUpdate(currentValue);

    if (progress < 1) {
      animationId = requestAnimationFrame(animate);
    } else {
      onComplete?.();
    }
  }

  animationId = requestAnimationFrame(animate);

  // Return cancel function
  return () => cancelAnimationFrame(animationId);
}
