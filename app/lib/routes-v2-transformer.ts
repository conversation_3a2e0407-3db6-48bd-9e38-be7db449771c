/**
 * Routes V2 Data Transformer
 *
 * Handles conversion between legacy Google Maps Directions API structures
 * and the new Google Routes API v2 format, maintaining backward compatibility
 * while enabling new features.
 */

import type {
  TravelRoute,
  RouteWaypoint,
  RouteLeg,
  RouteStep,
  RouteOptimizationOptions,
  VisitedPlace
} from '~/types/wanderlust';

import type {
  ComputeRoutesRequest,
  RoutesV2Response,
  RouteV2,
  RouteLegV2,
  RouteStepV2,
  WaypointV2,
  RouteModifiers
} from './google-routes-v2-service';

/**
 * Enhanced route options for Routes API v2
 */
export interface EnhancedRouteOptions extends RouteOptimizationOptions {
  routingPreference?: 'TRAFFIC_UNAWARE' | 'TRAFFIC_AWARE' | 'TRAFFIC_AWARE_OPTIMAL';
  requestedReferenceTime?: Date;
  includeAlternativeRoutes?: boolean;
  maxAlternativeRoutes?: number;
  polylineQuality?: 'HIGH_QUALITY' | 'OVERVIEW';
  computeTollInfo?: boolean;
  computeFuelConsumption?: boolean;
}

/**
 * Routes V2 Data Transformer Class
 */
export class RoutesV2Transformer {
  /**
   * Convert legacy RouteWaypoint to Routes V2 WaypointV2
   */
  static toWaypointV2(waypoint: RouteWaypoint): WaypointV2 {
    return {
      location: {
        latLng: {
          latitude: waypoint.location.lat,
          longitude: waypoint.location.lng,
        },
      },
      via: !waypoint.stopover,
      vehicleStopover: waypoint.stopover,
    };
  }

  /**
   * Convert VisitedPlace to WaypointV2
   */
  static visitedPlaceToWaypointV2(place: VisitedPlace, isStopover: boolean = true): WaypointV2 {
    return {
      location: {
        latLng: {
          latitude: place.coordinates.latitude,
          longitude: place.coordinates.longitude,
        },
      },
      via: !isStopover,
      vehicleStopover: isStopover,
    };
  }

  /**
   * Convert legacy travel mode to Routes V2 travel mode
   */
  static convertTravelMode(legacyMode: TravelRoute['travelMode']): 'DRIVE' | 'WALK' | 'BICYCLE' | 'TRANSIT' {
    const modeMap: Record<TravelRoute['travelMode'], 'DRIVE' | 'WALK' | 'BICYCLE' | 'TRANSIT'> = {
      'DRIVING': 'DRIVE',
      'WALKING': 'WALK',
      'BICYCLING': 'BICYCLE',
      'TRANSIT': 'TRANSIT',
    };

    return modeMap[legacyMode] || 'DRIVE';
  }

  /**
   * Get appropriate routing preference for travel mode
   * TRANSIT mode doesn't support routing preferences
   */
  static getRoutingPreferenceForMode(
    travelMode: TravelRoute['travelMode'],
    requestedPreference?: 'TRAFFIC_UNAWARE' | 'TRAFFIC_AWARE' | 'TRAFFIC_AWARE_OPTIMAL'
  ): 'TRAFFIC_UNAWARE' | 'TRAFFIC_AWARE' | 'TRAFFIC_AWARE_OPTIMAL' | undefined {
    // TRANSIT mode doesn't support routing preferences
    if (travelMode === 'TRANSIT') {
      return undefined;
    }

    // DRIVING mode supports all routing preferences
    if (travelMode === 'DRIVING') {
      return requestedPreference || 'TRAFFIC_AWARE';
    }

    // WALKING and BICYCLING only support TRAFFIC_UNAWARE
    if (travelMode === 'WALKING' || travelMode === 'BICYCLING') {
      return 'TRAFFIC_UNAWARE';
    }

    // Default fallback
    return requestedPreference || 'TRAFFIC_UNAWARE';
  }

  /**
   * Convert Routes V2 travel mode back to legacy format
   */
  static convertTravelModeToLegacy(v2Mode: string): TravelRoute['travelMode'] {
    const modeMap: Record<string, TravelRoute['travelMode']> = {
      'DRIVE': 'DRIVING',
      'WALK': 'WALKING',
      'BICYCLE': 'BICYCLING',
      'TRANSIT': 'TRANSIT',
    };

    return modeMap[v2Mode] || 'DRIVING';
  }

  /**
   * Convert legacy route options to Routes V2 route modifiers
   * Handles both full RouteOptimizationOptions and partial EnhancedRouteOptions
   */
  static toRouteModifiers(options: Partial<RouteOptimizationOptions> | Partial<EnhancedRouteOptions>): RouteModifiers {
    return {
      avoidTolls: options.avoidTolls ?? false,
      avoidHighways: options.avoidHighways ?? false,
      avoidFerries: options.avoidFerries ?? false,
      avoidIndoor: false, // New in v2
    };
  }

  /**
   * Create ComputeRoutesRequest from legacy parameters
   */
  static toComputeRoutesRequest(
    waypoints: RouteWaypoint[],
    travelMode: TravelRoute['travelMode'],
    options: Partial<EnhancedRouteOptions> = {}
  ): ComputeRoutesRequest {
    if (waypoints.length < 2) {
      throw new Error('At least 2 waypoints are required');
    }

    const origin = this.toWaypointV2(waypoints[0]);
    const destination = this.toWaypointV2(waypoints[waypoints.length - 1]);
    const intermediates = waypoints.length > 2
      ? waypoints.slice(1, -1).map(wp => this.toWaypointV2(wp))
      : undefined;

    // Get appropriate routing preference for the travel mode
    const routingPreference = this.getRoutingPreferenceForMode(travelMode, options.routingPreference);

    const request: ComputeRoutesRequest = {
      origin,
      destination,
      intermediates,
      travelMode: this.convertTravelMode(travelMode),
      polylineQuality: options.polylineQuality || 'HIGH_QUALITY',
      polylineEncoding: 'ENCODED_POLYLINE',
      computeAlternativeRoutes: options.includeAlternativeRoutes || false,
      routeModifiers: this.toRouteModifiers(options),
      optimizeWaypointOrder: options.optimizeWaypointOrder || false,
      languageCode: 'en',
      units: 'METRIC',
    };

    // Only set routing preference if it's supported for this travel mode
    if (routingPreference !== undefined) {
      request.routingPreference = routingPreference;
    }

    // Add departure time if specified
    if (options.departureTime) {
      request.departureTime = options.departureTime.toISOString();
    }

    // Add arrival time if specified
    if (options.arrivalTime) {
      request.arrivalTime = options.arrivalTime.toISOString();
    }

    // Add reference time for traffic-aware routing
    if (options.requestedReferenceTime) {
      request.requestedReferenceTime = options.requestedReferenceTime.toISOString();
    }

    return request;
  }

  /**
   * Create ComputeRoutesRequest from VisitedPlaces
   */
  static fromVisitedPlacesToRequest(
    places: VisitedPlace[],
    travelMode: TravelRoute['travelMode'],
    options: Partial<EnhancedRouteOptions> = {}
  ): ComputeRoutesRequest {
    if (places.length < 2) {
      throw new Error('At least 2 places are required');
    }

    const origin = this.visitedPlaceToWaypointV2(places[0]);
    const destination = this.visitedPlaceToWaypointV2(places[places.length - 1]);
    const intermediates = places.length > 2
      ? places.slice(1, -1).map(place => this.visitedPlaceToWaypointV2(place))
      : undefined;

    // Get appropriate routing preference for the travel mode
    const routingPreference = this.getRoutingPreferenceForMode(travelMode, options.routingPreference);

    const request: ComputeRoutesRequest = {
      origin,
      destination,
      intermediates,
      travelMode: this.convertTravelMode(travelMode),
      polylineQuality: options.polylineQuality || 'HIGH_QUALITY',
      polylineEncoding: 'ENCODED_POLYLINE',
      computeAlternativeRoutes: options.includeAlternativeRoutes || false,
      routeModifiers: this.toRouteModifiers(options),
      optimizeWaypointOrder: options.optimizeWaypointOrder || false,
      languageCode: 'en',
      units: 'METRIC',
      departureTime: options.departureTime?.toISOString(),
      arrivalTime: options.arrivalTime?.toISOString(),
      requestedReferenceTime: options.requestedReferenceTime?.toISOString(),
    };

    // Only set routing preference if it's supported for this travel mode
    if (routingPreference !== undefined) {
      request.routingPreference = routingPreference;
    }

    return request;
  }

  /**
   * Convert Routes V2 response to legacy TravelRoute
   */
  static fromRoutesV2Response(
    response: RoutesV2Response,
    originalWaypoints: RouteWaypoint[],
    travelMode: TravelRoute['travelMode'],
    options: Partial<EnhancedRouteOptions> = {}
  ): TravelRoute {
    if (!response.routes || response.routes.length === 0) {
      throw new Error('No routes found in response');
    }

    const primaryRoute = response.routes[0];
    return this.convertRouteV2ToTravelRoute(primaryRoute, originalWaypoints, travelMode, options);
  }

  /**
   * Convert multiple Routes V2 routes to TravelRoute array
   */
  static fromRoutesV2ResponseMultiple(
    response: RoutesV2Response,
    originalWaypoints: RouteWaypoint[],
    travelMode: TravelRoute['travelMode'],
    options: Partial<EnhancedRouteOptions> = {}
  ): TravelRoute[] {
    if (!response.routes || response.routes.length === 0) {
      throw new Error('No routes found in response');
    }

    return response.routes.map((route, index) =>
      this.convertRouteV2ToTravelRoute(route, originalWaypoints, travelMode, options, index)
    );
  }

  /**
   * Convert single RouteV2 to TravelRoute
   */
  private static convertRouteV2ToTravelRoute(
    route: RouteV2,
    originalWaypoints: RouteWaypoint[],
    travelMode: TravelRoute['travelMode'],
    _options: Partial<EnhancedRouteOptions> = {},
    routeIndex: number = 0
  ): TravelRoute {
    const routeId = `route_v2_${Date.now()}_${routeIndex}`;

    // Convert legs
    const legs: RouteLeg[] = route.legs.map(leg => this.convertRouteLegV2ToLeg(leg));

    // Calculate totals
    const totalDistance = route.distanceMeters;
    const totalDuration = this.parseDuration(route.duration);

    // Handle optimized waypoint order
    let waypoints = originalWaypoints;
    if (route.optimizedIntermediateWaypointIndex && route.optimizedIntermediateWaypointIndex.length > 0) {
      waypoints = this.reorderWaypoints(originalWaypoints, route.optimizedIntermediateWaypointIndex);
    }

    return {
      id: routeId,
      name: route.description || `Route ${routeIndex + 1}`,
      waypoints,
      optimized: !!route.optimizedIntermediateWaypointIndex,
      travelMode,
      estimatedDuration: route.localizedValues?.duration?.text || this.formatDuration(totalDuration),
      estimatedDistance: route.localizedValues?.distance?.text || this.formatDistance(totalDistance),
      polyline: route.polyline.encodedPolyline || '',
      bounds: route.viewport ? {
        north: route.viewport.high.latitude,
        south: route.viewport.low.latitude,
        east: route.viewport.high.longitude,
        west: route.viewport.low.longitude,
      } : undefined,
      legs,
      overview: {
        totalDistance: route.localizedValues?.distance?.text || this.formatDistance(totalDistance),
        totalDuration: route.localizedValues?.duration?.text || this.formatDuration(totalDuration),
        totalDurationInTraffic: route.localizedValues?.duration?.text || this.formatDuration(totalDuration),
        waypointOrder: route.optimizedIntermediateWaypointIndex,
        optimizedWaypoints: waypoints,
      },
      warnings: route.warnings || [],
      copyrights: 'Map data ©2024 Google',
      createdAt: new Date().toISOString(),
      lastOptimized: new Date().toISOString(),
    };
  }

  /**
   * Convert RouteLegV2 to legacy RouteLeg
   */
  private static convertRouteLegV2ToLeg(legV2: RouteLegV2): RouteLeg {
    const steps: RouteStep[] = legV2.steps.map(step => this.convertRouteStepV2ToStep(step));

    return {
      distance: {
        text: legV2.localizedValues?.distance?.text || this.formatDistance(legV2.distanceMeters),
        value: legV2.distanceMeters,
      },
      duration: {
        text: legV2.localizedValues?.duration?.text || this.formatDuration(this.parseDuration(legV2.duration)),
        value: this.parseDuration(legV2.duration),
      },
      duration_in_traffic: {
        text: legV2.localizedValues?.duration?.text || this.formatDuration(this.parseDuration(legV2.duration)),
        value: this.parseDuration(legV2.duration),
      },
      end_address: 'Destination', // V2 doesn't provide addresses directly
      end_location: {
        lat: legV2.endLocation.latLng.latitude,
        lng: legV2.endLocation.latLng.longitude,
      },
      start_address: 'Origin', // V2 doesn't provide addresses directly
      start_location: {
        lat: legV2.startLocation.latLng.latitude,
        lng: legV2.startLocation.latLng.longitude,
      },
      steps,
      traffic_speed_entry: [],
      via_waypoint: [],
    };
  }

  /**
   * Convert RouteStepV2 to legacy RouteStep
   */
  private static convertRouteStepV2ToStep(stepV2: RouteStepV2): RouteStep {
    return {
      distance: {
        text: stepV2.localizedValues?.distance?.text || this.formatDistance(stepV2.distanceMeters),
        value: stepV2.distanceMeters,
      },
      duration: {
        text: stepV2.localizedValues?.staticDuration?.text || this.formatDuration(this.parseDuration(stepV2.staticDuration)),
        value: this.parseDuration(stepV2.staticDuration),
      },
      end_location: {
        lat: stepV2.endLocation.latLng.latitude,
        lng: stepV2.endLocation.latLng.longitude,
      },
      html_instructions: stepV2.navigationInstruction?.instructions || 'Continue',
      polyline: {
        points: stepV2.polyline.encodedPolyline || '',
      },
      start_location: {
        lat: stepV2.startLocation.latLng.latitude,
        lng: stepV2.startLocation.latLng.longitude,
      },
      travel_mode: stepV2.travelMode || 'DRIVING',
      maneuver: stepV2.navigationInstruction?.maneuver,
    };
  }

  /**
   * Reorder waypoints based on optimization result
   */
  private static reorderWaypoints(
    originalWaypoints: RouteWaypoint[],
    optimizedOrder: number[]
  ): RouteWaypoint[] {
    if (originalWaypoints.length <= 2) return originalWaypoints;

    const origin = originalWaypoints[0];
    const destination = originalWaypoints[originalWaypoints.length - 1];
    const intermediates = originalWaypoints.slice(1, -1);

    const reorderedIntermediates = optimizedOrder.map(index => intermediates[index]);

    return [origin, ...reorderedIntermediates, destination];
  }

  /**
   * Parse duration string (e.g., "1234s") to seconds
   */
  private static parseDuration(duration: string): number {
    const match = duration.match(/^(\d+)s?$/);
    return match ? parseInt(match[1], 10) : 0;
  }

  /**
   * Format duration in seconds to human-readable string
   */
  private static formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours} hr ${minutes} min`;
    }
    return `${minutes} min`;
  }

  /**
   * Format distance in meters to human-readable string
   */
  private static formatDistance(meters: number): string {
    if (meters >= 1000) {
      const km = (meters / 1000).toFixed(1);
      return `${km} km`;
    }
    return `${meters} m`;
  }
}

/**
 * Utility functions for working with Routes V2 data
 */
export const RoutesV2Utils = {
  /**
   * Check if a response contains alternative routes
   */
  hasAlternativeRoutes(response: RoutesV2Response): boolean {
    return response.routes && response.routes.length > 1;
  },

  /**
   * Get the fastest route from multiple routes
   */
  getFastestRoute(response: RoutesV2Response): RouteV2 | null {
    if (!response.routes || response.routes.length === 0) return null;

    return response.routes.reduce((fastest, current) => {
      const fastestDuration = this.parseDuration(fastest.duration);
      const currentDuration = this.parseDuration(current.duration);
      return currentDuration < fastestDuration ? current : fastest;
    });
  },

  /**
   * Get the shortest route from multiple routes
   */
  getShortestRoute(response: RoutesV2Response): RouteV2 | null {
    if (!response.routes || response.routes.length === 0) return null;

    return response.routes.reduce((shortest, current) => {
      return current.distanceMeters < shortest.distanceMeters ? current : shortest;
    });
  },

  /**
   * Parse duration string to seconds
   */
  parseDuration(duration: string): number {
    const match = duration.match(/^(\d+)s?$/);
    return match ? parseInt(match[1], 10) : 0;
  },

  /**
   * Calculate estimated toll cost from route
   */
  getEstimatedTollCost(route: RouteV2): number {
    if (!route.travelAdvisory?.tollInfo?.estimatedPrice) return 0;

    return route.travelAdvisory.tollInfo.estimatedPrice.reduce((total, price) => {
      const units = parseInt(price.units) || 0;
      const nanos = price.nanos || 0;
      return total + units + (nanos / 1000000000);
    }, 0);
  },
};
