/**
 * Performance and Load Testing for Routes API v2
 * 
 * Tests performance characteristics, memory usage, and scalability
 * of the Routes V2 implementation under various load conditions.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { GoogleRoutesV2Service, createRoutesV2Service } from '../google-routes-v2-service';
import { RoutesV2Transformer } from '../routes-v2-transformer';
import { calculateRoute, calculateEnhancedRoute, getRouteAlternatives } from '../route-planning-utils';
import type { RouteWaypoint } from '~/types/wanderlust';

// Performance test configuration
const PERFORMANCE_THRESHOLDS = {
  singleRouteCalculation: 2000, // 2 seconds
  multipleRouteCalculation: 5000, // 5 seconds
  cacheHitResponse: 100, // 100ms
  memoryLeakThreshold: 50 * 1024 * 1024, // 50MB
  maxConcurrentRequests: 10,
};

// Mock fetch with realistic delays
const mockFetchWithDelay = (delay: number = 500) => {
  return vi.fn().mockImplementation(() => 
    new Promise(resolve => 
      setTimeout(() => resolve({
        ok: true,
        json: () => Promise.resolve({
          routes: [{
            legs: [{
              distanceMeters: 15000,
              duration: '1200s',
              staticDuration: '1200s',
              polyline: { encodedPolyline: 'mock_polyline_data' },
              startLocation: { latLng: { latitude: 36.1627, longitude: -86.7816 } },
              endLocation: { latLng: { latitude: 36.1263, longitude: -86.6782 } },
              steps: [],
              localizedValues: {
                distance: { text: '15.0 km' },
                duration: { text: '20 min' },
              },
            }],
            distanceMeters: 15000,
            duration: '1200s',
            staticDuration: '1200s',
            polyline: { encodedPolyline: 'mock_polyline_data' },
            description: 'Test Route',
            warnings: [],
            viewport: {
              low: { latitude: 36.1, longitude: -86.8 },
              high: { latitude: 36.2, longitude: -86.7 },
            },
            travelAdvisory: {},
            localizedValues: {
              distance: { text: '15.0 km' },
              duration: { text: '20 min' },
            },
          }],
        }),
      }), delay)
    )
  );
};

// Generate test waypoints
const generateWaypoints = (count: number): RouteWaypoint[] => {
  return Array(count).fill(null).map((_, index) => ({
    location: { 
      lat: 36.1627 + (index * 0.01), 
      lng: -86.7816 + (index * 0.01) 
    },
    stopover: index > 0 && index < count - 1,
    placeId: `waypoint-${index}`,
  }));
};

// Memory usage tracking
describe('Routes V2 Performance Tests', () => {
  let service: GoogleRoutesV2Service;
  let mockFetch: ReturnType<typeof mockFetchWithDelay>;

  beforeEach(() => {
    (global as any).google = {
      maps: {
        DirectionsService: vi.fn().mockImplementation(() => ({
          route: vi.fn((request, callback) => {
            callback(
              {
                routes: [
                  {
                    legs: [
                      {
                        distance: { text: '1 km', value: 1000 },
                        duration: { text: '1 min', value: 60 },
                        start_address: 'Mock Start Address',
                        end_address: 'Mock End Address',
                        start_location: { lat: () => 0, lng: () => 0 },
                        end_location: { lat: () => 0, lng: () => 0 },
                        steps: [],
                      },
                    ],
                    overview_polyline: { points: '' },
                    bounds: {
                      getNorthEast: () => ({ lat: () => 0, lng: () => 0 }),
                      getSouthWest: () => ({ lat: () => 0, lng: () => 0 }),
                    },
                    warnings: [],
                    copyrights: 'Mock Copyrights',
                  },
                ],
                status: 'OK',
              },
              'OK'
            );
          }),
        })),
        DirectionsRenderer: vi.fn().mockImplementation(() => ({
          setMap: vi.fn(),
          setDirections: vi.fn(),
        })),
        LatLng: vi.fn().mockImplementation((lat, lng) => ({
          lat: () => lat,
          lng: () => lng,
        })),
        TravelMode: {
          DRIVING: 'DRIVING',
        },
        UnitSystem: {
          METRIC: 0,
        },
        DirectionsStatus: {
          OK: 'OK',
        },
      },
    };
    vi.clearAllMocks();
    service = createRoutesV2Service({
      apiKey: 'test-api-key',
      endpoint: 'https://routes.googleapis.com/directions/v2:computeRoutes',
    });
    mockFetch = mockFetchWithDelay(500);
    global.fetch = mockFetch;
  });

  afterEach(() => {
    service.clearCache();
  });

  describe('Single Route Calculation Performance', () => {
    it('should calculate simple route within performance threshold', async () => {
      const waypoints = generateWaypoints(2);
      const startTime = performance.now();
      
      await calculateRoute(waypoints, 'DRIVING');
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.singleRouteCalculation);
    });

    it('should handle complex routes with multiple waypoints efficiently', async () => {
      const waypoints = generateWaypoints(10); // Complex route
      const startTime = performance.now();
      
      await calculateRoute(waypoints, 'DRIVING');
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.singleRouteCalculation);
    });

    it('should perform enhanced route calculation within threshold', async () => {
      const waypoints = generateWaypoints(5);
      const startTime = performance.now();
      
      await calculateEnhancedRoute(waypoints, 'DRIVING', {
        routingPreference: 'TRAFFIC_AWARE',
        includeAlternativeRoutes: true,
        maxAlternativeRoutes: 3,
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.singleRouteCalculation);
    });
  });

  describe('Caching Performance', () => {
    it('should serve cached responses quickly', async () => {
      const waypoints = generateWaypoints(3);
      
      // First request (cache miss)
      await calculateRoute(waypoints, 'DRIVING');
      
      // Second request (cache hit)
      const startTime = performance.now();
      await calculateRoute(waypoints, 'DRIVING');
      const endTime = performance.now();
      
      const duration = endTime - startTime;
      expect(duration).toBeLessThan(PERFORMANCE_THRESHOLDS.cacheHitResponse);
    });

    it('should handle cache eviction gracefully', async () => {
      // Fill cache beyond capacity
      const requests = Array(150).fill(null).map((_, index) => 
        generateWaypoints(2).map(wp => ({
          ...wp,
          placeId: `${wp.placeId}-${index}`,
        }))
      );

      // Execute all requests
      for (const waypoints of requests) {
        await calculateRoute(waypoints, 'DRIVING');
      }

      // Cache should still function
      const stats = service.getCacheStats();
      expect(stats.size).toBeLessThanOrEqual(100); // Max cache size
    });

    it('should maintain performance with cache operations', async () => {
      const waypoints = generateWaypoints(3);
      const iterations = 50;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        await calculateRoute(waypoints, 'DRIVING');
      }
      
      const endTime = performance.now();
      const averageTime = (endTime - startTime) / iterations;
      
      // Average time should be very low due to caching
      expect(averageTime).toBeLessThan(50); // 50ms average
    });
  });

  describe('Concurrent Request Handling', () => {
    it('should handle multiple concurrent requests', async () => {
      const requests = Array(PERFORMANCE_THRESHOLDS.maxConcurrentRequests)
        .fill(null)
        .map((_, index) => 
          calculateRoute(generateWaypoints(2), 'DRIVING')
        );

      const startTime = performance.now();
      const results = await Promise.all(requests);
      const endTime = performance.now();
      
      expect(results).toHaveLength(PERFORMANCE_THRESHOLDS.maxConcurrentRequests);
      expect(endTime - startTime).toBeLessThan(PERFORMANCE_THRESHOLDS.multipleRouteCalculation);
    });

    it('should handle mixed request types concurrently', async () => {
      const waypoints = generateWaypoints(4);
      
      const requests = [
        calculateRoute(waypoints, 'DRIVING'),
        calculateRoute(waypoints, 'WALKING'),
        calculateEnhancedRoute(waypoints, 'DRIVING', { routingPreference: 'TRAFFIC_AWARE' }),
        getRouteAlternatives(waypoints, 'DRIVING', 3),
      ];

      const startTime = performance.now();
      const results = await Promise.all(requests);
      const endTime = performance.now();
      
      expect(results).toHaveLength(4);
      expect(endTime - startTime).toBeLessThan(PERFORMANCE_THRESHOLDS.multipleRouteCalculation);
    });
  });

  describe('Memory Usage and Leak Detection', () => {
    // Removed memory usage test for Node.js compatibility
    // it('should not leak memory during repeated calculations', async () => {
    //   const initialMemory = getMemoryUsage();
    //   if (!initialMemory) {
    //     console.warn('Memory testing not available in this environment');
    //     return;
    //   }

    //   const waypoints = generateWaypoints(5);
    //   const iterations = 100;
      
    //   for (let i = 0; i < iterations; i++) {
    //     await calculateRoute(waypoints, 'DRIVING');
        
    //     // Clear cache periodically to test cleanup
    //     if (i % 20 === 0) {
    //       service.clearCache();
    //     }
    //   }

    //   // Force garbage collection if available
    //   if (global.gc) {
    //     global.gc();
    //   }

    //   const finalMemory = getMemoryUsage();
    //   const memoryIncrease = finalMemory!.used - initialMemory.used;
      
    //   expect(memoryIncrease).toBeLessThan(PERFORMANCE_THRESHOLDS.memoryLeakThreshold);
    // });

    it('should clean up resources properly', async () => {
      const waypoints = generateWaypoints(3);
      
      // Create multiple service instances
      const services = Array(10).fill(null).map(() => 
        createRoutesV2Service({
          apiKey: 'test-key',
          endpoint: 'https://routes.googleapis.com/directions/v2:computeRoutes',
        })
      );

      // Use each service
      for (const svc of services) {
        const request = RoutesV2Transformer.toComputeRoutesRequest(waypoints, 'DRIVING');
        await svc.computeRoutes(request);
      }

      // Clear all caches
      services.forEach(svc => svc.clearCache());

      // All caches should be empty
      services.forEach(svc => {
        const stats = svc.getCacheStats();
        expect(stats.size).toBe(0);
      });
    });
  });

  describe('Rate Limiting Performance', () => {
    it('should handle rate limiting without blocking', async () => {
      const waypoints = generateWaypoints(2);
      
      // Create requests that would exceed rate limit
      const requests = Array(350).fill(null).map(() => 
        calculateRoute(waypoints, 'DRIVING').catch(() => null)
      );

      const startTime = performance.now();
      const results = await Promise.allSettled(requests);
      const endTime = performance.now();
      
      // Should complete within reasonable time even with rate limiting
      expect(endTime - startTime).toBeLessThan(10000); // 10 seconds
      
      // Some requests should succeed, some should be rate limited
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      
      expect(successful).toBeGreaterThan(0);
      expect(failed).toBeGreaterThan(0);
    });

    it('should recover from rate limiting', async () => {
      const waypoints = generateWaypoints(2);
      
      // Trigger rate limiting
      const rapidRequests = Array(350).fill(null).map(() => 
        calculateRoute(waypoints, 'DRIVING').catch(() => null)
      );
      
      await Promise.allSettled(rapidRequests);
      
      // Wait for rate limit reset (simulate)
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Should be able to make requests again
      const result = await calculateRoute(waypoints, 'DRIVING');
      expect(result).toBeDefined();
    });
  });

  describe('Data Transformation Performance', () => {
    it('should transform large datasets efficiently', () => {
      const largeWaypointSet = generateWaypoints(25); // Maximum allowed
      
      const startTime = performance.now();
      const request = RoutesV2Transformer.toComputeRoutesRequest(
        largeWaypointSet,
        'DRIVING',
        {
          routingPreference: 'TRAFFIC_AWARE',
          includeAlternativeRoutes: true,
          maxAlternativeRoutes: 3,
        }
      );
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100); // 100ms for transformation
      expect(request.intermediates).toHaveLength(23); // 25 - 2 (origin/destination)
    });

    it('should handle response transformation efficiently', () => {
      const mockResponse = {
        routes: Array(3).fill(null).map((_, index) => ({
          legs: Array(10).fill(null).map((_, legIndex) => ({
            distanceMeters: 1000 + legIndex * 500,
            duration: `${600 + legIndex * 300}s`,
            staticDuration: `${600 + legIndex * 300}s`,
            polyline: { encodedPolyline: `polyline_${legIndex}` },
            startLocation: { latLng: { latitude: 36.1 + legIndex * 0.01, longitude: -86.8 + legIndex * 0.01 } },
            endLocation: { latLng: { latitude: 36.1 + (legIndex + 1) * 0.01, longitude: -86.8 + (legIndex + 1) * 0.01 } },
            steps: [],
            localizedValues: {
              distance: { text: `${1 + legIndex * 0.5} km` },
              duration: { text: `${10 + legIndex * 5} min` },
            },
          })),
          distanceMeters: 15000 + index * 2000,
          duration: `${1200 + index * 300}s`,
          staticDuration: `${1200 + index * 300}s`,
          polyline: { encodedPolyline: `route_polyline_${index}` },
          description: `Route ${index + 1}`,
          warnings: [],
          viewport: {
            low: { latitude: 36.1, longitude: -86.8 },
            high: { latitude: 36.2, longitude: -86.7 },
          },
          travelAdvisory: {},
          localizedValues: {
            distance: { text: `${15 + index * 2} km` },
            duration: { text: `${20 + index * 5} min` },
          },
        })),
      };

      const waypoints = generateWaypoints(11);
      
      const startTime = performance.now();
      const routes = RoutesV2Transformer.fromRoutesV2ResponseMultiple(
        mockResponse,
        waypoints,
        'DRIVING'
      );
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(200); // 200ms for complex transformation
      expect(routes).toHaveLength(3);
      expect(routes[0].legs).toHaveLength(10);
    });
  });

  describe('Scalability Tests', () => {
    it('should scale with increasing waypoint complexity', async () => {
      const waypointCounts = [2, 5, 10, 15, 20, 25];
      const results: number[] = [];
      
      for (const count of waypointCounts) {
        const waypoints = generateWaypoints(count);
        const startTime = performance.now();
        
        await calculateRoute(waypoints, 'DRIVING');
        
        const endTime = performance.now();
        results.push(endTime - startTime);
      }
      
      // Performance should scale reasonably (not exponentially)
      const firstResult = results[0];
      const lastResult = results[results.length - 1];
      
      // Last result should not be more than 5x the first result
      expect(lastResult).toBeLessThan(firstResult * 5);
    });

    it('should maintain performance across different travel modes', async () => {
      const waypoints = generateWaypoints(5);
      const travelModes = ['DRIVING', 'WALKING', 'BICYCLING', 'TRANSIT'] as const;
      const results: number[] = [];
      
      for (const mode of travelModes) {
        const startTime = performance.now();
        await calculateRoute(waypoints, mode);
        const endTime = performance.now();
        
        results.push(endTime - startTime);
      }
      
      // All travel modes should perform similarly
      const maxTime = Math.max(...results);
      const minTime = Math.min(...results);
      
      expect(maxTime - minTime).toBeLessThan(1000); // Within 1 second of each other
    });
  });
});
