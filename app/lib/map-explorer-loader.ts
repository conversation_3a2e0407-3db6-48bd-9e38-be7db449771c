import { loadWanderlustData } from '~/lib/wanderlust-loader';
import type { LoaderFunctionArgs } from 'react-router';

export async function loadData({}: LoaderFunctionArgs) {
  try {
    const regions = await loadWanderlustData();

    return {
      regions,
      success: true,
    };
  } catch (error) {
    console.error('Failed to load visited places data:', error);
    return {
      regions: [],
      success: false,
      error: 'Failed to load travel data',
    };
  }
}