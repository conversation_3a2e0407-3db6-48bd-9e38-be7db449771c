export interface TripEvent {
  id: string;
  time: string;
  desc: string;
  details?: string[];
  timezone?: string;
}

export interface ItineraryDay {
  id: string;
  date: string;
  events: TripEvent[];
}

export interface WeatherInfo {
  date: string;
  temperature: number;
  condition: string;
  icon: string;
}

export interface TravelRoute {
  id: string;
  estimatedDuration: string;
  estimatedDistance: string;
  estimatedCost?: number;
  trafficLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
  ecoScore?: number;
  warnings?: string[];
  polyline?: string; // Optional: Encoded polyline for the route
}

export interface TravelEssential {
  id: string;
  name: string;
  category: string;
  packed: boolean;
}

export interface Overview {
  dates: string;
  travelers: string;
  locations: string;
}

export interface TripInformation {
  overview: {
    title: string;
    location: string;
    participants: string[];
    duration: number;
  };
  dates: string;
  itinerary: ItineraryDay[];
  essentials: TravelEssential[];
  destination: string;
  startDate: string;
  endDate: string;
  weather: WeatherInfo[];
  travelers: number;
}

export type TimelineColor = 'primary' | 'secondary' | 'muted' | 'accent' | 'destructive';

export interface VisitedPlace {
  id: string;
  name: string;
  description: {
    en: string;
    fr: string;
    ar: string;
  };
  coordinates: {
    latitude: number;
    longitude: number;
  };
  category: string; // Consider making this a more specific enum if possible
  rating?: number;
  visitDate?: string; // Make optional based on useEnhancedPlaceSearch
  icon?: string;
  city: string;
  region: string;
  revisitPotential?: 'High' | 'Medium' | 'Low' | 'Worth a Look' | 'Highly Recommend' | 'Skip'; // Added missing types
  // Add any other relevant properties from your data sources
}

export interface CityRegion {
  id: string;
  name: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  radius: number; // in meters
  description?: string; // Added based on usePlaceSearch
  icon?: string; // Added based on usePlaceSearch
  bounds?: { // Added based on usePlaceSearch
    north: number;
    south: number;
    east: number;
    west: number;
  };
  center?: { // Added based on usePlaceSearch
    lat: number;
    lng: number;
  };
  places?: VisitedPlace[]; // Added based on usePlaceSearch
}
 
export interface Activity {
  id: string;
  type: 'transport' | 'match' | 'meal' | 'hotel' | 'activity';
  title: string;
  time: string;
  duration?: string;
  location?: Location;
  transport?: TransportDetails;
  notes?: string;
  attachments?: string[];
  status: ActivityStatus;
  important?: boolean;
  requiresConfirmation?: boolean;
  isGroupEvent?: boolean;
}

export interface VisitedPlace {
  id: string;
  name: string;
  description: {
    en: string;
    fr: string;
    ar: string;
  };
  coordinates: {
    latitude: number;
    longitude: number;
  };
  category: 'food' | 'landmark' | 'museum' | 'park' | 'accommodation' | 'transport' | 'entertainment' | 'shopping' | string; // Allow string for now, but ideally use a strict enum
  rating?: number;
  visitDate?: string; // Make optional based on useEnhancedPlaceSearch
  icon?: string;
  city: string;
  region: string;
  revisitPotential?: 'High' | 'Medium' | 'Low' | 'Worth a Look' | 'Highly Recommend' | 'Skip'; // Added missing types
  // Add any other relevant properties from your data sources
}

export interface CityRegion {
  id: string;
  name: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  radius: number; // in meters
  description?: string; // Added based on usePlaceSearch
  icon?: string; // Added based on usePlaceSearch
  bounds?: { // Added based on usePlaceSearch
    north: number;
    south: number;
    east: number;
    west: number;
  };
  center?: { // Added based on usePlaceSearch
    lat: number;
    lng: number;
  };
  places?: VisitedPlace[]; // Added based on usePlaceSearch
}
 
export type ActivityStatus = 'pending' | 'confirmed' | 'completed';

export interface Location {
  name: string;
  lat: number;
  lng: number;
  address?: string;
  contact?: string;
  confirmationNumber?: string;
  website?: string;
  mapUrl?: string;
  venueType?: string;
}

export interface TransportDetails {
  mode: string;
  carrier?: string;
  bookingReference?: string;
  seatMap?: { [key: string]: string };
  pickup_time?: string;
  pickup_location?: string;
  estimated_cost?: number;
  shared_with?: string[];
  notes?: string;
}

export interface DayProgram {
  date: string;
  title: string;
  summary: string;
  weather?: WeatherInfo;
  reminders?: string[];
  docs?: string[];
  items: Activity[];
  _source?: 'supabase' | 'mock' | 'standardized' | 'offline'; // Indicates where the data came from
  _additionalData?: Record<string, any>; // Additional data that doesn't fit in the standard schema
}

export interface ItineraryState {
  currentItinerary: DayProgram | null;
}
export interface ItineraryData {
  date: string;
  title: string;
  summary: string;
  weather: {
    forecast: string;
    temperature: string;
    wind: string;
    icon: string;
  };
  reminders: string[];
  docs: string[];
  items: Activity[];
}

// Add HTMLMotionProps type for Framer Motion
import type { HTMLMotionProps as FramerHTMLMotionProps } from 'framer-motion';
export type HTMLMotionProps<T extends string> = any;