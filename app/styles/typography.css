/**
 * World Cup Itinerary Typography System
 * Implements fluid typography with responsive sizing
 */

:root {
  /* Font families */
  --font-sans: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-heading: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  
  /* Font sizes using fluid typography - scales smoothly between viewport sizes */
  --font-size-xs: clamp(0.75rem, calc(0.7rem + 0.25vw), 0.875rem);
  --font-size-sm: clamp(0.875rem, calc(0.8rem + 0.375vw), 1rem);
  --font-size-base: clamp(1rem, calc(0.925rem + 0.375vw), 1.125rem);
  --font-size-lg: clamp(1.125rem, calc(1rem + 0.625vw), 1.25rem);
  --font-size-xl: clamp(1.25rem, calc(1.125rem + 0.625vw), 1.5rem);
  --font-size-2xl: clamp(1.5rem, calc(1.25rem + 1.25vw), 2rem);
  --font-size-3xl: clamp(1.875rem, calc(1.5rem + 1.875vw), 2.5rem);
  --font-size-4xl: clamp(2.25rem, calc(1.75rem + 2.5vw), 3rem);
  
  /* Line heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-loose: 1.75;
  
  /* Font weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Letter spacing */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;

  /* Paragraph spacing */
  --paragraph-spacing: 1.5em;
}

/* Base HTML element styling */
html {
  font-family: var(--font-sans);
  font-size: 16px;
  line-height: var(--line-height-normal);
  color: var(--color-foreground);
}

body {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

/* Heading styles */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

/* Paragraph styles */
p {
  margin-bottom: var(--paragraph-spacing);
}

/* Link styles */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

/* Utility classes for typography */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-loose { line-height: var(--line-height-loose); }

.tracking-tight { letter-spacing: var(--letter-spacing-tight); }
.tracking-normal { letter-spacing: var(--letter-spacing-normal); }
.tracking-wide { letter-spacing: var(--letter-spacing-wide); }

/* Tailwind-style utility classes */
.text-heading-1 { 
  font-size: var(--font-size-4xl); 
  font-weight: var(--font-weight-bold); 
  line-height: var(--line-height-tight); 
  letter-spacing: var(--letter-spacing-tight);
}

.text-heading-2 { 
  font-size: var(--font-size-3xl); 
  font-weight: var(--font-weight-bold); 
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
}

.text-heading-3 { 
  font-size: var(--font-size-2xl); 
  font-weight: var(--font-weight-semibold); 
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
}

.text-heading-4 { 
  font-size: var(--font-size-xl); 
  font-weight: var(--font-weight-semibold); 
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-normal);
}

.text-body-lg { 
  font-size: var(--font-size-lg); 
  line-height: var(--line-height-normal);
}

.text-body { 
  font-size: var(--font-size-base); 
  line-height: var(--line-height-normal);
}

.text-body-sm { 
  font-size: var(--font-size-sm); 
  line-height: var(--line-height-normal);
}

.text-caption { 
  font-size: var(--font-size-xs); 
  line-height: var(--line-height-tight);
}

/* RTL Support - applied when dir="rtl" is set on html element */
html[dir="rtl"] {
  /* Adjust text alignment for RTL languages */
  text-align: right;
}

/* Theme Support - Dark Mode */
@media (prefers-color-scheme: dark) {
  :root {
    --color-foreground: hsl(0, 0%, 98%);
    --color-foreground-secondary: hsl(0, 0%, 80%);
    --color-foreground-tertiary: hsl(0, 0%, 65%);
    --color-foreground-quaternary: hsl(0, 0%, 50%);
    --color-foreground-muted: hsl(0, 0%, 40%);
    --color-foreground-disabled: hsl(0, 0%, 30%);
    
    --color-background: hsl(222, 47%, 11%);
    --color-background-secondary: hsl(223, 47%, 13%);
    --color-background-tertiary: hsl(223, 47%, 15%);
    
    /* Adjust other colors for dark mode if needed */
    --color-primary-light: hsl(350, 90%, 40%);
    --color-secondary-light: hsl(205, 100%, 45%);
    --color-accent-light: hsl(40, 95%, 50%);
  }
}