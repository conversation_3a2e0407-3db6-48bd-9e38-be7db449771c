/* FIFA Club World Cup 2025™ Header Design System Styles */

/* Core Brand Colors - Hardcoded hex values for cross-browser compatibility */
:root {
  --brand-gold: #FFD700;
  --brand-red: #DC2626;
  --brand-black: #000000;
  --brand-white: #FFFFFF;
}

/* Header Component Styles */
.header-base {
  position: sticky;
  top: 0;
  z-index: 50;
  width: 100%;
  transition: all 0.5s ease-out;
}

.header-default {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-scrolled {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Glassmorphism Effects */
.glass-premium {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Logo Styles */
.header-logo-container {
  transition: all 0.3s ease;
}

.header-logo-container:hover {
  transform: scale(1.05);
}

.header-logo-icon {
  transition: all 0.3s ease;
}

.header-logo-title {
  font-weight: 700;
  font-size: 1.125rem;
  transition: color 0.3s ease;
}

.header-logo-subtitle {
  font-size: 0.75rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* Navigation Trigger Styles */
.nav-trigger {
  height: 2.5rem;
  gap: 0.5rem;
  padding: 0 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.nav-trigger:hover {
  transform: scale(1.05);
}

/* Navigation Badge Styles */
.nav-badge {
  margin-left: 0.25rem;
  padding: 0 0.375rem;
  height: 1rem;
  font-size: 0.625rem;
  font-weight: 500;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Navigation Content Styles */
.nav-content {
  width: 31.25rem;
  padding: 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-featured-link {
  transition: all 0.3s ease;
}

.nav-featured-link:hover {
  transform: scale(1.05);
}

/* Quick Link Styles */
.nav-quick-link {
  transition: all 0.2s ease;
}

.nav-quick-link:hover {
  transform: translateX(0.25rem);
}

.nav-quick-icon {
  transition: all 0.3s ease;
}

.nav-quick-icon:hover {
  transform: scale(1.1);
}

/* Utility Button Styles */
.header-utilities {
  gap: 0.5rem;
}

.header-search-button,
.header-notifications,
.header-settings {
  height: 2.25rem;
  width: 2.25rem;
  transition: all 0.3s ease;
}

.header-search-button:hover,
.header-notifications:hover,
.header-settings:hover {
  transform: scale(1.1);
}

.header-settings:hover {
  transform: scale(1.1) rotate(90deg);
}

.header-search-input {
  width: 16rem;
  height: 2.25rem;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.header-search-close {
  height: 2.25rem;
  padding: 0 0.5rem;
}

/* User Navigation Styles */
.user-nav-trigger {
  height: 2rem;
  width: 2rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.user-nav-trigger:hover {
  transform: scale(1.1);
}

.user-nav-content {
  width: 14rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-nav-item {
  transition: all 0.2s ease;
}

/* Language Switcher Styles */
.lang-switcher-trigger {
  transition: all 0.3s ease;
}

.lang-switcher-trigger:hover {
  transform: scale(1.1);
}

.lang-switcher-content {
  width: 14rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.lang-menu-item {
  transition: all 0.2s ease;
}

/* Mobile Navigation Styles */
.mobile-nav-trigger {
  transition: all 0.3s ease;
}

.mobile-nav-trigger:hover {
  transform: scale(1.1);
}

.mobile-nav-content {
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-nav-quick-action {
  transition: all 0.2s ease;
}

/* Animation Keyframes */
@keyframes float {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg); 
  }
  33% { 
    transform: translateY(-10px) rotate(120deg); 
  }
  66% { 
    transform: translateY(10px) rotate(240deg); 
  }
}

@keyframes gradient-shift {
  0%, 100% { 
    background-position: 0% 50%; 
  }
  50% { 
    background-position: 100% 50%; 
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    filter: brightness(1) drop-shadow(0 0 8px #FFD700);
  }
  50% {
    opacity: 0.8;
    filter: brightness(1.2) drop-shadow(0 0 16px #FFD700);
  }
}

/* Utility Classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Text Gradient Implementation */
.text-gradient-gold {
  background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  animation: gradient-shift 4s ease infinite;
}

/* Fallback for unsupported browsers */
@supports not (-webkit-background-clip: text) {
  .text-gradient-gold {
    background: none;
    color: #FFD700;
    text-shadow:
      0 0 10px rgba(255, 215, 0, 0.5),
      0 0 20px rgba(220, 38, 38, 0.3);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-logo-title {
    font-size: 1rem;
  }
  
  .header-logo-subtitle {
    font-size: 0.625rem;
  }
  
  .nav-content {
    width: 100%;
    max-width: 20rem;
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  .header-base,
  .nav-trigger,
  .header-search-button,
  .header-notifications,
  .header-settings,
  .user-nav-trigger,
  .lang-switcher-trigger,
  .mobile-nav-trigger {
    transition: none;
  }
  
  .animate-float,
  .animate-gradient-shift,
  .animate-pulse-glow {
    animation: none;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .header-default {
    background: #FFD700;
    color: #000000;
  }
  
  .header-scrolled {
    background: #FFFFFF;
    color: #000000;
    border-bottom: 2px solid #000000;
  }
}
