import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import WanderlustExplorer from '../../wanderlust';

// Mock the loader data
const mockLoaderData = {
  regions: [
    {
      id: 'test-region',
      name: 'Test Region',
      places: [],
      coordinates: { latitude: 40.7128, longitude: -74.0060 }
    }
  ],
  success: true,
  error: null
};

// Mock React Router
vi.mock('react-router', () => ({
  useLoaderData: () => mockLoaderData,
}));

// Mock Zustand store
vi.mock('~/stores/wanderlust', () => ({
  useWanderlustStore: () => ({
    loadRegionData: vi.fn(),
    currentRegion: 'test-region',
    isLoading: false,
    error: null,
    setError: vi.fn(),
    setLoading: vi.fn(),
    filteredPlaces: [],
    mapCenter: { lat: 40.7128, lng: -74.0060 },
    mapZoom: 10,
    selectedPlace: null,
    setSelectedPlace: vi.fn(),
    setMapCenter: vi.fn(),
    setMapZoom: vi.fn(),
    setCurrentRegion: vi.fn(),
  }),
}));

// Mock components
vi.mock('~/components/wanderlust/WanderlustHeader', () => ({
  WanderlustHeader: () => <div data-testid="wanderlust-header">Header</div>,
}));

vi.mock('~/components/wanderlust/CityHub', () => ({
  CityHub: () => <div data-testid="city-hub">City Hub</div>,
}));

vi.mock('~/components/wanderlust/ClientOnlyMap', () => ({
  ClientOnlyMap: () => <div data-testid="client-only-map">Map</div>,
}));

vi.mock('~/components/wanderlust/ExplorerLog', () => ({
  ExplorerLog: () => <div data-testid="explorer-log">Explorer Log</div>,
}));

vi.mock('~/components/wanderlust/DirectionsPanel', () => ({
  DirectionsPanel: () => <div data-testid="directions-panel">Directions</div>,
}));

vi.mock('~/components/wanderlust/NotificationSystem', () => ({
  NotificationSystem: () => <div data-testid="notification-system">Notifications</div>,
}));

vi.mock('~/components/wanderlust/HydrationSafeWrapper', () => ({
  HydrationSafeWrapper: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

describe('WanderlustExplorer Layout', () => {
  it('renders main layout components', () => {
    render(<WanderlustExplorer />);

    expect(screen.getByTestId('wanderlust-header')).toBeInTheDocument();
    expect(screen.getByTestId('city-hub')).toBeInTheDocument();
    expect(screen.getByTestId('client-only-map')).toBeInTheDocument();
    expect(screen.getByTestId('explorer-log')).toBeInTheDocument();
    expect(screen.getByTestId('directions-panel')).toBeInTheDocument();
    expect(screen.getByTestId('notification-system')).toBeInTheDocument();
  });

  it('has mobile-first layout structure', () => {
    render(<WanderlustExplorer />);

    // Check for mobile-first layout classes
    const mainContent = screen.getByTestId('client-only-map').closest('.flex');
    expect(mainContent).toHaveClass('flex-col', 'lg:flex-row');

    // Check map container has mobile height
    const mapContainer = screen.getByTestId('client-only-map').parentElement;
    expect(mapContainer).toHaveClass('h-[50vh]', 'lg:h-[calc(100vh-200px)]');

    // Check explorer log has mobile ordering
    const explorerContainer = screen.getByTestId('explorer-log').parentElement;
    expect(explorerContainer).toHaveClass('order-1', 'lg:order-2');
  });

  it('has correct ordering for mobile vs desktop', () => {
    render(<WanderlustExplorer />);

    // Map should be order-2 on mobile, order-1 on desktop
    const mapContainer = screen.getByTestId('client-only-map').parentElement;
    expect(mapContainer).toHaveClass('order-2', 'lg:order-1');

    // Explorer Log should be order-1 on mobile, order-2 on desktop
    const explorerContainer = screen.getByTestId('explorer-log').parentElement;
    expect(explorerContainer).toHaveClass('order-1', 'lg:order-2');
  });
});
