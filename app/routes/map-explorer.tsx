import React from 'react';
import { useLoaderData } from 'react-router';
import type { LoaderFunctionArgs } from 'react-router';
import { MapExplorerComponent } from '~/components/shared/MapExplorer';
import { useMapExplorer } from '~/hooks/useMapExplorer';
import { loadData } from '~/lib/map-explorer-loader';
import { MapPinIcon, GlobeAltIcon, ArrowPathIcon } from '@heroicons/react/24/outline'; // Example icon imports
import '~/styles/map-explorer.css'; // Import the CSS file

export default function MapExplorerRoute() {
  const { mode, setMode } = useMapExplorer();
  const { regions, success, error } = useLoaderData<typeof loadData>();

  return (
    <div className="map-explorer-container"> {/* Added main container for layout */}
      {/* State Feedback */}
      {error && (
        <div className="error-banner" role="alert">
          <p>Failed to load map data. Please try again.</p>
        </div>
      )}
      {!success && !error && ( // Show loading unless success or error is true
        <div className="loading-spinner" aria-live="polite">
          Loading map data...
        </div>
      )}

      {/* Mode switching UI */}
      <div className="mode-buttons-container">
        <button
          className={`mode-button ${mode === 'venue-discovery' ? 'active-mode' : ''}`}
          onClick={() => setMode('venue-discovery')}
          aria-pressed={mode === 'venue-discovery'} // Indicate pressed state
          aria-label="Venues" // Keep if icon needs extra context, but visible text is primary
        >
          <MapPinIcon className="button-icon" aria-hidden="true" /> {/* Added icon */}
          <span>Venues</span> {/* Added visible text label */}
        </button>
        <button
          className={`mode-button ${mode === 'travel-history' ? 'active-mode' : ''}`}
          onClick={() => setMode('travel-history')}
          aria-pressed={mode === 'travel-history'}
          aria-label="Travel History"
        >
          <GlobeAltIcon className="button-icon" aria-hidden="true" />
          <span>History</span>
        </button>
        <button
          className={`mode-button ${mode === 'route-planning' ? 'active-mode' : ''}`}
          onClick={() => setMode('route-planning')}
          aria-pressed={mode === 'route-planning'}
          aria-label="Route Planner"
        >
          <ArrowPathIcon className="button-icon" aria-hidden="true" />
          <span>Route</span>
        </button>
      </div>

      {/* Map Explorer Component or Empty State */}
      <div className="map-container"> {/* Added map container for layout */}
        {success && regions && regions.length === 0 && ( /* Show empty state only on success with no data */
          <div className="empty-state" role="status">
            <p>No regions found matching your criteria.</p>
            <p>Try adjusting your filters or search area.</p>
          </div>
        )}
        {success && regions && regions.length > 0 && (
          <MapExplorerComponent mode={mode} regions={regions} />
        )}
        {/* Loading/Error states are rendered above the map container */}
      </div>
    </div>
  );
}

export function meta() {
  return [{ title: 'Map Explorer' }];
}

export async function loader(args: LoaderFunctionArgs) {
  return loadData(args);
}
