import { useTranslation } from "react-i18next";

export function meta() {
  const { t } = useTranslation();
  return [
    { title: 'Wanderlust Explorer - Interactive Travel Log' },
    {
      name: 'description',
      content: 'Explore your travel memories with an interactive map and personalized recommendations. Relive past trips and plan future adventures.'
    },
    { name: 'keywords', content: 'travel, exploration, interactive map, travel log, recommendations' },
  ];
}

export default function WanderlustExplorer() {
  return null;
}
