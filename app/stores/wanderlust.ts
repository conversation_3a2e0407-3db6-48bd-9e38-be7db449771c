import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type {
  WanderlustState,
  VisitedPlace,
  CityRegion,
  FilterOptions,
  TravelRoute,
  RouteOptimizationOptions
} from '~/types/wanderlust';

interface WanderlustActions {
  // Region management
  setCurrentRegion: (regionId: string) => void;
  loadRegionData: (regions: CityRegion[]) => void;

  // Place management
  setSelectedPlace: (place: VisitedPlace | null) => void;
  updatePlaceNotes: (placeId: string, notes: string) => void;
  updatePlaceRating: (placeId: string, rating: number) => void;

  // Filtering and search
  setFilterOptions: (options: Partial<FilterOptions>) => void;
  applyFilters: () => void;
  searchPlaces: (query: string) => void;

  // Map management
  setMapCenter: (lat: number, lng: number) => void;
  setMapZoom: (zoom: number) => void;

  // Directions and routing
  setShowDirections: (show: boolean) => void;
  setCurrentRoute: (route: TravelRoute | null) => void;
  clearRoute: () => void;
  clearRouteAndItinerary: () => void;

  // Enhanced route planning
  startRoutePlanning: () => void;
  stopRoutePlanning: () => void;
  setRoutePlanningError: (error: string | null) => void;
  updateOptimizationOptions: (options: Partial<RouteOptimizationOptions>) => void;
  setAlternativeRoutes: (routes: TravelRoute[]) => void;
  selectRoute: (index: number) => void;
  optimizeCurrentRoute: () => Promise<void>;
  calculateRouteFromItinerary: () => Promise<void>;

  // Itinerary management
  addToItinerary: (place: VisitedPlace) => void;
  removeFromItinerary: (placeId: string) => void;
  reorderItinerary: (fromIndex: number, toIndex: number) => void;
  clearItinerary: () => void;

  // Loading and error states
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Reset state
  reset: () => void;
}

type WanderlustStore = WanderlustState & WanderlustActions;

const initialState: WanderlustState = {
  currentRegion: null,
  selectedPlace: null,
  filteredPlaces: [],
  filterOptions: {
    categories: [],
    searchQuery: '',
    sortBy: 'name',
    sortOrder: 'asc',
  },
  mapCenter: {
    lat: 40.7589,
    lng: -73.9851,
  },
  mapZoom: 10,
  showDirections: false,
  currentRoute: null,
  itinerary: [],
  isLoading: false,
  error: null,
  routePlanning: {
    isPlanning: false,
    isOptimizing: false,
    planningError: null,
    optimizationOptions: {
      avoidTolls: false,
      avoidHighways: false,
      avoidFerries: false,
      optimizeWaypointOrder: true,
    },
    alternativeRoutes: [],
    selectedRouteIndex: 0,
    routePreferences: {
      preferredTravelMode: 'DRIVING',
      maxWaypoints: 10,
      autoOptimize: true,
    },
  },
};

let allRegions: CityRegion[] = [];
let allPlaces: VisitedPlace[] = [];

export const useWanderlustStore = create<WanderlustStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Region management
      setCurrentRegion: (regionId: string) => {
        const region = allRegions.find(r => r.id === regionId);
        if (region) {
          set({
            currentRegion: regionId,
            mapCenter: {
              lat: region.center?.latitude ?? 0,
              lng: region.center?.longitude ?? 0,
            },
            mapZoom: 11,
            selectedPlace: null,
          });
          get().applyFilters();
        }
      },

      loadRegionData: (regions: CityRegion[]) => {
        allRegions = regions;
        allPlaces = regions.flatMap(region => region.places).filter((place): place is VisitedPlace => place !== undefined);

        if (regions.length > 0 && !get().currentRegion) {
          get().setCurrentRegion(regions[0].id);
        }
      },

      // Place management
      setSelectedPlace: (place: VisitedPlace | null) => {
        set({ selectedPlace: place });

        if (place) {
          set({
            mapCenter: {
              lat: place.coordinates.latitude,
              lng: place.coordinates.longitude,
            },
            mapZoom: 15,
          });
        }
      },

      updatePlaceNotes: (placeId: string, notes: string) => {
        const updatedPlaces = allPlaces.map(place =>
          place.id === placeId ? { ...place, personalNotes: notes } : place
        );
        allPlaces = updatedPlaces;

        // Update regions
        allRegions = allRegions.map(region => ({
          ...region,
          places: (region.places ?? []).map(place =>
            place.id === placeId ? { ...place, personalNotes: notes } : place
          ),
        }));

        get().applyFilters();
      },

      updatePlaceRating: (placeId: string, rating: number) => {
        const updatedPlaces = allPlaces.map(place =>
          place.id === placeId ? { ...place, rating } : place
        );
        allPlaces = updatedPlaces;

        // Update regions
        allRegions = allRegions.map(region => ({
          ...region,
          places: (region.places ?? []).map(place =>
            place.id === placeId ? { ...place, rating } : place
          ),
        }));

        get().applyFilters();
      },

      // Filtering and search
      setFilterOptions: (options: Partial<FilterOptions>) => {
        set(state => ({
          filterOptions: { ...state.filterOptions, ...options },
        }));
        get().applyFilters();
      },

      applyFilters: () => {
        const { currentRegion, filterOptions } = get();
        if (!currentRegion) return;

        const region = allRegions.find(r => r.id === currentRegion);
        if (!region) return;

        let filtered = [...(region.places ?? [])];

        // Apply category filter
        if (filterOptions.categories.length > 0) {
          filtered = filtered.filter(place =>
            filterOptions.categories.includes(place.category)
          );
        }

        // Apply search filter
        if (filterOptions.searchQuery) {
          const query = filterOptions.searchQuery.toLowerCase();
          filtered = filtered.filter(place =>
            place.name.toLowerCase().includes(query) ||
            place.description.en.toLowerCase().includes(query) ||
            place.city.toLowerCase().includes(query)
          );
        }

        // Apply sorting
        filtered.sort((a, b) => {
          let aValue: any, bValue: any;

          switch (filterOptions.sortBy) {
            case 'name':
              aValue = a.name.toLowerCase();
              bValue = b.name.toLowerCase();
              break;
            case 'rating':
              aValue = a.rating || 0;
              bValue = b.rating || 0;
              break;
            case 'category':
              aValue = a.category;
              bValue = b.category;
              break;
            case 'visitDate':
              aValue = a.visitDate || '';
              bValue = b.visitDate || '';
              break;
            default:
              return 0;
          }

          if (filterOptions.sortOrder === 'desc') {
            return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
          } else {
            return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
          }
        });

        set({ filteredPlaces: filtered });
        console.log('📍 Store: filteredPlaces updated', filtered); // Add console log here
      },

      searchPlaces: (query: string) => {
        get().setFilterOptions({ searchQuery: query });
      },

      // Map management
      setMapCenter: (lat: number, lng: number) => {
        set({ mapCenter: { lat, lng } });
      },

      setMapZoom: (zoom: number) => {
        set({ mapZoom: zoom });
      },

      // Directions and routing
      setShowDirections: (show: boolean) => {
        set({ showDirections: show });
      },

      setCurrentRoute: (route: TravelRoute | null) => {
        set({ currentRoute: route });
      },

      clearRoute: () => {
        const currentState = get();
        console.log('🧹 Store: Clearing route', {
          hadRoute: !!currentState.currentRoute,
          routeId: currentState.currentRoute?.id || 'none',
          showDirections: currentState.showDirections,
          alternativeRoutesCount: currentState.routePlanning.alternativeRoutes.length
        });

        set({
          currentRoute: null,
          showDirections: false,
          routePlanning: {
            ...currentState.routePlanning,
            alternativeRoutes: [],
            selectedRouteIndex: 0,
            planningError: null,
          }
        });

        console.log('✅ Store: Route cleared successfully');
      },

      clearRouteAndItinerary: () => {
        const currentState = get();
        console.log('🧹 Store: Clearing route and itinerary', {
          hadRoute: !!currentState.currentRoute,
          routeId: currentState.currentRoute?.id || 'none',
          itineraryCount: currentState.itinerary.length,
          showDirections: currentState.showDirections,
          isPlanning: currentState.routePlanning.isPlanning
        });

        set({
          currentRoute: null,
          showDirections: false,
          itinerary: [],
          routePlanning: {
            ...currentState.routePlanning,
            alternativeRoutes: [],
            selectedRouteIndex: 0,
            planningError: null,
            isPlanning: false,
            isOptimizing: false,
          }
        });

        console.log('✅ Store: Route and itinerary cleared successfully');
      },

      // Itinerary management
      addToItinerary: (place: VisitedPlace) => {
        const { itinerary } = get();
        if (!itinerary.find(p => p.id === place.id)) {
          set({ itinerary: [...itinerary, place] });
        }
      },

      removeFromItinerary: (placeId: string) => {
        const { itinerary } = get();
        set({ itinerary: itinerary.filter(p => p.id !== placeId) });
      },

      reorderItinerary: (fromIndex: number, toIndex: number) => {
        const { itinerary } = get();
        const newItinerary = [...itinerary];
        const [removed] = newItinerary.splice(fromIndex, 1);
        newItinerary.splice(toIndex, 0, removed);
        set({ itinerary: newItinerary });
      },

      clearItinerary: () => {
        set({ itinerary: [] });
      },

      // Loading and error states
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      // Enhanced route planning actions
      startRoutePlanning: () => {
        set(state => ({
          routePlanning: {
            ...state.routePlanning,
            isPlanning: true,
            planningError: null,
          },
        }));
      },

      stopRoutePlanning: () => {
        set(state => ({
          routePlanning: {
            ...state.routePlanning,
            isPlanning: false,
            isOptimizing: false,
          },
        }));
      },

      setRoutePlanningError: (error: string | null) => {
        set(state => ({
          routePlanning: {
            ...state.routePlanning,
            planningError: error,
            isPlanning: false,
            isOptimizing: false,
          },
        }));
      },

      updateOptimizationOptions: (options: Partial<RouteOptimizationOptions>) => {
        set(state => ({
          routePlanning: {
            ...state.routePlanning,
            optimizationOptions: {
              ...state.routePlanning.optimizationOptions,
              ...options,
            },
          },
        }));
      },

      setAlternativeRoutes: (routes: TravelRoute[]) => {
        set(state => ({
          routePlanning: {
            ...state.routePlanning,
            alternativeRoutes: routes,
            selectedRouteIndex: 0,
          },
        }));
      },

      selectRoute: (index: number) => {
        const { routePlanning } = get();
        if (index >= 0 && index < routePlanning.alternativeRoutes.length) {
          const selectedRoute = routePlanning.alternativeRoutes[index];
          set(state => ({
            currentRoute: selectedRoute,
            routePlanning: {
              ...state.routePlanning,
              selectedRouteIndex: index,
            },
          }));
        }
      },

      optimizeCurrentRoute: async () => {
        const { currentRoute, routePlanning } = get();

        if (!currentRoute || currentRoute.waypoints.length < 2) {
          get().setRoutePlanningError('No route to optimize');
          return;
        }

        set(state => ({
          routePlanning: {
            ...state.routePlanning,
            isOptimizing: true,
            planningError: null,
          },
        }));

        try {
          const { calculateRoute } = await import('~/lib/route-planning-utils');

          const optimizedRoute = await calculateRoute(
            currentRoute.waypoints,
            currentRoute.travelMode,
            {
              ...routePlanning.optimizationOptions,
              optimizeWaypointOrder: true,
            }
          );

          set(state => ({
            currentRoute: optimizedRoute,
            routePlanning: {
              ...state.routePlanning,
              isOptimizing: false,
            },
          }));
        } catch (error) {
          console.error('Route optimization failed:', error);
          get().setRoutePlanningError(
            error instanceof Error ? error.message : 'Route optimization failed'
          );
        }
      },

      calculateRouteFromItinerary: async () => {
        const { itinerary, routePlanning } = get();

        if (itinerary.length < 2) {
          get().setRoutePlanningError('At least 2 places required for route calculation');
          return;
        }

        get().startRoutePlanning();

        try {
          const { calculateRoute, placesToWaypoints } = await import('~/lib/route-planning-utils');

          const waypoints = placesToWaypoints(itinerary);
          const route = await calculateRoute(
            waypoints,
            routePlanning.routePreferences.preferredTravelMode,
            routePlanning.optimizationOptions
          );

          set(state => ({
            currentRoute: route,
            showDirections: true,
            routePlanning: {
              ...state.routePlanning,
              isPlanning: false,
              alternativeRoutes: [route],
              selectedRouteIndex: 0,
            },
          }));
        } catch (error) {
          console.error('Route calculation failed:', error);
          get().setRoutePlanningError(
            error instanceof Error ? error.message : 'Route calculation failed'
          );
        }
      },

      // Reset state
      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'wanderlust-store',
    }
  )
);
