/**
 * Service Worker for FIFA Club World Cup 2025™ Route Planner
 *
 * Implements offline support with intelligent caching strategies:
 * - Cache First: Google Maps tiles (7-day retention)
 * - Network First with Cache Fallback: Google Places API (24-hour retention)
 * - Stale While Revalidate: Google Routes API v2 (6-hour retention)
 *
 * Features:
 * - Cache versioning with automatic cleanup (max 50MB)
 * - Background sync for queued offline actions
 * - Cache invalidation for expired data
 */

/// <reference lib="webworker" />

declare const self: ServiceWorkerGlobalScope;

// Cache configuration
const CACHE_CONFIG = {
  VERSION: 'v1.0.0',
  MAX_SIZE: 50 * 1024 * 1024, // 50MB
  CACHES: {
    MAPS_TILES: 'fifa-maps-tiles-v1',
    PLACES_API: 'fifa-places-api-v1',
    ROUTES_API: 'fifa-routes-api-v1',
    STATIC_ASSETS: 'fifa-static-v1',
  },
  TTL: {
    MAPS_TILES: 7 * 24 * 60 * 60 * 1000, // 7 days
    PLACES_API: 24 * 60 * 60 * 1000, // 24 hours
    ROUTES_API: 6 * 60 * 60 * 1000, // 6 hours
    STATIC_ASSETS: 30 * 24 * 60 * 60 * 1000, // 30 days
  },
};

// Background sync tags
const SYNC_TAGS = {
  PLACE_SEARCH: 'place-search-sync',
  ROUTE_CALCULATION: 'route-calculation-sync',
  VENUE_DISCOVERY: 'venue-discovery-sync',
  ITINERARY_UPDATE: 'itinerary-update-sync',
};

// Cache strategies
interface CacheStrategy {
  name: string;
  match: (request: Request) => boolean;
  handler: (request: Request) => Promise<Response>;
}

/**
 * Cache First Strategy - For Google Maps tiles
 * Serves from cache first, falls back to network
 */
const cacheFirstStrategy = (cacheName: string, ttl: number): CacheStrategy => ({
  name: 'CacheFirst',
  match: (request) => {
    const url = new URL(request.url);
    return url.hostname.includes('maps.googleapis.com') &&
           (url.pathname.includes('/maps/api/staticmap') ||
            url.pathname.includes('/maps/vt'));
  },
  handler: async (request) => {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);

    if (cachedResponse) {
      const cacheTime = cachedResponse.headers.get('sw-cache-time');
      if (cacheTime && Date.now() - parseInt(cacheTime) < ttl) {
        return cachedResponse;
      }
    }

    try {
      const networkResponse = await fetch(request);
      if (networkResponse.ok) {
        const responseToCache = networkResponse.clone();
        responseToCache.headers.set('sw-cache-time', Date.now().toString());
        await cache.put(request, responseToCache);
      }
      return networkResponse;
    } catch (error) {
      if (cachedResponse) {
        return cachedResponse;
      }
      throw error;
    }
  },
});

/**
 * Network First with Cache Fallback - For Google Places API
 * Tries network first, falls back to cache if offline
 */
const networkFirstStrategy = (cacheName: string, ttl: number): CacheStrategy => ({
  name: 'NetworkFirst',
  match: (request) => {
    const url = new URL(request.url);
    return url.hostname.includes('maps.googleapis.com') &&
           url.pathname.includes('/maps/api/place');
  },
  handler: async (request) => {
    const cache = await caches.open(cacheName);

    try {
      const networkResponse = await fetch(request);
      if (networkResponse.ok) {
        const responseToCache = networkResponse.clone();
        responseToCache.headers.set('sw-cache-time', Date.now().toString());
        await cache.put(request, responseToCache);
        return networkResponse;
      }
    } catch (error) {
      console.log('Network failed, trying cache:', error);
    }

    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      const cacheTime = cachedResponse.headers.get('sw-cache-time');
      if (!cacheTime || Date.now() - parseInt(cacheTime) < ttl) {
        return cachedResponse;
      }
    }

    throw new Error('No network or valid cache available');
  },
});

/**
 * Stale While Revalidate - For Google Routes API v2
 * Serves from cache immediately, updates cache in background
 */
const staleWhileRevalidateStrategy = (cacheName: string, ttl: number): CacheStrategy => ({
  name: 'StaleWhileRevalidate',
  match: (request) => {
    const url = new URL(request.url);
    return url.hostname.includes('routes.googleapis.com');
  },
  handler: async (request) => {
    const cache = await caches.open(cacheName);
    const cachedResponse = await cache.match(request);

    // Always try to update cache in background
    const fetchPromise = fetch(request).then(async (networkResponse) => {
      if (networkResponse.ok) {
        const responseToCache = networkResponse.clone();
        responseToCache.headers.set('sw-cache-time', Date.now().toString());
        await cache.put(request, responseToCache);
      }
      return networkResponse;
    }).catch(() => null);

    // Return cached response immediately if available and fresh
    if (cachedResponse) {
      const cacheTime = cachedResponse.headers.get('sw-cache-time');
      if (cacheTime && Date.now() - parseInt(cacheTime) < ttl) {
        return cachedResponse;
      }
    }

    // Wait for network response if no valid cache
    const networkResponse = await fetchPromise;
    if (networkResponse) {
      return networkResponse;
    }

    // Fall back to stale cache if network fails
    if (cachedResponse) {
      return cachedResponse;
    }

    throw new Error('No network or cache available');
  },
});

// Initialize cache strategies
const strategies: CacheStrategy[] = [
  cacheFirstStrategy(CACHE_CONFIG.CACHES.MAPS_TILES, CACHE_CONFIG.TTL.MAPS_TILES),
  networkFirstStrategy(CACHE_CONFIG.CACHES.PLACES_API, CACHE_CONFIG.TTL.PLACES_API),
  staleWhileRevalidateStrategy(CACHE_CONFIG.CACHES.ROUTES_API, CACHE_CONFIG.TTL.ROUTES_API),
];

/**
 * Service Worker Install Event
 */
self.addEventListener('install', (event: ExtendableEvent) => {
  console.log('🏆 FIFA Service Worker installing...');

  event.waitUntil(
    caches.open(CACHE_CONFIG.CACHES.STATIC_ASSETS).then((cache) => {
      // Pre-cache critical static assets
      return cache.addAll([
        '/',
        '/route-planner',
        '/venues',
        '/manifest.json',
      ]);
    })
  );

  // Skip waiting to activate immediately
  self.skipWaiting();
});

/**
 * Service Worker Activate Event
 */
self.addEventListener('activate', (event: ExtendableEvent) => {
  console.log('🏆 FIFA Service Worker activating...');

  event.waitUntil(
    Promise.all([
      // Clean up old caches
      cleanupOldCaches(),
      // Claim all clients
      self.clients.claim(),
    ])
  );
});

/**
 * Fetch Event Handler
 */
self.addEventListener('fetch', (event: FetchEvent) => {
  const request = event.request;

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Find matching strategy
  const strategy = strategies.find(s => s.match(request));

  if (strategy) {
    event.respondWith(
      strategy.handler(request).catch((error) => {
        console.error(`Strategy ${strategy.name} failed:`, error);
        return new Response('Offline - Content not available', {
          status: 503,
          statusText: 'Service Unavailable',
        });
      })
    );
  }
});

/**
 * Background Sync Event Handler
 */
self.addEventListener('sync', (event: any) => {
  // Type assertion to 'any' to avoid missing SyncEvent type error
  console.log('🔄 Background sync triggered:', event.tag);

  switch (event.tag) {
    case SYNC_TAGS.PLACE_SEARCH:
      event.waitUntil(syncPlaceSearches());
      break;
    case SYNC_TAGS.ROUTE_CALCULATION:
      event.waitUntil(syncRouteCalculations());
      break;
    case SYNC_TAGS.VENUE_DISCOVERY:
      event.waitUntil(syncVenueDiscoveries());
      break;
    case SYNC_TAGS.ITINERARY_UPDATE:
      event.waitUntil(syncItineraryUpdates());
      break;
  }
});

/**
 * Message Event Handler
 */
self.addEventListener('message', (event: ExtendableMessageEvent) => {
  const { type, payload } = event.data;

  switch (type) {
    case 'GET_CACHE_STATS':
      event.ports[0].postMessage(getCacheStats());
      break;
    case 'CLEAR_CACHE':
      event.waitUntil(clearSpecificCache(payload.cacheName));
      break;
    case 'FORCE_SYNC':
      event.waitUntil(forceSyncAll());
      break;
  }
});

/**
 * Clean up old cache versions
 */
async function cleanupOldCaches(): Promise<void> {
  const cacheNames = await caches.keys();
  const currentCaches = Object.values(CACHE_CONFIG.CACHES);

  const deletePromises = cacheNames
    .filter(name => !currentCaches.includes(name))
    .map(name => caches.delete(name));

  await Promise.all(deletePromises);

  // Check cache size and cleanup if needed
  await enforceMaxCacheSize();
}

/**
 * Enforce maximum cache size
 */
async function enforceMaxCacheSize(): Promise<void> {
  const totalSize = await getTotalCacheSize();

  if (totalSize > CACHE_CONFIG.MAX_SIZE) {
    console.log('🧹 Cache size exceeded, cleaning up...');

    // Remove oldest entries from each cache
    for (const cacheName of Object.values(CACHE_CONFIG.CACHES)) {
      await cleanupCacheByAge(cacheName);
    }
  }
}

/**
 * Get total cache size across all caches
 */
async function getTotalCacheSize(): Promise<number> {
  let totalSize = 0;

  for (const cacheName of Object.values(CACHE_CONFIG.CACHES)) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();

    for (const request of keys) {
      const response = await cache.match(request);
      if (response) {
        const blob = await response.blob();
        totalSize += blob.size;
      }
    }
  }

  return totalSize;
}

/**
 * Clean up cache entries by age
 */
async function cleanupCacheByAge(cacheName: string): Promise<void> {
  const cache = await caches.open(cacheName);
  const keys = await cache.keys();

  const entries = await Promise.all(
    keys.map(async (request) => {
      const response = await cache.match(request);
      const cacheTime = response?.headers.get('sw-cache-time');
      return {
        request,
        cacheTime: cacheTime ? parseInt(cacheTime) : 0,
      };
    })
  );

  // Sort by cache time (oldest first) and remove oldest 25%
  entries.sort((a, b) => a.cacheTime - b.cacheTime);
  const toDelete = entries.slice(0, Math.floor(entries.length * 0.25));

  await Promise.all(
    toDelete.map(entry => cache.delete(entry.request))
  );
}

/**
 * Get cache statistics
 */
async function getCacheStats() {
  const stats = {
    version: CACHE_CONFIG.VERSION,
    caches: {} as Record<string, { count: number; size: number }>,
    totalSize: 0,
    lastCleanup: Date.now(),
  };

  for (const [name, cacheName] of Object.entries(CACHE_CONFIG.CACHES)) {
    const cache = await caches.open(cacheName);
    const keys = await cache.keys();

    let cacheSize = 0;
    for (const request of keys) {
      const response = await cache.match(request);
      if (response) {
        const blob = await response.blob();
        cacheSize += blob.size;
      }
    }

    stats.caches[name] = {
      count: keys.length,
      size: cacheSize,
    };
    stats.totalSize += cacheSize;
  }

  return stats;
}

/**
 * Clear specific cache
 */
async function clearSpecificCache(cacheName: string): Promise<void> {
  await caches.delete(cacheName);
}

/**
 * Sync place searches from offline queue
 */
async function syncPlaceSearches(): Promise<void> {
  console.log('🔍 Syncing place searches...');
  
  try {
    // Get queued place search actions from storage
    const offlineQueue = await getOfflineQueue();
    const placeSearchActions = offlineQueue.filter(action => action.type === 'place-search');
    
    if (placeSearchActions.length === 0) {
      console.log('No place searches to sync');
      return;
    }
    
    console.log(`Found ${placeSearchActions.length} place searches to sync`);
    
    // Process each action
    for (const action of placeSearchActions) {
      try {
        const { query, searchMode = 'google' } = action.payload;
        
        // Perform the search
        const response = await fetch(`https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(query)}&key=${GOOGLE_MAPS_API_KEY}`);
        
        if (!response.ok) {
          throw new Error(`Failed to sync place search: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Cache the results
        const cacheKey = `place-search:${query}`;
        const cache = await caches.open(CACHE_CONFIG.CACHES.PLACES_API);
        await cache.put(cacheKey, new Response(JSON.stringify(data)));
        
        // Remove the action from the queue
        await removeActionFromQueue(action.id);
        
        console.log(`Successfully synced place search: ${query}`);
      } catch (error) {
        console.error(`Error syncing place search:`, error);
        
        // Increment retry count
        action.retryCount = (action.retryCount || 0) + 1;
        
        // If max retries exceeded, remove from queue
        if (action.retryCount >= MAX_RETRIES) {
          await removeActionFromQueue(action.id);
          console.log(`Removed failed place search after ${MAX_RETRIES} retries: ${action.payload.query}`);
        } else {
          // Update retry count in queue
          await updateActionInQueue(action);
        }
      }
    }
  } catch (error) {
    console.error('Error in syncPlaceSearches:', error);
  }
}

/**
 * Sync route calculations from offline queue
 */
async function syncRouteCalculations(): Promise<void> {
  console.log('🗺️ Syncing route calculations...');
  
  try {
    // Get queued route calculation actions from storage
    const offlineQueue = await getOfflineQueue();
    const routeActions = offlineQueue.filter(action => action.type === 'route-calculation');
    
    if (routeActions.length === 0) {
      console.log('No route calculations to sync');
      return;
    }
    
    console.log(`Found ${routeActions.length} route calculations to sync`);
    
    // Process each action
    for (const action of routeActions) {
      try {
        const { origin, destination, travelMode, waypoints } = action.payload;
        
        // Prepare request URL
        const waypointsParam = waypoints && waypoints.length > 0 
          ? `&waypoints=${waypoints.map((wp: string) => encodeURIComponent(wp)).join('|')}` 
          : '';
        
        // Perform the route calculation
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/directions/json?origin=${encodeURIComponent(origin)}&destination=${encodeURIComponent(destination)}&mode=${travelMode}${waypointsParam}&key=${GOOGLE_MAPS_API_KEY}`
        );
        
        if (!response.ok) {
          throw new Error(`Failed to sync route calculation: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Cache the results
        const cacheKey = `route-calculation:${origin}-${destination}-${travelMode}${waypointsParam}`;
        const cache = await caches.open(CACHE_CONFIG.CACHES.ROUTES_API);
        await cache.put(cacheKey, new Response(JSON.stringify(data)));
        
        // Remove the action from the queue
        await removeActionFromQueue(action.id);
        
        console.log(`Successfully synced route calculation: ${origin} to ${destination}`);
      } catch (error) {
        console.error(`Error syncing route calculation:`, error);
        
        // Increment retry count
        action.retryCount = (action.retryCount || 0) + 1;
        
        // If max retries exceeded, remove from queue
        if (action.retryCount >= MAX_RETRIES) {
          await removeActionFromQueue(action.id);
          console.log(`Removed failed route calculation after ${MAX_RETRIES} retries: ${action.payload.origin} to ${action.payload.destination}`);
        } else {
          // Update retry count in queue
          await updateActionInQueue(action);
        }
      }
    }
  } catch (error) {
    console.error('Error in syncRouteCalculations:', error);
  }
}

/**
 * Sync venue discoveries from offline queue
 */
async function syncVenueDiscoveries(): Promise<void> {
  console.log('🏟️ Syncing venue discoveries...');
  
  try {
    // Get queued venue discovery actions from storage
    const offlineQueue = await getOfflineQueue();
    const venueActions = offlineQueue.filter(action => action.type === 'venue-discovery');
    
    if (venueActions.length === 0) {
      console.log('No venue discoveries to sync');
      return;
    }
    
    console.log(`Found ${venueActions.length} venue discoveries to sync`);
    
    // Process each action
    for (const action of venueActions) {
      try {
        const { location, radius, category } = action.payload;
        
        // Prepare request URL with category filter if provided
        const categoryParam = category ? `&type=${encodeURIComponent(category)}` : '';
        
        // Perform the venue discovery
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${encodeURIComponent(location)}&radius=${radius}${categoryParam}&key=${GOOGLE_MAPS_API_KEY}`
        );
        
        if (!response.ok) {
          throw new Error(`Failed to sync venue discovery: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Cache the results
        const cacheKey = `venue-discovery:${location}-${radius}-${category || 'all'}`;
        const cache = await caches.open(CACHE_CONFIG.CACHES.PLACES_API);
        await cache.put(cacheKey, new Response(JSON.stringify(data)));
        
        // Remove the action from the queue
        await removeActionFromQueue(action.id);
        
        console.log(`Successfully synced venue discovery near ${location}`);
      } catch (error) {
        console.error(`Error syncing venue discovery:`, error);
        
        // Increment retry count
        action.retryCount = (action.retryCount || 0) + 1;
        
        // If max retries exceeded, remove from queue
        if (action.retryCount >= MAX_RETRIES) {
          await removeActionFromQueue(action.id);
          console.log(`Removed failed venue discovery after ${MAX_RETRIES} retries: ${action.payload.location}`);
        } else {
          // Update retry count in queue
          await updateActionInQueue(action);
        }
      }
    }
  } catch (error) {
    console.error('Error in syncVenueDiscoveries:', error);
  }
}

/**
 * Sync itinerary updates from offline queue
 */
async function syncItineraryUpdates(): Promise<void> {
  console.log('📅 Syncing itinerary updates...');
  
  try {
    // Get queued itinerary update actions from storage
    const offlineQueue = await getOfflineQueue();
    const itineraryActions = offlineQueue.filter(action => action.type === 'itinerary-update');
    
    if (itineraryActions.length === 0) {
      console.log('No itinerary updates to sync');
      return;
    }
    
    console.log(`Found ${itineraryActions.length} itinerary updates to sync`);
    
    // Process each action
    for (const action of itineraryActions) {
      try {
        const { itineraryId, updates } = action.payload;
        
        // Prepare the request body
        const requestBody = JSON.stringify({
          itineraryId,
          updates,
          timestamp: Date.now()
        });
        
        // Perform the itinerary update
        const response = await fetch(
          'https://worldcup-api.example.com/itineraries/update',
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: requestBody
          }
        );
        
        if (!response.ok) {
          throw new Error(`Failed to sync itinerary update: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Cache the updated itinerary
        const cacheKey = `itinerary:${itineraryId}`;
        const cache = await caches.open(CACHE_CONFIG.CACHES.STATIC_ASSETS);
        await cache.put(cacheKey, new Response(JSON.stringify(data)));
        
        // Remove the action from the queue
        await removeActionFromQueue(action.id);
        
        console.log(`Successfully synced itinerary update for ID: ${itineraryId}`);
      } catch (error) {
        console.error(`Error syncing itinerary update:`, error);
        
        // Increment retry count
        action.retryCount = (action.retryCount || 0) + 1;
        
        // If max retries exceeded, remove from queue
        if (action.retryCount >= MAX_RETRIES) {
          await removeActionFromQueue(action.id);
          console.log(`Removed failed itinerary update after ${MAX_RETRIES} retries: ${action.payload.itineraryId}`);
        } else {
          // Update retry count in queue
          await updateActionInQueue(action);
        }
      }
    }
  } catch (error) {
    console.error('Error in syncItineraryUpdates:', error);
  }
}

/**
 * Force sync all queued items
 */
async function forceSyncAll(): Promise<void> {
  await Promise.all([
    syncPlaceSearches(),
    syncRouteCalculations(),
    syncVenueDiscoveries(),
    syncItineraryUpdates(),
  ]);
}

export {};

// Constants for offline queue
const OFFLINE_QUEUE_KEY = 'fifa-offline-queue';
const MAX_RETRIES = 3;
const GOOGLE_MAPS_API_KEY = 'YOUR_API_KEY'; // This should be replaced with the actual API key

/**
 * Get the offline action queue from IndexedDB
 */
async function getOfflineQueue(): Promise<any[]> {
  try {
    const db = await openDatabase();
    const transaction = db.transaction(['offlineQueue'], 'readonly');
    const store = transaction.objectStore('offlineQueue');
    const actions: any[] = await new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
    return actions || [];
  } catch (error) {
    console.error('Error getting offline queue:', error);
    // Fallback to localStorage if IndexedDB fails
    const queueJson = localStorage.getItem(OFFLINE_QUEUE_KEY);
    return queueJson ? JSON.parse(queueJson) : [];
  }
}

/**
 * Open IndexedDB database
 */
async function openDatabase(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('FIFAOfflineDB', 1);
    
    request.onupgradeneeded = (event) => {
      const db = request.result;
      if (!db.objectStoreNames.contains('offlineQueue')) {
        db.createObjectStore('offlineQueue', { keyPath: 'id' });
      }
    };
    
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
}

/**
 * Remove an action from the offline queue
 */
async function removeActionFromQueue(actionId: string): Promise<void> {
  try {
    const db = await openDatabase();
    const transaction = db.transaction(['offlineQueue'], 'readwrite');
    const store = transaction.objectStore('offlineQueue');
    await store.delete(actionId);
  } catch (error) {
    console.error('Error removing action from queue:', error);
    // Fallback to localStorage if IndexedDB fails
    const queue = await getOfflineQueue();
    const updatedQueue = queue.filter(action => action.id !== actionId);
    localStorage.setItem(OFFLINE_QUEUE_KEY, JSON.stringify(updatedQueue));
  }
}

/**
 * Update an action in the offline queue
 */
async function updateActionInQueue(action: any): Promise<void> {
  try {
    const db = await openDatabase();
    const transaction = db.transaction(['offlineQueue'], 'readwrite');
    const store = transaction.objectStore('offlineQueue');
    await store.put(action);
  } catch (error) {
    console.error('Error updating action in queue:', error);
    // Fallback to localStorage if IndexedDB fails
    const queue = await getOfflineQueue();
    const updatedQueue = queue.map(a => a.id === action.id ? action : a);
    localStorage.setItem(OFFLINE_QUEUE_KEY, JSON.stringify(updatedQueue));
  }
}

/**
 * Periodic Sync Event Handler
 */
self.addEventListener('periodicsync', (event: any) => {
  console.log('⏰ Periodic sync triggered:', event.tag);

  switch (event.tag) {
    case 'periodic-sync-all':
      event.waitUntil(forceSyncAll());
      break;
  }
});
