import type { VisitedPlace } from './wanderlust';

export interface RouteWaypoint extends VisitedPlace {
  id: string;
  location: {
    lat: number;
    lng: number;
  };
  placeData: VisitedPlace;
  stopover: boolean;
  waypointIndex: number;
  isStart: boolean;
  isEnd: boolean;
  estimatedTravelTime?: number;
  trafficDelay?: number;
}

export interface TrafficInfo {
  level: 'low' | 'medium' | 'high';
  delay: number;
  alternativeAvailable: boolean;
  lastUpdated: number;
}

export interface RouteTemplate {
  id: string;
  name: string;
  description: string;
  waypoints: RouteWaypoint[];
  estimatedDuration: number;  // Duration in minutes
  difficulty: 'Easy' | 'Moderate' | 'Hard';
  tags: string[];
  estimatedDistance: string;
  categories: string[];
}


