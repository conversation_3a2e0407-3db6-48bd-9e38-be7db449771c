/**
 * Enhanced Types for Google Routes API v2
 * 
 * Extends existing route types with new capabilities from Routes API v2
 * while maintaining backward compatibility with legacy implementations.
 */

import type {
  TravelRoute,
  RouteWaypoint,
  RouteLeg,
  RouteOptimizationOptions
} from './wanderlust';

/**
 * Enhanced route options with Routes API v2 features
 */
export interface EnhancedRouteOptions extends RouteOptimizationOptions {
  // Traffic and routing preferences
  routingPreference?: 'TRAFFIC_UNAWARE' | 'TRAFFIC_AWARE' | 'TRAFFIC_AWARE_OPTIMAL';
  requestedReferenceTime?: Date;
  
  // Alternative routes
  includeAlternativeRoutes?: boolean;
  maxAlternativeRoutes?: number;
  
  // Quality and encoding
  polylineQuality?: 'HIGH_QUALITY' | 'OVERVIEW';
  polylineEncoding?: 'ENCODED_POLYLINE' | 'GEO_JSON_LINESTRING';
  
  // Enhanced features
  computeTollInfo?: boolean;
  computeFuelConsumption?: boolean;
  includeSpeedLimits?: boolean;
  includeTrafficSpeedEntry?: boolean;
  
  // Eco-friendly options
  requestEcoFriendlyRoute?: boolean;
  vehicleEmissionType?: 'GASOLINE' | 'ELECTRIC' | 'HYBRID' | 'DIESEL';
  
  // Advanced modifiers
  avoidIndoor?: boolean;
  avoidPrivateRoads?: boolean;
}

/**
 * Enhanced travel route with v2 features
 */
export interface EnhancedTravelRoute extends TravelRoute {
  // Traffic and timing
  durationInTraffic?: string;
  durationInTrafficValue?: number;
  trafficConditions?: 'UNKNOWN' | 'LIGHT' | 'MODERATE' | 'HEAVY' | 'SEVERE';
  
  // Toll information
  tollInfo?: {
    estimatedCost?: number;
    currency?: string;
    tollFreeAlternativeAvailable?: boolean;
  };
  
  // Environmental data
  fuelConsumption?: {
    microliters?: number;
    estimatedCost?: number;
    co2Emissions?: number;
  };
  
  // Route quality indicators
  routeQuality?: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
  confidenceLevel?: number; // 0-1
  
  // Alternative route metadata
  isAlternativeRoute?: boolean;
  alternativeRouteIndex?: number;
  routeToken?: string;
  
  // Enhanced warnings and advisories
  travelAdvisory?: {
    tollInfo?: any;
    speedReadingIntervals?: any[];
    fuelConsumptionMicroliters?: number;
    routeRestrictionsPartiallyIgnored?: boolean;
    transitFare?: any;
  };
  
  // Route comparison metrics
  comparisonMetrics?: {
    timeSavings?: number; // seconds compared to alternative
    distanceSavings?: number; // meters compared to alternative
    costSavings?: number; // currency units compared to alternative
    ecoFriendliness?: number; // 0-1 score
  };
}

/**
 * Enhanced route leg with v2 features
 */
export interface EnhancedRouteLeg extends RouteLeg {
  // Traffic data
  durationInTraffic?: {
    text: string;
    value: number;
  };
  
  // Speed and traffic information
  speedReadingIntervals?: SpeedReadingInterval[];
  trafficSpeedEntry?: TrafficSpeedEntry[];
  
  // Toll information for this leg
  tollInfo?: {
    estimatedPrice?: number;
    currency?: string;
  };
  
  // Environmental data for this leg
  fuelConsumption?: number; // microliters
  co2Emissions?: number; // grams
}

/**
 * Speed reading intervals for traffic analysis
 */
export interface SpeedReadingInterval {
  startPolylinePointIndex: number;
  endPolylinePointIndex: number;
  speed: 'NORMAL' | 'SLOW' | 'TRAFFIC_JAM';
}

/**
 * Traffic speed entry data
 */
export interface TrafficSpeedEntry {
  offsetMeters: number;
  speedKmh: number;
}

/**
 * Route comparison result
 */
export interface RouteComparison {
  routes: EnhancedTravelRoute[];
  recommendation: {
    fastestRoute: EnhancedTravelRoute;
    shortestRoute: EnhancedTravelRoute;
    mostEcoFriendlyRoute?: EnhancedTravelRoute;
    cheapestRoute?: EnhancedTravelRoute;
  };
  comparisonMatrix: {
    routeId: string;
    duration: number;
    distance: number;
    cost?: number;
    ecoScore?: number;
  }[];
}

/**
 * Route calculation request with v2 features
 */
export interface EnhancedRouteRequest {
  waypoints: RouteWaypoint[];
  travelMode: TravelRoute['travelMode'];
  options: EnhancedRouteOptions;
  
  // Request metadata
  requestId?: string;
  userId?: string;
  sessionId?: string;
  
  // Preferences
  userPreferences?: {
    prioritizeTime?: boolean;
    prioritizeDistance?: boolean;
    prioritizeCost?: boolean;
    prioritizeEcoFriendliness?: boolean;
    avoidTolls?: boolean;
    avoidHighways?: boolean;
  };
}

/**
 * Route calculation response with v2 features
 */
export interface EnhancedRouteResponse {
  primaryRoute: EnhancedTravelRoute;
  alternativeRoutes: EnhancedTravelRoute[];
  comparison: RouteComparison;
  
  // Response metadata
  requestId?: string;
  calculationTime: number; // milliseconds
  apiVersion: 'v1' | 'v2';
  fallbackUsed?: boolean;
  
  // Quality indicators
  dataFreshness: Date;
  trafficDataAge?: number; // minutes
  confidenceScore: number; // 0-1
  
  // Warnings and notices
  warnings: string[];
  notices: string[];
  
  // Rate limiting info
  rateLimitInfo?: {
    remaining: number;
    resetTime: Date;
  };
}

/**
 * Route planning state with v2 features
 */
export interface EnhancedRoutePlanningState {
  // Current state
  isPlanning: boolean;
  isOptimizing: boolean;
  planningError: string | null;
  
  // Route data
  currentRoute: EnhancedTravelRoute | null;
  alternativeRoutes: EnhancedTravelRoute[];
  selectedRouteIndex: number;
  
  // Options and preferences
  optimizationOptions: EnhancedRouteOptions;
  routePreferences: {
    preferredTravelMode: TravelRoute['travelMode'];
    maxWaypoints: number;
    autoOptimize: boolean;
    preferTrafficAware: boolean;
    includeAlternatives: boolean;
    maxAlternatives: number;
  };
  
  // Performance tracking
  lastCalculationTime: number;
  averageCalculationTime: number;
  cacheHitRate: number;
  
  // API usage tracking
  apiUsage: {
    requestsToday: number;
    requestsThisHour: number;
    quotaRemaining: number;
    costToday: number;
  };
  
  // Feature flags
  features: {
    useRoutesV2: boolean;
    enableTrafficAware: boolean;
    enableAlternativeRoutes: boolean;
    enableTollInfo: boolean;
    enableEcoRouting: boolean;
  };
}

/**
 * Route analytics and metrics
 */
export interface RouteAnalytics {
  // Usage statistics
  totalRoutesCalculated: number;
  averageWaypoints: number;
  mostUsedTravelMode: TravelRoute['travelMode'];
  
  // Performance metrics
  averageCalculationTime: number;
  cacheHitRate: number;
  errorRate: number;
  
  // User behavior
  alternativeRouteUsage: number; // percentage
  trafficAwareUsage: number; // percentage
  optimizationUsage: number; // percentage
  
  // Cost and efficiency
  totalApiCalls: number;
  estimatedCost: number;
  fuelSavings: number; // liters
  timeSavings: number; // hours
  
  // Quality metrics
  routeAccuracy: number; // 0-1
  userSatisfaction: number; // 0-1
  reportedIssues: number;
}

/**
 * Route sharing and collaboration
 */
export interface RouteShare {
  id: string;
  route: EnhancedTravelRoute;
  sharedBy: string;
  sharedAt: Date;
  expiresAt?: Date;
  
  // Sharing options
  isPublic: boolean;
  allowEditing: boolean;
  requiresAuth: boolean;
  
  // Collaboration features
  comments: RouteComment[];
  likes: number;
  views: number;
  
  // Metadata
  title: string;
  description?: string;
  tags: string[];
  category?: string;
}

/**
 * Route comment for collaboration
 */
export interface RouteComment {
  id: string;
  userId: string;
  userName: string;
  content: string;
  createdAt: Date;
  waypointIndex?: number; // Comment on specific waypoint
  legIndex?: number; // Comment on specific leg
}

/**
 * Route export formats
 */
export type RouteExportFormat = 'GPX' | 'KML' | 'JSON' | 'CSV' | 'PDF';

/**
 * Route export options
 */
export interface RouteExportOptions {
  format: RouteExportFormat;
  includeWaypoints: boolean;
  includeDirections: boolean;
  includeElevation: boolean;
  includeTrafficData: boolean;
  includeTollInfo: boolean;
  
  // Customization
  title?: string;
  description?: string;
  author?: string;
  
  // Quality settings
  polylineSimplification?: number; // 0-1
  imageQuality?: 'LOW' | 'MEDIUM' | 'HIGH';
}

/**
 * Utility type for route status
 */
export type RouteStatus = 
  | 'CALCULATING'
  | 'COMPLETED'
  | 'ERROR'
  | 'CANCELLED'
  | 'OPTIMIZING'
  | 'CACHED';

/**
 * Utility type for route priority
 */
export type RoutePriority = 
  | 'TIME'
  | 'DISTANCE'
  | 'COST'
  | 'ECO_FRIENDLY'
  | 'COMFORT'
  | 'SCENIC';

/**
 * Route calculation context
 */
export interface RouteCalculationContext {
  requestId: string;
  userId?: string;
  sessionId?: string;
  timestamp: Date;
  
  // Request details
  waypoints: RouteWaypoint[];
  travelMode: TravelRoute['travelMode'];
  options: EnhancedRouteOptions;
  
  // Context information
  userLocation?: {
    latitude: number;
    longitude: number;
  };
  deviceInfo?: {
    type: 'mobile' | 'desktop' | 'tablet';
    platform: string;
    userAgent: string;
  };
  
  // Performance tracking
  startTime: number;
  endTime?: number;
  duration?: number;
  
  // Result information
  status: RouteStatus;
  error?: string;
  routesReturned?: number;
  cacheHit?: boolean;
  apiVersion?: 'v1' | 'v2';
}
