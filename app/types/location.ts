/**
 * Type definitions for location tracking (Task 14)
 */

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  altitudeAccuracy?: number;
  heading?: number;
  speed?: number;
}

export interface LocationPosition {
  coordinates: LocationCoordinates;
  timestamp: number;
  id: string;
}

export interface LocationTrackingSettings {
  enableHighAccuracy: boolean;
  timeout: number;
  maximumAge: number;
  updateInterval: number;
  accuracyThreshold: number;
  saveHistory: boolean;
  maxHistorySize: number;
}

export interface LocationTrackingState {
  isTracking: boolean;
  currentPosition?: LocationPosition;
  positionHistory: LocationPosition[];
  accuracy: number;
  lastUpdate: number;
  error?: string;
  settings: LocationTrackingSettings;
  isHighAccuracy: boolean;
}

export interface LocationTrackingStats {
  totalDistance: number; // in meters
  averageAccuracy: number; // in meters
  trackingDuration: number; // in seconds
  positionCount: number;
  averageSpeed?: number; // in m/s
  maxSpeed?: number; // in m/s
}

export interface GeofenceArea {
  id: string;
  name: string;
  center: {
    latitude: number;
    longitude: number;
  };
  radius: number; // in meters
  isActive: boolean;
  notifications: {
    onEnter: boolean;
    onExit: boolean;
  };
}

export interface LocationEvent {
  id: string;
  type: 'position_update' | 'accuracy_change' | 'geofence_enter' | 'geofence_exit' | 'tracking_start' | 'tracking_stop';
  timestamp: number;
  position?: LocationPosition;
  geofence?: GeofenceArea;
  data?: any;
}
