import type { RouteWaypoint } from '~/types/route-planning';

export interface WaypointManagementOverlayProps {
  waypoints: RouteWaypoint[];
  maxWaypoints: number;
  allowReordering: boolean;
  onWaypointRemove: (waypointId: string) => void;
  onWaypointReorder: (fromIndex: number, toIndex: number) => void;
  routeStats: {
    totalWaypoints: number;
    estimatedDuration: number;
    trafficDelay: number;
    optimizable: boolean;
  };
  onOptimize?: () => void;
}
