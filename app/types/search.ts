/**
 * Type definitions for search functionality (Task 12)
 */

export interface SearchResult {
  id: string;
  name: string;
  description: string;
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  category: string;
  rating?: number;
  priceLevel?: number;
  photos?: string[];
  types: string[];
  placeId: string;
}

export interface AutocompleteResult {
  placeId: string;
  description: string;
  mainText: string;
  secondaryText: string;
  types: string[];
}

export interface SearchFilters {
  category?: string;
  rating?: number;
  priceLevel?: number;
  radius?: number;
  openNow?: boolean;
}

export interface SearchOptions {
  query: string;
  filters?: SearchFilters;
  location?: {
    latitude: number;
    longitude: number;
  };
  radius?: number;
  limit?: number;
}

export interface SearchState {
  query: string;
  results: SearchResult[];
  autocompleteResults: AutocompleteResult[];
  isSearching: boolean;
  hasSearched: boolean;
  error?: string;
  filters: SearchFilters;
}
