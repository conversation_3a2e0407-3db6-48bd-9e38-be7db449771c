export interface VisitedPlace {
  id: string;
  name: string;
  description: {
    en: string;
    fr: string;
    ar: string;
  };
  category: 'food' | 'landmark' | 'museum' | 'park' | 'accommodation' | 'transport' | 'entertainment' | 'shopping' | string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  city: string;
  region: string;
  rating?: number;
  revisitPotential?: 'Highly Recommend' | 'Worth a Look' | 'Skip' | 'High' | 'Medium' | 'Low';
  keyTakeaway?: string;
  personalNotes?: string;
  imageUrl?: string;
  visitDate?: string;
  icon?: string;
}

export interface CityRegion {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  bounds?: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  center?: {
    latitude: number;
    longitude: number;
    lat?: number;
    lng?: number;
  };
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  radius?: number;
  places?: VisitedPlace[];
}

export interface MapMarker {
  id: string;
  position: {
    lat: number;
    lng: number;
  };
  category: string;
  title: string;
  place: VisitedPlace;
}

export interface RouteWaypoint {
  location: {
    lat: number;
    lng: number;
  };
  stopover: boolean;
  placeId?: string;
}

export interface TravelRoute {
  id: string;
  name: string;
  waypoints: RouteWaypoint[];
  optimized: boolean;
  travelMode: 'DRIVING' | 'WALKING' | 'BICYCLING' | 'TRANSIT';
  estimatedDuration: string;
  estimatedDistance: string;
  // Enhanced route planning properties
  polyline?: string;
  bounds?: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
  legs?: RouteLeg[];
  overview?: RouteOverview;
  warnings?: string[];
  copyrights?: string;
  fare?: RouteFare;
  createdAt?: string;
  lastOptimized?: string;
}

export interface RouteLeg {
  distance: {
    text: string;
    value: number; // meters
  };
  duration: {
    text: string;
    value: number; // seconds
  };
  duration_in_traffic?: {
    text: string;
    value: number; // seconds
  };
  end_address: string;
  end_location: {
    lat: number;
    lng: number;
  };
  start_address: string;
  start_location: {
    lat: number;
    lng: number;
  };
  steps: RouteStep[];
  traffic_speed_entry?: any[];
  via_waypoint?: any[];
}

export interface RouteStep {
  distance: {
    text: string;
    value: number;
  };
  duration: {
    text: string;
    value: number;
  };
  end_location: {
    lat: number;
    lng: number;
  };
  html_instructions: string;
  polyline: {
    points: string;
  };
  start_location: {
    lat: number;
    lng: number;
  };
  travel_mode: string;
  maneuver?: string;
}

export interface RouteOverview {
  totalDistance: string;
  totalDuration: string;
  totalDurationInTraffic?: string;
  waypointOrder?: number[];
  optimizedWaypoints?: RouteWaypoint[];
}

export interface RouteFare {
  currency: string;
  text: string;
  value: number;
}

export interface RouteOptimizationOptions {
  avoidTolls: boolean;
  avoidHighways: boolean;
  avoidFerries: boolean;
  optimizeWaypointOrder: boolean;
  departureTime?: Date;
  arrivalTime?: Date;
  trafficModel?: 'best_guess' | 'pessimistic' | 'optimistic';
}

export interface RoutePlanningState {
  isPlanning: boolean;
  isOptimizing: boolean;
  planningError: string | null;
  optimizationOptions: RouteOptimizationOptions;
  alternativeRoutes: TravelRoute[];
  selectedRouteIndex: number;
  routePreferences: {
    preferredTravelMode: TravelRoute['travelMode'];
    maxWaypoints: number;
    autoOptimize: boolean;
  };
}

export interface FilterOptions {
  categories: string[];
  searchQuery: string;
  sortBy: 'name' | 'rating' | 'category' | 'visitDate';
  sortOrder: 'asc' | 'desc';
}

export interface WanderlustState {
  currentRegion: string | null;
  selectedPlace: VisitedPlace | null;
  filteredPlaces: VisitedPlace[];
  filterOptions: FilterOptions;
  mapCenter: {
    lat: number;
    lng: number;
  };
  mapZoom: number;
  showDirections: boolean;
  currentRoute: TravelRoute | null;
  itinerary: VisitedPlace[];
  isLoading: boolean;
  error: string | null;
  // Enhanced route planning state
  routePlanning: RoutePlanningState;
}

export interface DirectionsResult {
  routes: any[]; // google.maps.DirectionsRoute[] when loaded
  status: string; // google.maps.DirectionsStatus when loaded
}

export interface PlaceSearchResult {
  place_id: string;
  name: string;
  vicinity: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  types: string[];
  rating?: number;
  photos?: any[]; // google.maps.places.PlacePhoto[] when loaded
}

// Gesture-related types for WL-004 Mobile Navigation Patterns
export interface GestureState {
  isActive: boolean;
  isDragging: boolean;
  isLongPressing: boolean;
  lastTapTime: number;
  startPoint: { x: number; y: number } | null;
  currentPoint: { x: number; y: number } | null;
  startTime: number | null;
}

export interface SwipeDirection {
  horizontal: 'left' | 'right' | null;
  vertical: 'up' | 'down' | null;
}

export interface BottomSheetState {
  isOpen: boolean;
  height: number;
  snapPoint: number;
  isDragging: boolean;
}

export interface BottomSheetProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  snapPoints?: number[];
  initialSnapPoint?: number;
  className?: string;
  backdropClassName?: string;
  onSnapPointChange?: (snapPoint: number) => void;
}

export interface TouchEventHandlers {
  onTouchStart?: (event: TouchEvent) => void;
  onTouchMove?: (event: TouchEvent) => void;
  onTouchEnd?: (event: TouchEvent) => void;
  onTouchCancel?: (event: TouchEvent) => void;
}

export interface SwipeableRegionCardsProps {
  regions: CityRegion[];
  currentRegion: string;
  onRegionChange: (regionId: string) => void;
  className?: string;
}

export interface GestureConfig {
  swipeThreshold: number;
  longPressDelay: number;
  velocityThreshold: number;
  snapAnimationDuration: number;
}
