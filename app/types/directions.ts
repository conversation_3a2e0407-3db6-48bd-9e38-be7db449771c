/**
 * Type definitions for directions and route planning (Task 13)
 */

export interface RouteWaypoint {
  id: string;
  name: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  address?: string;
  stopover: boolean;
}

export interface RouteStep {
  instruction: string;
  distance: string;
  duration: string;
  distanceValue: number; // in meters
  durationValue: number; // in seconds
  startLocation: {
    latitude: number;
    longitude: number;
  };
  endLocation: {
    latitude: number;
    longitude: number;
  };
  maneuver?: string;
  polyline?: string;
}

export interface RouteLeg {
  startAddress: string;
  endAddress: string;
  distance: string;
  duration: string;
  distanceValue: number; // in meters
  durationValue: number; // in seconds
  steps: RouteStep[];
  startLocation: {
    latitude: number;
    longitude: number;
  };
  endLocation: {
    latitude: number;
    longitude: number;
  };
}

export interface Route {
  id: string;
  name?: string;
  summary: string;
  legs: RouteLeg[];
  waypoints: RouteWaypoint[];
  totalDistance: string;
  totalDuration: string;
  totalDistanceValue: number; // in meters
  totalDurationValue: number; // in seconds
  bounds: {
    northeast: {
      latitude: number;
      longitude: number;
    };
    southwest: {
      latitude: number;
      longitude: number;
    };
  };
  polyline: string;
  waypointOrder: number[];
  travelMode: TravelMode;
  createdAt: string;
}

export type TravelMode = 'DRIVING' | 'WALKING' | 'BICYCLING' | 'TRANSIT';

export interface RouteOptions {
  travelMode: TravelMode;
  optimizeWaypoints: boolean;
  avoidHighways: boolean;
  avoidTolls: boolean;
  avoidFerries: boolean;
  region?: string;
  language?: string;
}

export interface DirectionsState {
  currentRoute?: Route;
  isCalculating: boolean;
  error?: string;
  travelMode: TravelMode;
  routeOptions: RouteOptions;
  savedRoutes: Route[];
}
