/**
 * Google Maps Debugger Component
 * 
 * Temporary debugging component to diagnose Google Maps loading issues
 * after the simplified architecture refactoring.
 */

import React, { useEffect, useState } from 'react';
import { useGoogleMaps } from '~/components/maps/GoogleMapsLoader';

interface DebugInfo {
  apiKey: string | undefined;
  apiKeyValid: boolean;
  googleMapsLoaded: boolean;
  loaderError: Error | undefined;
  dynamicImportTest: string;
  networkConnectivity: string;
  environmentMode: string;
  consoleErrors: string[];
}

export function GoogleMapsDebugger() {
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    apiKey: undefined,
    apiKeyValid: false,
    googleMapsLoaded: false,
    loaderError: undefined,
    dynamicImportTest: 'Not tested',
    networkConnectivity: 'Not tested',
    environmentMode: 'Unknown',
    consoleErrors: [],
  });

  const { isLoaded, loadError } = useGoogleMaps();

  useEffect(() => {
    const runDiagnostics = async () => {
      // 1. Check API Key
      const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
      const apiKeyValid = !!(apiKey && apiKey.length > 20);

      // 2. Check Google Maps Loading
      const googleMapsLoaded = !!(window as any).google?.maps;

      // 3. Test Dynamic Import
      let dynamicImportTest = 'Failed';
      try {
        const module = await import('@googlemaps/js-api-loader');
        dynamicImportTest = module.Loader ? 'Success' : 'Loader not found';
      } catch (error) {
        dynamicImportTest = `Error: ${error instanceof Error ? error.message : 'Unknown'}`;
      }

      // 4. Test Network Connectivity
      let networkConnectivity = 'Failed';
      if (apiKey) {
        try {
          const response = await fetch(`https://maps.googleapis.com/maps/api/js?key=${apiKey}`, {
            method: 'HEAD',
            mode: 'no-cors'
          });
          networkConnectivity = 'Success';
        } catch (error) {
          networkConnectivity = `Error: ${error instanceof Error ? error.message : 'Unknown'}`;
        }
      } else {
        networkConnectivity = 'No API key to test';
      }

      // 5. Environment Mode
      const environmentMode = import.meta.env.MODE || 'Unknown';

      // 6. Capture Console Errors (simplified)
      const consoleErrors: string[] = [];
      const originalError = console.error;
      console.error = (...args: any[]) => {
        const message = args.join(' ');
        if (message.toLowerCase().includes('google') || 
            message.toLowerCase().includes('maps') ||
            message.toLowerCase().includes('loader')) {
          consoleErrors.push(message);
        }
        originalError.apply(console, args);
      };

      setDebugInfo({
        apiKey: apiKey ? `${apiKey.substring(0, 8)}...` : undefined,
        apiKeyValid,
        googleMapsLoaded,
        loaderError: loadError,
        dynamicImportTest,
        networkConnectivity,
        environmentMode,
        consoleErrors,
      });
    };

    runDiagnostics();
  }, [loadError]);

  const getStatusIcon = (condition: boolean) => condition ? '✅' : '❌';
  const getStatusColor = (condition: boolean) => condition ? 'text-green-600' : 'text-red-600';

  return (
    <div className="fixed top-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-md z-50 text-sm">
      <h3 className="font-bold text-lg mb-3 text-gray-800">🔍 Google Maps Debug Info</h3>
      
      <div className="space-y-2">
        {/* API Key Status */}
        <div className="flex items-center justify-between">
          <span>API Key:</span>
          <span className={getStatusColor(debugInfo.apiKeyValid)}>
            {getStatusIcon(debugInfo.apiKeyValid)} {debugInfo.apiKey || 'Not found'}
          </span>
        </div>

        {/* Google Maps Loaded */}
        <div className="flex items-center justify-between">
          <span>Google Maps Loaded:</span>
          <span className={getStatusColor(debugInfo.googleMapsLoaded)}>
            {getStatusIcon(debugInfo.googleMapsLoaded)} {debugInfo.googleMapsLoaded ? 'Yes' : 'No'}
          </span>
        </div>

        {/* Loader Status */}
        <div className="flex items-center justify-between">
          <span>Loader Status:</span>
          <span className={getStatusColor(isLoaded)}>
            {getStatusIcon(isLoaded)} {isLoaded ? 'Loaded' : 'Not loaded'}
          </span>
        </div>

        {/* Dynamic Import Test */}
        <div className="flex items-center justify-between">
          <span>Dynamic Import:</span>
          <span className={getStatusColor(debugInfo.dynamicImportTest === 'Success')}>
            {getStatusIcon(debugInfo.dynamicImportTest === 'Success')} {debugInfo.dynamicImportTest}
          </span>
        </div>

        {/* Network Connectivity */}
        <div className="flex items-center justify-between">
          <span>Network:</span>
          <span className={getStatusColor(debugInfo.networkConnectivity === 'Success')}>
            {getStatusIcon(debugInfo.networkConnectivity === 'Success')} {debugInfo.networkConnectivity}
          </span>
        </div>

        {/* Environment */}
        <div className="flex items-center justify-between">
          <span>Environment:</span>
          <span className="text-blue-600">{debugInfo.environmentMode}</span>
        </div>

        {/* Load Error */}
        {debugInfo.loaderError && (
          <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded">
            <div className="font-semibold text-red-800">Load Error:</div>
            <div className="text-red-600 text-xs mt-1">{debugInfo.loaderError.message}</div>
          </div>
        )}

        {/* Console Errors */}
        {debugInfo.consoleErrors.length > 0 && (
          <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
            <div className="font-semibold text-yellow-800">Console Errors:</div>
            {debugInfo.consoleErrors.map((error, index) => (
              <div key={index} className="text-yellow-600 text-xs mt-1">{error}</div>
            ))}
          </div>
        )}

        {/* Quick Actions */}
        <div className="mt-4 pt-3 border-t border-gray-200">
          <div className="text-xs text-gray-600 mb-2">Quick Actions:</div>
          <div className="flex gap-2">
            <button
              onClick={() => window.location.reload()}
              className="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
            >
              Reload Page
            </button>
            <button
              onClick={() => console.log('Debug Info:', debugInfo)}
              className="px-2 py-1 bg-gray-500 text-white text-xs rounded hover:bg-gray-600"
            >
              Log to Console
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Hook to easily add debugging to any component
export function useGoogleMapsDebug() {
  const { isLoaded, loadError } = useGoogleMaps();
  
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.group('🔍 Google Maps Debug Info');
      console.log('API Key Present:', !!import.meta.env.VITE_GOOGLE_MAPS_API_KEY);
      console.log('Maps Loaded:', isLoaded);
      console.log('Load Error:', loadError);
      console.log('window.google exists:', !!(window as any).google);
      console.log('window.google.maps exists:', !!(window as any).google?.maps);
      console.groupEnd();
    }
  }, [isLoaded, loadError]);

  return { isLoaded, loadError };
}
