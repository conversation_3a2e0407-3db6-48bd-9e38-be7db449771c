/**
 * Enhanced Map Provider
 *
 * Extends the existing MapProvider with venue search capabilities and
 * unified place management for both route planning and venue discovery.
 */

import React, { createContext, useContext, useState, useCallback, useMemo } from 'react';
import { MapProvider, useMapContext, type MapContextValue } from './MapProvider';
import { useGooglePlaces } from '~/hooks/useGooglePlaces';
import { usePlaceSearch } from '~/hooks/usePlaceSearch';
import type { MapFeature } from '~/hooks/useMapFeatures';
import type { VisitedPlace, CityRegion } from '~/types/wanderlust';
import type { PlaceSearchResult } from '~/hooks/useGooglePlaces';

export interface EnhancedMapContextValue extends MapContextValue {
  // Venue search state
  searchQuery: string;
  searchResults: PlaceSearchResult[];
  isSearching: boolean;
  searchHistory: string[];
  
  // Place management
  availablePlaces: VisitedPlace[];
  filteredPlaces: VisitedPlace[];
  selectedCategory: string;
  
  // Enhanced actions
  searchPlaces: (query: string) => Promise<PlaceSearchResult[]>;
  filterPlaces: (category: string, query: string) => void;
  addPlace: (place: VisitedPlace) => void;
  removePlace: (placeId: string) => void;
  clearSearch: () => void;
  setSearchQuery: (query: string) => void;
  setSelectedCategory: (category: string) => void;
}

const EnhancedMapContext = createContext<EnhancedMapContextValue | null>(null);

export interface EnhancedMapProviderProps {
  children: React.ReactNode;
  features?: MapFeature[];
  regions?: CityRegion[];
  onMapLoad?: (map: google.maps.Map) => void;
  onError?: (error: Error) => void;
  onPlaceAdd?: (place: VisitedPlace) => void;
  onPlaceRemove?: (placeId: string) => void;
}

function EnhancedMapProviderInner({
  children,
  regions = [],
  onPlaceAdd,
  onPlaceRemove,
}: Omit<EnhancedMapProviderProps, 'features' | 'onMapLoad' | 'onError'>) {
  // Get base map context
  const baseMapContext = useMapContext();
  
  // Enhanced state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<PlaceSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Get all available places from regions
  const availablePlaces = useMemo(() => {
    return regions.flatMap(region => region.places).filter((place): place is VisitedPlace => place !== undefined);
  }, [regions]);

  // Use existing place search hook for local filtering
  const {
    filteredPlaces,
    categories,
  } = usePlaceSearch(regions, baseMapContext.places || []);

  // Google Places integration
  const {
    searchPlaces: googleSearchPlaces,
    autocompleteResults,
    clearSearchResults,
  } = useGooglePlaces(baseMapContext.getMap() || undefined);

  // Enhanced search function that combines local and Google search
  const searchPlaces = useCallback(async (query: string): Promise<PlaceSearchResult[]> => {
    if (!query.trim()) {
      setSearchResults([]);
      return [];
    }

    setIsSearching(true);
    setSearchQuery(query);

    try {
      // Search Google Places
      const googleResults = await googleSearchPlaces(query);
      setSearchResults(googleResults);

      // Add to search history
      if (query.trim() && !searchHistory.includes(query.trim())) {
        setSearchHistory(prev => [query.trim(), ...prev.slice(0, 9)]); // Keep last 10 searches
      }

      return googleResults;
    } catch (error) {
      console.error('Enhanced search failed:', error);
      setSearchResults([]);
      return [];
    } finally {
      setIsSearching(false);
    }
  }, [googleSearchPlaces, searchHistory]);

  // Filter places by category and query
  const filterPlaces = useCallback((category: string, query: string) => {
    setSelectedCategory(category);
    setSearchQuery(query);
  }, []);

  // Add place with context awareness
  const addPlace = useCallback((place: VisitedPlace) => {
    // Add to map context
    const currentPlaces = baseMapContext.places ?? [];
    if (!currentPlaces.find(p => p.id === place.id)) {
      baseMapContext.setPlaces([...currentPlaces, place]);
      onPlaceAdd?.(place);
    }
  }, [baseMapContext, onPlaceAdd]);

  // Remove place
  const removePlace = useCallback((placeId: string) => {
    const updatedPlaces = (baseMapContext.places ?? []).filter(p => p.id !== placeId);
    baseMapContext.setPlaces(updatedPlaces);
    
    // Clear selection if removing selected place
    if (baseMapContext.selectedPlace?.id === placeId) {
      baseMapContext.setSelectedPlace(null);
    }
    
    onPlaceRemove?.(placeId);
  }, [baseMapContext, onPlaceRemove]);

  // Clear search state
  const clearSearch = useCallback(() => {
    setSearchQuery('');
    setSearchResults([]);
    setIsSearching(false);
    clearSearchResults();
  }, [clearSearchResults]);

  // Memoize the enhanced context value
  const enhancedContextValue = useMemo((): EnhancedMapContextValue => ({
    // Spread all base context values
    ...baseMapContext,
    
    // Enhanced search state
    searchQuery,
    searchResults,
    isSearching,
    searchHistory,
    
    // Place management state
    availablePlaces,
    filteredPlaces,
    selectedCategory,
    
    // Enhanced actions
    searchPlaces,
    filterPlaces,
    addPlace,
    removePlace,
    clearSearch,
    setSearchQuery,
    setSelectedCategory,
  }), [
    baseMapContext,
    searchQuery,
    searchResults,
    isSearching,
    searchHistory,
    availablePlaces,
    filteredPlaces,
    selectedCategory,
    searchPlaces,
    filterPlaces,
    addPlace,
    removePlace,
    clearSearch,
  ]);

  return (
    <EnhancedMapContext.Provider value={enhancedContextValue}>
      {children}
    </EnhancedMapContext.Provider>
  );
}

export function EnhancedMapProvider({
  children,
  features = ['markers'],
  regions = [],
  onMapLoad,
  onError,
  onPlaceAdd,
  onPlaceRemove,
}: EnhancedMapProviderProps) {
  return (
    <MapProvider features={features} onMapLoad={onMapLoad} onError={onError}>
      <EnhancedMapProviderInner
        regions={regions}
        onPlaceAdd={onPlaceAdd}
        onPlaceRemove={onPlaceRemove}
      >
        {children}
      </EnhancedMapProviderInner>
    </MapProvider>
  );
}

// Hook to use the enhanced map context
export function useEnhancedMapContext(): EnhancedMapContextValue {
  const context = useContext(EnhancedMapContext);

  if (!context) {
    throw new Error('useEnhancedMapContext must be used within an EnhancedMapProvider');
  }

  return context;
}

// Convenience hooks for specific enhanced features
export const useMapSearch = () => {
  const {
    searchQuery,
    searchResults,
    isSearching,
    searchHistory,
    searchPlaces,
    clearSearch,
    setSearchQuery,
  } = useEnhancedMapContext();
  
  return {
    searchQuery,
    searchResults,
    isSearching,
    searchHistory,
    searchPlaces,
    clearSearch,
    setSearchQuery,
  };
};

export const useMapPlaceManagement = () => {
  const {
    availablePlaces,
    filteredPlaces,
    selectedCategory,
    addPlace,
    removePlace,
    filterPlaces,
    setSelectedCategory,
  } = useEnhancedMapContext();
  
  return {
    availablePlaces,
    filteredPlaces,
    selectedCategory,
    addPlace,
    removePlace,
    filterPlaces,
    setSelectedCategory,
  };
};

// Types are already exported in their declarations
