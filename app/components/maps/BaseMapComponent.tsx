/**
 * Base Map Component
 *
 * Unified map rendering component with configurable features and modes.
 * Serves as the foundation for both route planning and venue discovery.
 * Extends the existing GoogleMap component with enhanced abstractions.
 */

import React, { useCallback, useMemo, useRef } from 'react';
import { GoogleMap as GoogleMapComponent, TrafficLayer } from '@react-google-maps/api';
import { useGoogleMaps } from '~/components/maps/GoogleMapsLoader';
import { useMapContext } from './MapProvider';
import { MarkerLayer } from './MapFeatures/MarkerLayer';
import { RouteLayer } from './MapFeatures/RouteLayer';
import { SearchOverlay } from './MapFeatures/SearchOverlay';
import { useGestures } from '~/hooks/useGestures';
import { showSuccess, showError, showInfo } from '~/components/wanderlust/NotificationSystem';
import type { MapFeature } from '~/hooks/useMapFeatures';
import type { VisitedPlace } from '~/types/wanderlust';
import { cn } from '~/lib/utils';

export type MapMode = 'route-planning' | 'venue-discovery' | 'general';

export interface MarkerContext {
  mode: MapMode;
  isSelected: boolean;
  index?: number;
  totalCount: number;
}

export interface MarkerConfig {
  icon: google.maps.Symbol | google.maps.Icon | string;
  animation?: google.maps.Animation;
  zIndex?: number;
  title?: string;
}

export interface BaseMapComponentProps {
  // Core configuration
  features?: MapFeature[];
  mode?: MapMode;

  // Data
  places?: VisitedPlace[]; // Add places prop

  // Map settings
  initialCenter?: { lat: number; lng: number };
  initialZoom?: number;
  mapType?: 'roadmap' | 'satellite';

  // Event handlers
  onMapLoad?: (map: google.maps.Map) => void;
  onPlaceSelect?: (place: VisitedPlace) => void;
  onMapClick?: (event: google.maps.MapMouseEvent) => void;
  onError?: (error: Error) => void;

  // Customization
  customMarkerRenderer?: (place: VisitedPlace, context: MarkerContext) => MarkerConfig;
  customStyles?: google.maps.MapTypeStyle[];

  // Layout
  children?: React.ReactNode;
  className?: string;
  containerStyle?: React.CSSProperties;

  // Feature-specific props
  showTrafficLayer?: boolean;
  enableSearch?: boolean;
  enableGestures?: boolean;
}

// Default map container style
const defaultMapContainerStyle: React.CSSProperties = {
  width: '100%',
  height: '100%',
};

// FIFA Club World Cup 2025™ map styling
const defaultMapStyles: google.maps.MapTypeStyle[] = [
  {
    featureType: 'poi',
    elementType: 'labels',
    stylers: [{ visibility: 'simplified' }],
  },
  {
    featureType: 'road',
    elementType: 'labels',
    stylers: [{ visibility: 'simplified' }],
  },
];

// Mode-specific map configurations
const getModeConfig = (mode: MapMode) => {
  switch (mode) {
    case 'route-planning':
      return {
        defaultFeatures: ['markers', 'routes', 'traffic'] as MapFeature[],
        gestureHandling: 'greedy' as const,
        zoomControl: true,
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: false,
      };
    case 'venue-discovery':
      return {
        defaultFeatures: ['markers', 'search'] as MapFeature[],
        gestureHandling: 'greedy' as const,
        zoomControl: true,
        mapTypeControl: true,
        streetViewControl: false,
        fullscreenControl: true,
      };
    case 'general':
    default:
      return {
        defaultFeatures: ['markers'] as MapFeature[],
        gestureHandling: 'greedy' as const,
        zoomControl: true,
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: false,
      };
  }
};

export function BaseMapComponent({
  features,
  mode = 'general',
  initialCenter,
  initialZoom,
  mapType,
  onMapLoad,
  onPlaceSelect,
  onMapClick,
  onError,
  customMarkerRenderer,
  customStyles,
  children,
  className,
  containerStyle = defaultMapContainerStyle,
  showTrafficLayer,
  enableSearch,
  enableGestures = true,
}: BaseMapComponentProps) {
  const trafficLayerRef = useRef<google.maps.TrafficLayer | null>(null);
  const modeConfig = getModeConfig(mode);

  const {
    center,
    zoom,
    mapType: contextMapType,
    showTraffic,
    setMapReady,
    setInteracting,
    setSelectedPlace,
    isFeatureEnabled,
    places,
    selectedPlace,
  } = useMapContext();

  // Google Maps loading state
  const { isLoaded: isGoogleMapsLoaded, loadError } = useGoogleMaps();

  // Use props or context values
  const effectiveCenter = initialCenter || center;
  const effectiveZoom = initialZoom || zoom;
  const effectiveMapType = mapType || contextMapType;
  const effectiveShowTraffic = showTrafficLayer ?? showTraffic;

  // Map options with mode-specific configurations
  const mapOptions = useMemo((): google.maps.MapOptions => ({
    disableDefaultUI: true,
    zoomControl: modeConfig.zoomControl,
    mapTypeControl: modeConfig.mapTypeControl,
    streetViewControl: modeConfig.streetViewControl,
    fullscreenControl: modeConfig.fullscreenControl,
    styles: customStyles || defaultMapStyles,
    gestureHandling: modeConfig.gestureHandling,
    backgroundColor: '#000000',
    mapTypeId: effectiveMapType,
  }), [effectiveMapType, customStyles, modeConfig]);

  // Handle map load with mode-specific setup
  const handleMapLoad = useCallback((map: google.maps.Map) => {
    try {
      setMapReady(true);

      // Set up basic event listeners
      const handleDragStart = () => setInteracting(true);
      const handleDragEnd = () => setInteracting(false);

      map.addListener('dragstart', handleDragStart);
      map.addListener('dragend', handleDragEnd);
      map.addListener('zoom_changed', handleDragStart);
      map.addListener('idle', handleDragEnd);

      // Handle traffic layer
      if (effectiveShowTraffic) {
        trafficLayerRef.current = new google.maps.TrafficLayer();
        trafficLayerRef.current.setMap(map);
      }

      onMapLoad?.(map);

      // Mode-specific success messages
      const modeMessages = {
        'route-planning': 'Route planner map loaded successfully',
        'venue-discovery': 'Venue discovery map loaded successfully',
        'general': 'Interactive map loaded successfully',
      };

      showSuccess('Map Ready', modeMessages[mode]);
    } catch (error) {
      console.error(`Error loading ${mode} map:`, error);
      const mapError = error instanceof Error ? error : new Error(`Failed to initialize ${mode} map`);
      onError?.(mapError);
      showError('Map Error', `Unable to initialize ${mode} map`);
    }
  }, [setMapReady, setInteracting, effectiveShowTraffic, onMapLoad, onError, mode]);

  // Handle map click with custom handler support
  const handleMapClick = useCallback((event: google.maps.MapMouseEvent) => {
    // Always deselect markers on map click
    setSelectedPlace(null);

    // Call custom handler if provided
    onMapClick?.(event);
  }, [setSelectedPlace, onMapClick]);

  // Handle place selection with mode-aware behavior
  const handlePlaceSelect = useCallback((place: VisitedPlace) => {
    setSelectedPlace(place);
    onPlaceSelect?.(place);

    // Mode-specific feedback
    const modeActions = {
      'route-planning': 'Added to route planning',
      'venue-discovery': 'Venue selected for exploration',
      'general': 'Place selected',
    };

    showInfo(modeActions[mode], place.name);
  }, [setSelectedPlace, onPlaceSelect, mode]);

  // Create marker context for custom renderers
  const createMarkerContext = useCallback((place: VisitedPlace, index: number): MarkerContext => ({
    mode,
    isSelected: selectedPlace?.id === place.id,
    index,
    totalCount: places?.length ?? 0,
  }), [mode, selectedPlace, places?.length]);

  // Handle search place selection (convert from Google Places format)
  const handleSearchPlaceSelect = useCallback((place: any) => {
    // Convert PlaceSearchResult to VisitedPlace format
    const visitedPlace: VisitedPlace = {
      id: place.place_id || 'search-result',
      name: place.name || place.formatted_address || 'Unknown Place',
      description: { en: place.formatted_address || '', fr: '', ar: '' },
      coordinates: {
        latitude: place.geometry?.location?.lat() || 0,
        longitude: place.geometry?.location?.lng() || 0,
      },
      category: 'landmark' as const,
      rating: place.rating || 0,
      visitDate: new Date().toISOString(),
      icon: '📍',
      city: '',
      region: '',
      revisitPotential: 'Worth a Look' as const,
    };

    handlePlaceSelect(visitedPlace);
  }, [handlePlaceSelect]);

  // Gesture handling with mode-aware configuration
  const { ref: gestureRef } = useGestures({
    onLongPress: () => {
      if (isGoogleMapsLoaded) {
        const modeActions = {
          'route-planning': 'Long press to add waypoint',
          'venue-discovery': 'Long press to explore venue',
          'general': 'Long press detected',
        };
        showInfo('Gesture', modeActions[mode]);
      }
    },
    onDragStart: () => setInteracting(true),
    onDragEnd: () => setInteracting(false),
    config: {
      longPressDelay: 500,
      swipeThreshold: 44,
      velocityThreshold: 0.5,
      snapAnimationDuration: 300,
    },
    disabled: !enableGestures || !isGoogleMapsLoaded,
  });

  // Handle loading errors with FIFA design system
  if (loadError) {
    return (
      <div className="h-full rounded-lg overflow-hidden relative">
        <div className="absolute inset-0 bg-gradient-to-br from-black/70 to-black/50 backdrop-blur-2xl" />
        <div className="absolute inset-0 bg-gradient-to-br from-[#FFD700]/10 to-[#DC2626]/10" />
        <div className="relative h-full flex items-center justify-center border-2 border-dashed border-[#FFD700]/30">
          <div className="text-center p-8 max-w-md">
            <div className="text-6xl mb-4 filter drop-shadow-lg">🗺️</div>
            <h3 className="text-lg font-semibold text-white mb-2">Map Unavailable</h3>
            <p className="text-sm text-white/80 mb-4">
              Unable to load {mode.replace('-', ' ')} map. Please check your internet connection.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Loading state with FIFA design system
  if (!isGoogleMapsLoaded) {
    return (
      <div className="h-full rounded-lg overflow-hidden shadow-lg relative">
        <div className="relative h-full bg-gradient-to-br from-black/70 to-black/50 backdrop-blur-2xl">
          <div className="absolute inset-0 bg-gradient-to-br from-[#FFD700]/20 to-[#DC2626]/20 animate-pulse" />
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <div className="text-center bg-black/40 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-[#FFD700]/30">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FFD700] mx-auto mb-2"></div>
              <p className="text-sm text-white/90">Loading {mode.replace('-', ' ')} map...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={enableGestures ? gestureRef as React.RefObject<HTMLDivElement> : undefined}
      className={cn(
        "relative h-full bg-gradient-to-br from-black/70 to-black/50 rounded-lg overflow-hidden shadow-lg",
        className
      )}
    >
      {/* Google Map */}
      <GoogleMapComponent
        mapContainerStyle={containerStyle}
        center={effectiveCenter}
        zoom={effectiveZoom}
        options={mapOptions}
        onLoad={handleMapLoad}
        onClick={handleMapClick}
      >
        {/* Traffic Layer */}
        {effectiveShowTraffic && <TrafficLayer />}

        {/* Marker Layer with custom renderer support */}
        <MarkerLayer
          places={places} // Pass the places prop received by BaseMapComponent
          onMarkerClick={handlePlaceSelect}
          customMarkerIcon={customMarkerRenderer}
          mode={mode}
        />

        {/* Route Layer - only show if routes feature is enabled */}
        {isFeatureEnabled('routes') && <RouteLayer />}

        {/* Custom children */}
        {children}
      </GoogleMapComponent>

      {/* Search Overlay - show if search is enabled */}
      {(enableSearch ?? isFeatureEnabled('search')) && (
        <SearchOverlay
          onPlaceSelect={handleSearchPlaceSelect}
          placeholder={`Search ${mode.replace('-', ' ')} locations...`}
        />
      )}
    </div>
  );
}
