/**
 * Lazy-Loaded Map Components
 * 
 * Implements React.lazy() code splitting for map-related components
 * to reduce initial bundle size and improve performance.
 * 
 * Features:
 * - Progressive loading with skeleton states
 * - Error boundaries for failed loads
 * - Performance monitoring
 * - Preloading strategies
 */

import React, { Suspense, lazy, useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Skeleton } from '~/components/ui/skeleton';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Button } from '~/components/ui/button';
import { Loader2, AlertTriangle, RefreshCw } from 'lucide-react';

// Lazy-loaded map components
const BaseMapComponent = lazy(() => 
  import('./BaseMapComponent').then(module => ({ default: module.BaseMapComponent }))
);

const EnhancedMapProvider = lazy(() => 
  import('./EnhancedMapProvider').then(module => ({ default: module.EnhancedMapProvider }))
);

const MarkerLayer = lazy(() => 
  import('./MapFeatures/MarkerLayer').then(module => ({ default: module.MarkerLayer }))
);

const RouteLayer = lazy(() => 
  import('./MapFeatures/RouteLayer').then(module => ({ default: module.RouteLayer }))
);

const SearchOverlay = lazy(() => 
  import('./MapFeatures/SearchOverlay').then(module => ({ default: module.SearchOverlay }))
);

// Loading skeleton components
const MapSkeleton = () => (
  <div className="relative w-full h-full bg-gray-100 rounded-lg overflow-hidden">
    {/* Map container skeleton */}
    <Skeleton className="w-full h-full" />
    
    {/* Loading overlay */}
    <div className="absolute inset-0 flex items-center justify-center bg-black/10">
      <div className="bg-white rounded-lg p-4 shadow-lg flex items-center space-x-3">
        <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
        <span className="text-sm font-medium text-gray-700">Loading map...</span>
      </div>
    </div>
    
    {/* Control skeletons */}
    <div className="absolute top-4 right-4 space-y-2">
      <Skeleton className="w-10 h-10 rounded" />
      <Skeleton className="w-10 h-10 rounded" />
    </div>
    
    {/* Search skeleton */}
    <div className="absolute top-4 left-4 right-16">
      <Skeleton className="w-full h-10 rounded-lg" />
    </div>
  </div>
);

const ProviderSkeleton = ({ children }: { children: React.ReactNode }) => (
  <div className="relative">
    <div className="absolute inset-0 bg-gray-50 animate-pulse rounded-lg" />
    {children}
  </div>
);

const LayerSkeleton = () => (
  <div className="absolute inset-0 pointer-events-none">
    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
      <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
    </div>
  </div>
);

// Error fallback components
const MapErrorFallback = ({ error, resetErrorBoundary }: { 
  error: Error; 
  resetErrorBoundary: () => void; 
}) => (
  <div className="w-full h-full flex items-center justify-center bg-gray-50 rounded-lg">
    <div className="text-center p-6 max-w-md">
      <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Failed to load map
      </h3>
      <p className="text-sm text-gray-600 mb-4">
        {error.message || 'An error occurred while loading the map component.'}
      </p>
      <Button 
        onClick={resetErrorBoundary}
        variant="outline"
        size="sm"
        className="inline-flex items-center"
      >
        <RefreshCw className="h-4 w-4 mr-2" />
        Try again
      </Button>
    </div>
  </div>
);

const LayerErrorFallback = ({ error, resetErrorBoundary }: { 
  error: Error; 
  resetErrorBoundary: () => void; 
}) => (
  <Alert className="absolute top-4 left-4 right-4 z-50">
    <AlertTriangle className="h-4 w-4" />
    <AlertDescription className="flex items-center justify-between">
      <span>Failed to load map layer: {error.message}</span>
      <Button 
        onClick={resetErrorBoundary}
        variant="ghost"
        size="sm"
      >
        Retry
      </Button>
    </AlertDescription>
  </Alert>
);

// Performance monitoring
const useComponentLoadTime = (componentName: string) => {
  const [loadTime, setLoadTime] = useState<number | null>(null);

  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      setLoadTime(duration);
      
      // Log performance metrics
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} load time: ${duration.toFixed(2)}ms`);
      }
    };
  }, [componentName]);

  return loadTime;
};

// Preloading utilities
export const preloadMapComponents = () => {
  // Preload critical map components
  import('./BaseMapComponent');
  import('./EnhancedMapProvider');
  import('./MapFeatures/MarkerLayer');
};

export const preloadRouteComponents = () => {
  // Preload route-specific components
  import('./MapFeatures/RouteLayer');
  import('../route-planner/EnhancedRouteMap');
};

export const preloadVenueComponents = () => {
  // Preload venue-specific components
  import('./MapFeatures/SearchOverlay');
  import('../venue-discovery/VenueExplorerMap');
};

// Lazy wrapper components with performance monitoring
export const LazyBaseMapComponent = React.forwardRef<
  HTMLDivElement,
  Omit<React.ComponentProps<typeof BaseMapComponent>, 'ref'>
>((props, ref) => {
  const loadTime = useComponentLoadTime('BaseMapComponent');

  return (
    <ErrorBoundary
      FallbackComponent={MapErrorFallback}
      onError={(error) => {
        console.error('BaseMapComponent failed to load:', error);
      }}
    >
      <Suspense fallback={<MapSkeleton />}>
        <div ref={ref}>
          <BaseMapComponent {...props} />
        </div>
      </Suspense>
    </ErrorBoundary>
  );
});

LazyBaseMapComponent.displayName = 'LazyBaseMapComponent';

export const LazyEnhancedMapProvider = ({ 
  children, 
  ...props 
}: React.ComponentProps<typeof EnhancedMapProvider>) => {
  const loadTime = useComponentLoadTime('EnhancedMapProvider');

  return (
    <ErrorBoundary
      FallbackComponent={({ error, resetErrorBoundary }) => (
        <ProviderSkeleton>
          <MapErrorFallback error={error} resetErrorBoundary={resetErrorBoundary} />
        </ProviderSkeleton>
      )}
      onError={(error) => {
        console.error('EnhancedMapProvider failed to load:', error);
      }}
    >
      <Suspense fallback={<ProviderSkeleton>{children}</ProviderSkeleton>}>
        <EnhancedMapProvider {...props}>
          {children}
        </EnhancedMapProvider>
      </Suspense>
    </ErrorBoundary>
  );
};

export const LazyMarkerLayer = (props: React.ComponentProps<typeof MarkerLayer>) => {
  return (
    <ErrorBoundary
      FallbackComponent={LayerErrorFallback}
      onError={(error) => {
        console.error('MarkerLayer failed to load:', error);
      }}
    >
      <Suspense fallback={<LayerSkeleton />}>
        <MarkerLayer {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

export const LazyRouteLayer = (props: React.ComponentProps<typeof RouteLayer>) => {
  return (
    <ErrorBoundary
      FallbackComponent={LayerErrorFallback}
      onError={(error) => {
        console.error('RouteLayer failed to load:', error);
      }}
    >
      <Suspense fallback={<LayerSkeleton />}>
        <RouteLayer {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

export const LazySearchOverlay = (props: React.ComponentProps<typeof SearchOverlay>) => {
  return (
    <ErrorBoundary
      FallbackComponent={LayerErrorFallback}
      onError={(error) => {
        console.error('SearchOverlay failed to load:', error);
      }}
    >
      <Suspense fallback={<LayerSkeleton />}>
        <SearchOverlay {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

// Bundle analysis utilities
export const getBundleInfo = () => {
  if (process.env.NODE_ENV === 'development') {
    return {
      lazyComponents: [
        'BaseMapComponent',
        'EnhancedMapProvider', 
        'MarkerLayer',
        'RouteLayer',
        'SearchOverlay',
      ],
      preloadStrategies: [
        'preloadMapComponents',
        'preloadRouteComponents', 
        'preloadVenueComponents',
      ],
    };
  }
  return null;
};
