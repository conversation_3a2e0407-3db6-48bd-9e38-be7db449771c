import React from 'react';
import type { RouteTemplate, RouteWaypoint } from '~/types/route-planning';
import { Card } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { cn } from '~/lib/utils';

export interface RouteTemplatesPanelProps {
  onTemplateLoad: (template: RouteTemplate) => void;
  className?: string;
}

export function RouteTemplatesPanel({
  onTemplateLoad,
  className
}: RouteTemplatesPanelProps) {
  return (
    <div className={cn("p-4 space-y-4", className)}>
      {/* Template implementation */}
    </div>
  );
}

export interface RouteExportPanelProps {
  waypoints: RouteWaypoint[];
  routeName: string;
  routeDescription: string;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
  onSave: () => void;
  enableSharing?: boolean;
  className?: string;
}

export function RouteExportPanel({
  waypoints,
  routeName,
  routeDescription,
  onNameChange,
  onDescriptionChange,
  onSave,
  enableSharing = true,
  className
}: RouteExportPanelProps) {
  return (
    <div className={cn("p-4 space-y-4", className)}>
      {/* Export implementation */}
    </div>
  );
}
