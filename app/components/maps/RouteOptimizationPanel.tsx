import React from 'react';
import { Card } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Zap, X } from 'lucide-react';
import type { RouteWaypoint } from '~/types/route-planning';

interface RouteStats {
  totalWaypoints: number;
  estimatedDuration: number;
  trafficDelay: number;
  optimizable: boolean;
}

interface RouteOptimizationPanelProps {
  waypoints: RouteWaypoint[];
  isOptimizing: boolean;
  routeStats: RouteStats;
  onOptimize: () => void;
  onClose: () => void;
}

export function RouteOptimizationPanel({
  waypoints,
  isOptimizing,
  routeStats,
  onOptimize,
  onClose
}: RouteOptimizationPanelProps) {
  return (
    <Card className="absolute right-4 top-20 w-72 bg-black/90 border-yellow-500/30 backdrop-blur-sm z-10">
      <div className="p-3">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4 text-yellow-400" />
            <h3 className="text-sm font-medium text-white">Route Optimization</h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0 hover:bg-white/10"
          >
            <X className="h-4 w-4 text-white/70" />
          </Button>
        </div>

        <div className="space-y-3">
          {/* Route Statistics */}
          <div className="bg-white/5 rounded-lg p-2 text-xs text-white/70">
            <div className="flex justify-between mb-1">
              <span>Total Waypoints:</span>
              <span className="font-medium text-white">{routeStats.totalWaypoints}</span>
            </div>
            <div className="flex justify-between mb-1">
              <span>Current Duration:</span>
              <span className="font-medium text-white">
                {Math.round(routeStats.estimatedDuration)}min
              </span>
            </div>
            {routeStats.trafficDelay > 0 && (
              <div className="flex justify-between text-yellow-400">
                <span>Traffic Delay:</span>
                <span className="font-medium">+{routeStats.trafficDelay}min</span>
              </div>
            )}
          </div>

          {/* Optimization Action */}
          <Button
            onClick={onOptimize}
            disabled={isOptimizing || !routeStats.optimizable}
            className="w-full bg-yellow-500 text-black hover:bg-yellow-400 disabled:opacity-50 disabled:hover:bg-yellow-500"
          >
            {isOptimizing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2" />
                Optimizing Route...
              </>
            ) : (
              <>
                <Zap className="h-4 w-4 mr-2" />
                Optimize Route
              </>
            )}
          </Button>

          {/* Optimization Tips */}
          <div className="text-xs text-white/60">
            {routeStats.optimizable ? (
              "Route will be optimized for shortest total travel time while maintaining start and end points."
            ) : (
              "Need at least 3 waypoints to optimize route order."
            )}
          </div>
        </div>
      </div>
    </Card>
  );
}
