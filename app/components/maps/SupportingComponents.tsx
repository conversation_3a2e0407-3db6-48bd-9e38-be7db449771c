/**
 * Supporting Components for Enhanced Route Planning
 * 
 * These components work with EnhancedRouteMap and SmartRouteBuilder
 * to provide complete route planning functionality.
 */

import React, { useState, useCallback } from 'react';
import { cn } from '~/lib/utils';
import type { RouteWaypoint, RouteTemplate, TrafficInfo } from '~/types/route-planning';
import { Card } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { useMapContext } from '../shared/MapProvider';
import { showInfo } from '~/components/wanderlust/NotificationSystem';


// ====================
// Traffic-Aware Route Layer
// ====================
export interface TrafficAwareRouteLayerProps {
  waypoints: RouteWaypoint[];
  routeColor?: string;
  showTraffic?: boolean;
  showETA?: boolean;
  showDistance?: boolean;
  onTrafficInfoUpdate?: (info: TrafficInfo) => void;
}

export function TrafficAwareRouteLayer({
  waypoints,
  routeColor = '#3B82F6',
  showTraffic = true,
  showETA = true,
  showDistance = true
}: TrafficAwareRouteLayerProps) {
  const [directionsRenderer, setDirectionsRenderer] = useState<google.maps.DirectionsRenderer | null>(null);
  const [routeInfo, setRouteInfo] = useState<{
    distance: string;
    duration: string;
    trafficDuration: string;
  } | null>(null);

  // Initialize directions renderer
  React.useEffect(() => {
    if (waypoints.length >= 2) {
      const renderer = new google.maps.DirectionsRenderer({
        polylineOptions: {
          strokeColor: routeColor,
          strokeWeight: 6,
          strokeOpacity: 0.8,
        },
        suppressMarkers: true, // We handle markers in MarkerLayer
        suppressInfoWindows: false,
      });
      
      setDirectionsRenderer(renderer);
      
      return () => {
        renderer.setMap(null);
      };
    }
  }, [waypoints.length, routeColor]);

  // Calculate and display route
  React.useEffect(() => {
    if (directionsRenderer && waypoints.length >= 2) {
      const directionsService = new google.maps.DirectionsService();
      
      const origin = waypoints[0];
      const destination = waypoints[waypoints.length - 1];
      const waypointsToPass = waypoints.slice(1, -1).map(waypoint => ({
        location: new google.maps.LatLng(
          waypoint.location.lat,
          waypoint.location.lng
        ),
        stopover: true,
      }));

      directionsService.route({
        origin: new google.maps.LatLng(
          origin.location.lat,
          origin.location.lng
        ),
        destination: new google.maps.LatLng(
          destination.location.lat,
          destination.location.lng
        ),
        waypoints: waypointsToPass,
        optimizeWaypoints: false, // Handled separately in optimization
        travelMode: google.maps.TravelMode.DRIVING,
        drivingOptions: showTraffic ? {
          departureTime: new Date(),
          trafficModel: google.maps.TrafficModel.BEST_GUESS,
        } : undefined,
      }, (result, status) => {
        if (status === google.maps.DirectionsStatus.OK && result) {
          directionsRenderer.setDirections(result);
          
          // Extract route information
          const route = result.routes[0];
          const leg = route.legs[0];
          
          const totalDistance = route.legs.reduce((sum, leg) => 
            sum + (leg.distance?.value ?? 0), 0);
          const totalDuration = route.legs.reduce((sum, leg) => 
            sum + (leg.duration?.value ?? 0), 0);
          const trafficDuration = showTraffic && leg.duration_in_traffic?.value
            ? leg.duration_in_traffic.value
            : null;
            
          setRouteInfo({
            distance: (totalDistance / 1000).toFixed(1) + ' km',
            duration: Math.ceil(totalDuration / 60) + ' min',
            trafficDuration: trafficDuration 
              ? Math.ceil(trafficDuration / 60) + ' min'
              : '',
          });
        }
      });
    }
  }, [directionsRenderer, waypoints, showTraffic]);

  return null; // This component manages Google Maps objects directly
}

// ====================
// Waypoint Management Overlay
// ====================
export interface WaypointManagementOverlayProps {
  waypoints: RouteWaypoint[];
  maxWaypoints: number;
  allowReordering: boolean;
  onWaypointRemove: (waypointId: string) => void;
  onWaypointReorder: (waypoints: RouteWaypoint[]) => void;
  routeStats: {
    totalWaypoints: number;
    estimatedDuration: number;
    trafficDelay: number;
    optimizable: boolean;
  };
}

export function WaypointManagementOverlay({
  waypoints,
  maxWaypoints,
  allowReordering,
  onWaypointRemove,
  onWaypointReorder,
  routeStats
}: WaypointManagementOverlayProps) {
  const [isReordering, setIsReordering] = useState(false);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  if (waypoints.length === 0) return null;

  return (
    <div className="absolute bottom-4 left-4 z-10 max-w-sm">
      <div className="bg-black/80 backdrop-blur-sm rounded-lg p-4 text-white border border-[#FFD700]/30">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-sm">Route Waypoints</h3>
          <span className="text-xs text-white/70">
            {waypoints.length}/{maxWaypoints}
          </span>
        </div>
        
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {waypoints.map((waypoint, index) => (
            <div
              key={waypoint.id}
              className="flex items-center space-x-2 p-2 rounded bg-white/10 hover:bg-white/20 transition-colors"
            >
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-[#3B82F6] flex items-center justify-center text-xs font-bold">
                {waypoint.isStart ? 'S' : waypoint.isEnd ? 'E' : index}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{waypoint.name}</p>
                {waypoint.estimatedTravelTime && (
                  <p className="text-xs text-white/70">~{waypoint.estimatedTravelTime}min</p>
                )}
              </div>
              <button
                onClick={() => onWaypointRemove(waypoint.id)}
                className="flex-shrink-0 text-red-400 hover:text-red-300 p-1"
                title="Remove waypoint"
              >
                ×
              </button>
            </div>
          ))}
        </div>
        
        {routeStats.estimatedDuration > 0 && (
          <div className="mt-3 pt-3 border-t border-white/20 text-xs text-white/80">
            Total: ~{Math.round(routeStats.estimatedDuration)}min
            {routeStats.trafficDelay > 0 && (
              <span className="text-yellow-400"> (+{routeStats.trafficDelay}min traffic)</span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

// ====================
// Route Optimization Panel
// ====================
export interface RouteOptimizationPanelProps {
  waypoints: RouteWaypoint[];
  isOptimizing: boolean;
  routeStats: {
    totalWaypoints: number;
    estimatedDuration: number;
    trafficDelay: number;
    optimizable: boolean;
  };
  onOptimize: () => void;
  onClose: () => void;
}

export function RouteOptimizationPanel({
  waypoints,
  isOptimizing,
  routeStats,
  onOptimize,
  onClose
}: RouteOptimizationPanelProps) {
  return (
    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
      <div className="bg-white rounded-lg shadow-2xl border border-gray-200 p-6 max-w-md w-full">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Optimize Route</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            ×
          </button>
        </div>
        
        <div className="space-y-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Current Route</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <div>📍 {routeStats.totalWaypoints} waypoints</div>
              <div>⏱️ ~{Math.round(routeStats.estimatedDuration)} minutes</div>
              {routeStats.trafficDelay > 0 && (
                <div className="text-orange-600">🚦 +{routeStats.trafficDelay} min traffic delay</div>
              )}
            </div>
          </div>
          
          <div className="text-sm text-gray-600">
            Route optimization will reorder your waypoints to minimize travel time while keeping your start and end points fixed.
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={onOptimize}
              disabled={isOptimizing}
              className="flex-1 bg-[#3B82F6] hover:bg-[#2563EB] disabled:opacity-50 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              {isOptimizing ? (
                <>
                  <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Optimizing...
                </>
              ) : (
                'Optimize Route'
              )}
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// ====================
// Route Templates Panel
// ====================
export interface RouteTemplatesPanelProps {
  onTemplateLoad: (template: RouteTemplate) => void;
  className?: string;
}

export function RouteTemplatesPanel({
  onTemplateLoad,
  className
}: RouteTemplatesPanelProps) {
  // Mock template data - replace with actual data source
  const templates: RouteTemplate[] = [
    {
      id: 'template-1',
      name: 'FIFA World Cup 2025 Venues',
      description: 'Tour of all FIFA Club World Cup 2025™ venues',
      waypoints: [], // Would contain actual waypoints
      tags: ['sports', 'fifa', 'official'],
      estimatedDuration: 480, // 8 hours in minutes
      difficulty: 'Moderate',
      estimatedDistance: '10 km',
      categories: ['sports', 'entertainment']
    },
    {
      id: 'template-2',
      name: 'Tunis Cultural Tour',
      description: 'Explore historic and cultural landmarks',
      waypoints: [],
      tags: ['culture', 'history', 'unesco'],
      estimatedDuration: 240, // 4 hours in minutes
      difficulty: 'Easy',
      estimatedDistance: '5 km',
      categories: ['culture', 'history']
    }
  ];

  return (
    <div className={cn("p-4 space-y-4", className)}>
      <div>
        <h3 className="font-semibold text-gray-900 mb-1">Route Templates</h3>
        <p className="text-sm text-gray-600">Choose from pre-built routes or your saved routes</p>
      </div>
      
      <div className="space-y-3">
        {templates.map(template => (
          <div
            key={template.id}
            onClick={() => onTemplateLoad(template)}
            className="border border-gray-200 rounded-lg p-4 hover:border-[#3B82F6] hover:bg-blue-50 cursor-pointer transition-colors"
          >
            <div className="flex items-start justify-between mb-2">
              <h4 className="font-medium text-gray-900">{template.name}</h4>
              <span className={cn(
                "px-2 py-1 rounded-full text-xs font-medium",
                template.difficulty === 'Easy' ? "bg-green-100 text-green-800" :
                template.difficulty === 'Moderate' ? "bg-yellow-100 text-yellow-800" :
                "bg-red-100 text-red-800"
              )}>
                {template.difficulty}
              </span>
            </div>
            <p className="text-sm text-gray-600 mb-3">{template.description}</p>
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>⏱️ ~{Math.floor(template.estimatedDuration / 60)}h {template.estimatedDuration % 60}min</span>
              <div className="flex space-x-1">
                {template.tags.slice(0, 2).map(tag => (
                  <span key={tag} className="bg-gray-100 px-2 py-1 rounded">
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// ====================
// Route Export Panel
// ====================
export interface RouteExportPanelProps {
  waypoints: RouteWaypoint[];
  routeName: string;
  routeDescription: string;
  onNameChange: (name: string) => void;
  onDescriptionChange: (description: string) => void;
  onSave: () => void;
  enableSharing?: boolean;
  className?: string;
}

export function RouteExportPanel({
  waypoints,
  routeName,
  routeDescription,
  onNameChange,
  onDescriptionChange,
  onSave,
  enableSharing = true,
  className
}: RouteExportPanelProps) {
  const [exportFormat, setExportFormat] = useState<'gpx' | 'kml' | 'json'>('gpx');

  const handleExport = useCallback(() => {
    // Implementation would depend on chosen format
    console.log(`Exporting route as ${exportFormat}:`, waypoints);
  }, [exportFormat, waypoints]);

  const handleShare = useCallback(() => {
    if (navigator.share) {
      navigator.share({
        title: routeName || 'My Route',
        text: routeDescription || 'Check out this route I created!',
        url: window.location.href,
      });
    }
  }, [routeName, routeDescription]);

  return (
    <div className={cn("p-4 space-y-4", className)}>
      <div>
        <h3 className="font-semibold text-gray-900 mb-1">Export & Save</h3>
        <p className="text-sm text-gray-600">Save your route or export for other apps</p>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Route Name
          </label>
          <input
            type="text"
            value={routeName}
            onChange={(e) => onNameChange(e.target.value)}
            placeholder="Enter route name..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            value={routeDescription}
            onChange={(e) => onDescriptionChange(e.target.value)}
            placeholder="Describe your route..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Export Format
          </label>
          <select
            value={exportFormat}
            onChange={(e) => setExportFormat(e.target.value as any)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#3B82F6] focus:border-transparent"
          >
            <option value="gpx">GPX (GPS Exchange)</option>
            <option value="kml">KML (Google Earth)</option>
            <option value="json">JSON (Web Apps)</option>
          </select>
        </div>

        <div className="space-y-2">
          <button
            onClick={onSave}
            disabled={!routeName.trim() || waypoints.length < 2}
            className="w-full bg-[#3B82F6] hover:bg-[#2563EB] disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            💾 Save as Template
          </button>

          <button
            onClick={handleExport}
            disabled={waypoints.length < 2}
            className="w-full bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            📤 Export Route
          </button>

          {enableSharing && (
            <button
              onClick={handleShare}
              disabled={waypoints.length < 2}
              className="w-full bg-gray-600 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              🔗 Share Route
            </button>
          )}
        </div>
      </div>

      {waypoints.length > 0 && (
        <div className="text-xs text-gray-500 bg-gray-50 rounded-lg p-3">
          <div className="font-medium mb-1">Route Summary:</div>
          <div>📍 {waypoints.length} waypoints</div>
          <div>🎯 {waypoints.filter(w => w.isStart || w.isEnd).length} endpoints</div>
        </div>
      )}
    </div>
  );
}