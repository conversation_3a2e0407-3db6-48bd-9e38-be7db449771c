import React, { useState, useCallback } from 'react';
import { Card } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { MapPin, Route, ArrowUpDown, Trash2 } from 'lucide-react';
import { useMapContext } from './MapProvider';
import type { RouteWaypoint } from '~/types/route-planning';
import { cn } from '~/lib/utils';

interface WaypointManagementOverlayProps {
  className?: string;
  onWaypointRemove?: (waypointId: string) => void;
  onWaypointReorder?: (sourceIndex: number, targetIndex: number) => void;
}

export function WaypointManagementOverlay({
  className,
  onWaypointRemove,
  onWaypointReorder,
}: WaypointManagementOverlayProps) {
  const { currentRoute, places } = useMapContext();
  const [isDragging, setIsDragging] = useState(false);

  // Handle waypoint drag and drop
  const handleDragStart = useCallback((e: React.DragEvent, index: number) => {
    e.dataTransfer.setData('text/plain', index.toString());
    setIsDragging(true);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.currentTarget.classList.add('bg-yellow-500/10');
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.currentTarget.classList.remove('bg-yellow-500/10');
  }, []);

  const handleDrop = useCallback((e: React.DragEvent, targetIndex: number) => {
    e.preventDefault();
    e.currentTarget.classList.remove('bg-yellow-500/10');
    const sourceIndex = parseInt(e.dataTransfer.getData('text/plain'), 10);
    
    if (sourceIndex !== targetIndex) {
      onWaypointReorder?.(sourceIndex, targetIndex);
    }
    
    setIsDragging(false);
  }, [onWaypointReorder]);

  if (!places || places.length === 0) {
    return null;
  }

  return (
    <Card className={cn(
      "absolute left-4 top-4 w-72 bg-black/90 border-yellow-500/30 backdrop-blur-sm shadow-xl z-10",
      isDragging && "ring-2 ring-yellow-500/50",
      className
    )}>
      <div className="p-3">
        <div className="flex items-center gap-2 mb-3">
          <Route className="h-4 w-4 text-yellow-400" />
          <h3 className="text-sm font-medium text-white">Route Waypoints</h3>
          <Badge className="ml-auto bg-yellow-500 text-black">
            {places.length}
          </Badge>
        </div>

        <div className="space-y-2">
          {places.map((waypoint, index) => (
            <div
              key={waypoint.id}
              draggable
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, index)}
              className={cn(
                "flex items-center gap-2 p-2 rounded-md bg-white/5 border border-white/10",
                "hover:bg-white/10 transition-colors cursor-move"
              )}
            >
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <Badge
                  variant="outline"
                  className="bg-yellow-500 text-black border-yellow-500 min-w-[24px]"
                >
                  {index + 1}
                </Badge>
                <div className="flex-1 min-w-0">
                  <div className="text-sm text-white truncate">
                    {waypoint.name}
                  </div>
                  <div className="text-xs text-white/60 truncate">
                    {waypoint.city}
                  </div>
                </div>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => onWaypointRemove?.(waypoint.id)}
                className="h-8 w-8 p-0 hover:bg-red-500/20 hover:text-red-400"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>

        {places.length >= 2 && currentRoute && (
          <div className="mt-3 pt-3 border-t border-white/10">
            <div className="text-xs text-white/60">
              Total Distance: {currentRoute.estimatedDistance}
            </div>
            <div className="text-xs text-white/60">
              Estimated Time: {currentRoute.estimatedDuration}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}
