/**
 * Smart Route Builder Component
 *
 * Integrates unified components for comprehensive route planning:
 * - Enhanced search interface for route planning
 * - Place management panel for waypoints
 * - Enhanced route map with optimization
 * - Real-time route statistics and controls
 */

import React, { useState, useCallback, useMemo } from 'react';
import { EnhancedRouteMap } from './EnhancedRouteMap';
import { EnhancedSearchInterface } from '../shared/EnhancedSearchInterface';
import { PlaceManagementPanel } from '../shared/PlaceManagementPanel';
import { useMapContext } from './MapProvider';
import { useUnifiedPlaceManagement } from '~/hooks/useUnifiedPlaceManagement';
import { showSuccess, showError, showInfo } from '~/components/wanderlust/NotificationSystem';
import type { VisitedPlace, CityRegion } from '~/types/wanderlust';
import type { RouteWaypoint, RouteTemplate } from '~/types/route-planning';
import type { PlaceAction } from '../shared/PlaceManagementPanel';
import { RouteTemplatesPanel, RouteExportPanel } from './RouteComponents';
import { cn } from '~/lib/utils';
import { useTranslation } from 'react-i18next';

export interface SmartRouteBuilderProps {
  initialWaypoints?: RouteWaypoint[];
  maxWaypoints?: number;
  regions: CityRegion[];
  layout?: 'split' | 'stacked' | 'tabs';
  showTemplates?: boolean;
  showExport?: boolean;
  enableVoiceSearch?: boolean;
  enableOfflineMode?: boolean;
  enableRouteSharing?: boolean;
  onRouteComplete?: (waypoints: RouteWaypoint[]) => void;
  onRouteSave?: (route: RouteTemplate) => void;
  onRouteLoad?: (template: RouteTemplate) => void;
  className?: string;
  panelWidth?: string;
}

const visitedPlaceToRouteWaypoint = (place: VisitedPlace, index: number, totalPlaces: number): RouteWaypoint => ({
  ...place,
  location: {
    lat: place.coordinates.latitude,
    lng: place.coordinates.longitude,
  },
  placeData: place, // Store original VisitedPlace for PlaceManagementPanel
  stopover: index > 0,
  waypointIndex: index,
  isStart: index === 0,
  isEnd: index === totalPlaces - 1,
  estimatedTravelTime: 0, // Placeholder, will be updated by route calculation
});

export function SmartRouteBuilder({
  initialWaypoints = [],
  maxWaypoints = 25,
  regions,
  layout = 'split',
  showTemplates = true,
  showExport = true,
  enableVoiceSearch = false,
  enableOfflineMode = false,
  enableRouteSharing = true,
  onRouteComplete,
  onRouteSave,
  onRouteLoad,
  className,
  panelWidth = '400px'
}: SmartRouteBuilderProps) {
  const { t } = useTranslation('smartRouteBuilder');

  const placeManagement = useUnifiedPlaceManagement({
    mode: 'route-planning',
    enableWaypoints: true,
    enableReordering: true,
    maxPlaces: maxWaypoints,
    enableDuplicateCheck: true,
  });

  const {
    state: {
      places: visitedPlaces, // Renamed to visitedPlaces to avoid confusion with RouteWaypoint
      selectedPlace,
      canAddMore,
      totalCount: waypointCount,
    },
    actions: {
      addPlace: addVisitedPlace, // Renamed to addVisitedPlace
      removePlace: removeVisitedPlace, // Renamed
      reorderPlaces: reorderVisitedPlaces, // Renamed
      clearPlaces: clearVisitedPlaces, // Renamed
      selectPlace: selectVisitedPlace, // Renamed
    },
    validation: {
      getValidationMessage,
    },
  } = placeManagement;

  // Convert VisitedPlace[] from useUnifiedPlaceManagement to RouteWaypoint[] for components expecting it
const waypoints: RouteWaypoint[] = useMemo(() => {
  return visitedPlaces.map((place, index) => visitedPlaceToRouteWaypoint(place, index, visitedPlaces.length));
}, [visitedPlaces]);

  const [activeTab, setActiveTab] = useState<'search' | 'waypoints' | 'templates' | 'export'>('search');
  const [isBuilding, setIsBuilding] = useState(false);
  const [routeName, setRouteName] = useState('');
  const [routeDescription, setRouteDescription] = useState('');

  const { setCenter, setZoom } = useMapContext();

  const handlePlaceSelectFromSearch = useCallback((place: VisitedPlace) => {
    const success = addVisitedPlace(place); // Add as VisitedPlace
    if (success) {
      setCenter({
        lat: place.coordinates.latitude,
        lng: place.coordinates.longitude
      });
      showSuccess(t('smartRouteBuilder.waypointAddedTitle'), t('smartRouteBuilder.waypointAddedMessage', { name: place.name }));
      setActiveTab('waypoints');
    } else {
      const message = getValidationMessage(place); // Pass VisitedPlace to validation
      showError(t('smartRouteBuilder.waypointAddFailedTitle'), message || t('smartRouteBuilder.waypointAddFailedGeneric'));
    }
  }, [addVisitedPlace, setCenter, t, getValidationMessage]);

  const handleWaypointAction = useCallback((action: PlaceAction, place: VisitedPlace, index?: number) => {
    switch (action.type) {
      case 'remove':
        removeVisitedPlace(place.id);
        break;
      case 'select':
        if (place) {
          setCenter({
            lat: place.coordinates.latitude,
            lng: place.coordinates.longitude
          });
          setZoom(16);
          selectVisitedPlace(place);
        }
        break;
      case 'reorder':
        if (action.payload && typeof index === 'number') {
          reorderVisitedPlaces(action.payload.from, action.payload.to);
        }
        break;
      case 'clear':
        clearVisitedPlaces();
        break;
      default:
        console.warn(`Unhandled place action: ${action.type}`);
    }
  }, [removeVisitedPlace, selectVisitedPlace, reorderVisitedPlaces, clearVisitedPlaces, setCenter, setZoom]);

  const handleBulkAction = useCallback((action: PlaceAction, places: VisitedPlace[]) => {
    switch (action.type) {
      case 'clear':
        clearVisitedPlaces();
        break;
      case 'optimize':
        console.log("TODO: Implement route optimization for selected waypoints");
        // This would typically involve calling a route optimization service
        // and then updating the waypoints via reorderVisitedPlaces or a new action
        break;
      default:
        console.warn(`Unhandled bulk action: ${action.type}`);
    }
  }, [clearVisitedPlaces]);

  const handleRouteOptimization = useCallback((optimizedWaypoints: RouteWaypoint[]) => {
    // This logic should ideally be moved into useUnifiedPlaceManagement or a dedicated route optimization hook
    // For now, we'll simulate updating the waypoints by re-adding them in optimized order
    clearVisitedPlaces();
    optimizedWaypoints?.forEach(wp => addVisitedPlace(wp.placeData)); // Add as VisitedPlace
    showSuccess(t('smartRouteBuilder.routeOptimizedTitle'), t('smartRouteBuilder.routeOptimizedMessage'));
  }, [clearVisitedPlaces, addVisitedPlace, t]);

  const handleLoadTemplate = useCallback((template: RouteTemplate) => {
    clearVisitedPlaces();
    template.waypoints.forEach(wp => addVisitedPlace(wp)); // Add as VisitedPlace
    setRouteName(template.name);
    setRouteDescription(template.description);
    onRouteLoad?.(template);
    showSuccess(t('smartRouteBuilder.templateLoadedTitle'), t('smartRouteBuilder.templateLoadedMessage', { name: template.name }));
    setActiveTab('waypoints');
  }, [onRouteLoad, clearVisitedPlaces, addVisitedPlace, t]);

  const handleSaveRoute = useCallback(() => {
    if (waypoints.length < 2) {
      showError(t('smartRouteBuilder.cannotSaveTitle'), t('smartRouteBuilder.cannotSaveMessage'));
      return;
    }

    if (!routeName.trim()) {
      showError(t('smartRouteBuilder.nameRequiredTitle'), t('smartRouteBuilder.nameRequiredMessage'));
      return;
    }

    // Convert VisitedPlace[] back to RouteWaypoint[] for saving
    const routeTemplate: RouteTemplate = {
      id: `route-${Date.now()}`,
      name: routeName.trim(),
      description: routeDescription.trim(),
      waypoints: waypoints.map(place => ({
        ...place,
        location: { lat: place.coordinates.latitude, lng: place.coordinates.longitude },
        placeData: place,
        stopover: true, // Assuming all saved waypoints are stopovers
        waypointIndex: 0, // Will be re-indexed on load
        isStart: false,
        isEnd: false,
        estimatedTravelTime: 0, // Placeholder
      })),
      estimatedDuration: parseFloat(waypoints.reduce((sum, w) => sum + (w.estimatedTravelTime || 0), 0).toFixed(2)),
      estimatedDistance: '',
      categories: [],
      difficulty: 'Easy',
      tags: [],
    };

    onRouteSave?.(routeTemplate);
    showSuccess(t('smartRouteBuilder.routeSavedTitle'), t('smartRouteBuilder.routeSavedMessage', { name: routeName }));
  }, [waypoints, routeName, routeDescription, onRouteSave, t]);

  const handleCompleteRoute = useCallback(() => {
    if (waypoints.length < 2) {
      showError(t('smartRouteBuilder.incompleteRouteTitle'), t('smartRouteBuilder.incompleteRouteMessage'));
      return;
    }

    setIsBuilding(true);
    
    setTimeout(() => {
      onRouteComplete?.(waypoints); // onRouteComplete expects RouteWaypoint[]
      showSuccess(t('smartRouteBuilder.routeCompleteTitle'), t('smartRouteBuilder.routeCompleteMessage'));
      setIsBuilding(false);
    }, 1500);
  }, [waypoints, onRouteComplete, t]);

  const routeStats = useMemo(() => ({
    waypointCount: waypoints.length,
    isComplete: waypoints.length >= 2,
    estimatedDuration: waypoints.reduce((sum, w) => sum + (w.estimatedTravelTime || 0), 0),
  }), [waypoints]);

  const renderPanelContent = () => {
    switch (activeTab) {
      case 'search':
        return (
          <EnhancedSearchInterface
            searchMode="route-planning"
            onPlaceSelect={handlePlaceSelectFromSearch}
            regions={regions}
            placeholder={t('smartRouteBuilder.searchPlaceholder')}
            className="h-full"
          />
        );

      case 'waypoints':
        return (
          <PlaceManagementPanel
            mode="waypoints"
            places={waypoints.map(w => w.placeData)} // Pass original VisitedPlace for display
            enableReordering={true}
            onPlaceAction={handleWaypointAction}
            onBulkAction={handleBulkAction}
            title={t('smartRouteBuilder.waypointsPanelTitle', { count: waypoints.length })}
            emptyMessage={t('smartRouteBuilder.waypointsPanelEmptyMessage')}
            className="h-full"
          />
        );

      case 'templates':
        return showTemplates ? (
          <RouteTemplatesPanel
            onTemplateLoad={handleLoadTemplate}
            className="h-full"
          />
        ) : null;

      case 'export':
        return showExport ? (
          <RouteExportPanel
            waypoints={waypoints} // This prop expects RouteWaypoint[]
            routeName={routeName}
            routeDescription={routeDescription}
            onNameChange={setRouteName}
            onDescriptionChange={setRouteDescription}
            onSave={handleSaveRoute}
            enableSharing={enableRouteSharing}
            className="h-full"
          />
        ) : null;

      default:
        return null;
    }
  };

  const renderContent = () => {
    if (layout === 'stacked') {
      return (
        <div className="flex flex-col h-full">
          <div className="flex-1">
            <EnhancedRouteMap
              waypoints={waypoints} // This prop expects RouteWaypoint[]
              maxWaypoints={maxWaypoints}
              onWaypointAdd={handlePlaceSelectFromSearch}
              onWaypointRemove={(placeId) => handleWaypointAction({ type: 'remove' }, { id: placeId } as VisitedPlace)}
              onWaypointReorder={(reordered) => handleWaypointAction({ type: 'reorder', payload: reordered }, {} as VisitedPlace)}
              onRouteOptimize={handleRouteOptimization}
              className="h-full"
            />
          </div>
          <div className="h-80 border-t border-gray-200">
            {renderPanelContent()}
          </div>
        </div>
      );
    }

    if (layout === 'tabs') {
      return (
        <div className="flex flex-col h-full">
          <div className="flex-1">
            <EnhancedRouteMap
              waypoints={waypoints} // This prop expects RouteWaypoint[]
              maxWaypoints={maxWaypoints}
              onWaypointAdd={handlePlaceSelectFromSearch}
              onWaypointRemove={(placeId) => handleWaypointAction({ type: 'remove' }, { id: placeId } as VisitedPlace)}
              onWaypointReorder={(reordered) => handleWaypointAction({ type: 'reorder', payload: reordered }, {} as VisitedPlace)}
              onRouteOptimize={handleRouteOptimization}
              className="h-full"
            />
          </div>
          <div className="h-64">
            {renderPanelContent()}
          </div>
        </div>
      );
    }

    return (
      <div className="flex h-full">
        <div className="flex-1">
          <EnhancedRouteMap
            waypoints={waypoints} // This prop expects RouteWaypoint[]
            maxWaypoints={maxWaypoints}
            onWaypointAdd={handlePlaceSelectFromSearch}
            onWaypointRemove={(placeId) => handleWaypointAction({ type: 'remove' }, { id: placeId } as VisitedPlace)}
            onWaypointReorder={(reordered) => handleWaypointAction({ type: 'reorder', payload: reordered }, {} as VisitedPlace)}
            onRouteOptimize={handleRouteOptimization}
            className="h-full"
          />
        </div>
        <div 
          className="bg-white border-l border-gray-200 flex flex-col"
          style={{ width: panelWidth }}
        >
          {renderPanelContent()}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("h-full bg-gray-50", className)}>
      <div className="bg-white border-b border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex space-x-1">
            {[
              { id: 'search', label: t('smartRouteBuilder.tab.search'), icon: '🔍' },
              { id: 'waypoints', label: t('smartRouteBuilder.tab.waypoints', { count: waypoints.length }), icon: '📍' },
              ...(showTemplates ? [{ id: 'templates', label: t('smartRouteBuilder.tab.templates'), icon: '📋' }] : []),
              ...(showExport ? [{ id: 'export', label: t('smartRouteBuilder.tab.export'), icon: '💾' }] : []),
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={cn(
                  "px-3 py-2 text-sm font-medium rounded-md transition-colors",
                  activeTab === tab.id
                    ? "bg-[#3B82F6] text-white"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                )}
              >
                <span className="mr-1">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>

          <div className="flex items-center space-x-2">
            {routeStats.isComplete && (
              <button
                onClick={handleCompleteRoute}
                disabled={isBuilding}
                className="bg-green-600 hover:bg-green-700 disabled:opacity-50 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                {isBuilding ? (
                  <>
                    <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {t('smartRouteBuilder.buildingRoute')}
                  </>
                ) : (
                  t('smartRouteBuilder.completeRoute')
                )}
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        {renderContent()}
      </div>

      <div className="bg-gray-100 border-t border-gray-200 px-4 py-2 text-sm text-gray-600">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span>📍 {waypoints.length}/{maxWaypoints} {t('smartRouteBuilder.waypointsLabel')}</span>
            {routeStats.estimatedDuration > 0 && (
              <span>⏱️ ~{Math.round(routeStats.estimatedDuration)} {t('smartRouteBuilder.minutesLabel')}</span>
            )}
            {routeStats.isComplete && (
              <span className="text-green-600 font-medium">✅ {t('smartRouteBuilder.routeReady')}</span>
            )}
          </div>
          <div className="flex items-center space-x-2 text-xs">
            {enableOfflineMode && <span>📱 {t('smartRouteBuilder.offlineReady')}</span>}
            {enableVoiceSearch && <span>🎤 {t('smartRouteBuilder.voiceSearch')}</span>}
          </div>
        </div>
      </div>
    </div>
  );
}
