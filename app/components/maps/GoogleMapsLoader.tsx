import React, { useEffect, useState } from 'react';
import type { Libraries } from '@react-google-maps/api';

// Define the libraries we want to load (includes geometry for Wanderlust Explorer and routes for multi-point planning)
const libraries: Libraries = ['places', 'geometry', 'routes'] as Libraries;

// Centralized error monitoring for Google Maps API
const logGoogleMapsError = (error: Error | string, context: string) => {
  const errorMessage = typeof error === 'string' ? error : error.message;
  console.error(`🗺️ Google Maps Error [${context}]:`, errorMessage);

  // In development, provide more detailed logging
  if (process.env.NODE_ENV === 'development') {
    console.group('🔍 Google Maps Diagnostic Info');
    console.log('API Key Present:', !!import.meta.env.VITE_GOOGLE_MAPS_API_KEY);
    console.log('Map ID Present:', !!import.meta.env.VITE_GOOGLE_MAPS_MAP_ID);
    console.log('Libraries:', libraries);
    console.log('Context:', context);
    if (typeof error === 'object') {
      console.log('Error Details:', error);
    }
    console.groupEnd();
  }
};

// Create a context to provide Google Maps loading state
export const GoogleMapsContext = React.createContext<{
  isLoaded: boolean;
  loadError: Error | undefined;
}>({
  isLoaded: false,
  loadError: undefined,
});

interface GoogleMapsLoaderProps {
  children: React.ReactNode;
}

export function GoogleMapsLoader({ children }: GoogleMapsLoaderProps) {
  const [loadingState, setLoadingState] = useState({
    isLoaded: false,
    loadError: undefined as Error | undefined,
  });

  // Get the Google Maps API key from environment variables
  const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY as string;

  // Enhanced Google Maps loader implementation with debugging
  useEffect(() => {
    const loadGoogleMaps = async () => {
      try {
        // Enhanced logging for debugging
        if (process.env.NODE_ENV === 'development') {
          console.group('🔍 Google Maps Loader Debug');
          console.log('API Key Present:', !!apiKey);
          console.log('API Key Length:', apiKey?.length || 0);
          console.log('API Key Preview:', apiKey ? `${apiKey.substring(0, 8)}...` : 'Not found');
          console.log('Libraries to load:', libraries);
          console.log('Window Google exists:', !!window.google);
          console.log('Window Google Maps exists:', !!window.google?.maps);
          console.groupEnd();
        }

        // Check if already loaded to prevent duplicate calls
        if (window.google?.maps) {
          console.log('✅ Google Maps already loaded, skipping');
          setLoadingState({
            isLoaded: true,
            loadError: undefined,
          });
          return;
        }

        console.log('🔄 Starting Google Maps dynamic import...');

        // Load Google Maps with dynamic import for CommonJS module
        const module = await import('@googlemaps/js-api-loader');
        console.log('✅ Dynamic import successful:', !!module.Loader);

        if (!module.Loader) {
          throw new Error('Loader not found in @googlemaps/js-api-loader module');
        }

        const { Loader } = module;
        const loader = new Loader({
          apiKey,
          version: 'weekly',
          libraries,
          language: 'en',
        });

        console.log('🔄 Loading Google Maps libraries...');
        await loader.importLibrary('maps');

        // Verify that Google Maps is actually loaded
        if (!window.google?.maps) {
          throw new Error('Google Maps failed to load properly - window.google.maps not available');
        }

        console.log('✅ Google Maps libraries loaded successfully');

        setLoadingState({
          isLoaded: true,
          loadError: undefined,
        });

        // Log successful load in development
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ Google Maps API loaded successfully');
          console.log('Available APIs:', {
            maps: !!window.google.maps,
            places: !!window.google.maps.places,
            geometry: !!window.google.maps.geometry,
            DirectionsService: !!window.google.maps.DirectionsService,
          });
        }
      } catch (error) {
        const loadError = error instanceof Error ? error : new Error('Failed to load Google Maps');
        console.error('❌ Google Maps loading failed:', loadError);

        setLoadingState({
          isLoaded: false,
          loadError,
        });

        // Log error with context
        logGoogleMapsError(loadError, 'Enhanced Loader');
      }
    };

    // Validate API key before attempting to load
    if (!apiKey) {
      const error = new Error('Google Maps API key is missing. Please set VITE_GOOGLE_MAPS_API_KEY environment variable.');
      setLoadingState({
        isLoaded: false,
        loadError: error,
      });
      logGoogleMapsError(error, 'Configuration');
      return;
    }

    loadGoogleMaps();
  }, [apiKey]);

  // Provide the loading state to children
  return (
    <GoogleMapsContext.Provider value={loadingState}>
      {children}
    </GoogleMapsContext.Provider>
  );
}

// Custom hook to use the Google Maps context
export function useGoogleMaps() {
  const context = React.useContext(GoogleMapsContext);
  if (context === undefined) {
    throw new Error('useGoogleMaps must be used within a GoogleMapsLoader');
  }
  return context;
}
