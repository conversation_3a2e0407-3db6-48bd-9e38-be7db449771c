/**
 * Lazy-Loaded Venue Discovery Components
 * 
 * Implements React.lazy() code splitting for venue discovery components
 * to optimize bundle size and improve performance for venue exploration features.
 * 
 * Features:
 * - Progressive loading with venue-specific skeletons
 * - Error boundaries with venue-specific fallbacks
 * - Performance monitoring for venue components
 * - Smart preloading based on venue discovery patterns
 */

import React, { Suspense, lazy, useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Skeleton } from '~/components/ui/skeleton';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader } from '~/components/ui/card';
import { Loader2, AlertTriangle, RefreshCw, MapPin, Search, Star } from 'lucide-react';

// Lazy-loaded venue discovery components
const VenueExplorerMap = lazy(() => 
  import('./VenueExplorerMap').then(module => ({ default: module.VenueExplorerMap }))
);

// Venue-specific skeleton components
const VenueMapSkeleton = () => (
  <div className="relative w-full h-full bg-gray-100 rounded-lg overflow-hidden">
    {/* Map skeleton */}
    <Skeleton className="w-full h-full" />
    
    {/* Venue discovery overlay */}
    <div className="absolute inset-0 flex items-center justify-center bg-black/5">
      <div className="bg-white rounded-lg p-6 shadow-lg flex flex-col items-center space-y-3 max-w-sm">
        <Search className="h-8 w-8 text-purple-600 animate-pulse" />
        <span className="text-sm font-medium text-gray-700">Loading venue explorer...</span>
        <div className="flex space-x-2">
          <Skeleton className="w-16 h-6 rounded-full" />
          <Skeleton className="w-20 h-6 rounded-full" />
          <Skeleton className="w-18 h-6 rounded-full" />
        </div>
      </div>
    </div>
    
    {/* Search controls skeleton */}
    <div className="absolute top-4 left-4 right-4">
      <Card>
        <CardContent className="p-4">
          <div className="flex space-x-2">
            <Skeleton className="flex-1 h-10 rounded-lg" />
            <Skeleton className="w-10 h-10 rounded-lg" />
          </div>
        </CardContent>
      </Card>
    </div>
    
    {/* Venue filters skeleton */}
    <div className="absolute top-20 left-4 right-4">
      <div className="flex space-x-2 overflow-x-auto pb-2">
        {['All', 'Food', 'Hotels', 'Attractions', 'Shopping'].map((category) => (
          <Skeleton key={category} className="w-16 h-8 rounded-full flex-shrink-0" />
        ))}
      </div>
    </div>
    
    {/* Venue results skeleton */}
    <div className="absolute bottom-4 left-4 right-4 max-h-48 overflow-hidden">
      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <Skeleton className="w-24 h-5" />
            <Skeleton className="w-16 h-4" />
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center space-x-3 p-2 border rounded-lg">
              <Skeleton className="w-12 h-12 rounded" />
              <div className="flex-1 space-y-1">
                <Skeleton className="w-full h-4" />
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star key={star} className="h-3 w-3 text-gray-300" />
                    ))}
                  </div>
                  <Skeleton className="w-12 h-3" />
                </div>
                <Skeleton className="w-2/3 h-3" />
              </div>
              <Skeleton className="w-8 h-8 rounded" />
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  </div>
);

const VenueCardSkeleton = () => (
  <Card className="w-full">
    <CardHeader className="pb-3">
      <div className="flex items-start justify-between">
        <div className="flex-1 space-y-2">
          <Skeleton className="w-3/4 h-6" />
          <div className="flex items-center space-x-2">
            <div className="flex space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <Star key={star} className="h-4 w-4 text-gray-300" />
              ))}
            </div>
            <Skeleton className="w-16 h-4" />
          </div>
        </div>
        <Skeleton className="w-16 h-12 rounded" />
      </div>
    </CardHeader>
    <CardContent className="space-y-4">
      {/* Venue details skeleton */}
      <div className="space-y-2">
        <div className="flex items-center space-x-2">
          <MapPin className="h-4 w-4 text-gray-400" />
          <Skeleton className="flex-1 h-4" />
        </div>
        <Skeleton className="w-full h-4" />
        <Skeleton className="w-2/3 h-4" />
      </div>
      
      {/* Venue features skeleton */}
      <div className="flex flex-wrap gap-2">
        {[1, 2, 3, 4].map((i) => (
          <Skeleton key={i} className="w-16 h-6 rounded-full" />
        ))}
      </div>
      
      {/* Action buttons skeleton */}
      <div className="flex space-x-2 pt-2">
        <Skeleton className="flex-1 h-8" />
        <Skeleton className="w-20 h-8" />
        <Skeleton className="w-16 h-8" />
      </div>
    </CardContent>
  </Card>
);

const VenueListSkeleton = () => (
  <div className="space-y-3">
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center space-x-2">
        <Search className="h-5 w-5 text-gray-400" />
        <Skeleton className="w-32 h-5" />
      </div>
      <Skeleton className="w-20 h-4" />
    </div>
    
    {[1, 2, 3, 4, 5].map((i) => (
      <Card key={i}>
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <Skeleton className="w-16 h-16 rounded-lg flex-shrink-0" />
            <div className="flex-1 space-y-2">
              <Skeleton className="w-full h-5" />
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="h-3 w-3 text-gray-300" />
                  ))}
                </div>
                <Skeleton className="w-12 h-3" />
                <Skeleton className="w-16 h-3" />
              </div>
              <Skeleton className="w-3/4 h-4" />
              <div className="flex space-x-2">
                <Skeleton className="w-12 h-5 rounded-full" />
                <Skeleton className="w-16 h-5 rounded-full" />
              </div>
            </div>
            <div className="flex flex-col space-y-2">
              <Skeleton className="w-8 h-8 rounded" />
              <Skeleton className="w-12 h-4" />
            </div>
          </div>
        </CardContent>
      </Card>
    ))}
  </div>
);

// Error fallback components
const VenueErrorFallback = ({ error, resetErrorBoundary }: { 
  error: Error; 
  resetErrorBoundary: () => void; 
}) => (
  <Card className="w-full">
    <CardContent className="flex flex-col items-center justify-center p-8 text-center">
      <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Venue explorer unavailable
      </h3>
      <p className="text-sm text-gray-600 mb-4 max-w-md">
        {error.message || 'Failed to load the venue discovery component. Please try again.'}
      </p>
      <Button 
        onClick={resetErrorBoundary}
        variant="outline"
        size="sm"
        className="inline-flex items-center"
      >
        <RefreshCw className="h-4 w-4 mr-2" />
        Reload venue explorer
      </Button>
    </CardContent>
  </Card>
);

// Performance monitoring for venue components
const useVenueComponentLoadTime = (componentName: string) => {
  const [loadTime, setLoadTime] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const startTime = performance.now();
    setIsLoading(true);
    
    const timer = setTimeout(() => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      setLoadTime(duration);
      setIsLoading(false);
      
      // Log venue component performance
      if (process.env.NODE_ENV === 'development') {
        console.log(`Venue ${componentName} load time: ${duration.toFixed(2)}ms`);
      }
    }, 100);

    return () => {
      clearTimeout(timer);
      setIsLoading(false);
    };
  }, [componentName]);

  return { loadTime, isLoading };
};

// Preloading strategies for venue components
export const preloadVenueEssentials = () => {
  // Preload core venue discovery components
  import('./VenueExplorerMap');
};

export const preloadVenueAdvanced = () => {
  // Preload advanced venue features (when they exist)
  // import('./VenueDetails');
  // import('./VenueReviews');
  // import('./VenueComparison');
};

// Lazy wrapper components with venue-specific optimizations
export const LazyVenueExplorerMap = (props: React.ComponentPropsWithoutRef<typeof VenueExplorerMap>) => {
  const { loadTime, isLoading } = useVenueComponentLoadTime('VenueExplorerMap');

  return (
    <ErrorBoundary
      FallbackComponent={VenueErrorFallback}
      onError={(error) => {
        console.error('VenueExplorerMap failed to load:', error);
      }}
    >
      <Suspense fallback={<VenueMapSkeleton />}>
        <VenueExplorerMap {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

LazyVenueExplorerMap.displayName = 'LazyVenueExplorerMap';

// Utility components for venue discovery
export const VenueLoadingIndicator = ({ message = 'Loading venues...' }: { message?: string }) => (
  <div className="flex items-center justify-center p-4">
    <div className="flex items-center space-x-3">
      <Loader2 className="h-5 w-5 animate-spin text-purple-600" />
      <span className="text-sm font-medium text-gray-700">{message}</span>
    </div>
  </div>
);

export const VenueErrorAlert = ({ 
  error, 
  onRetry 
}: { 
  error: string; 
  onRetry?: () => void; 
}) => (
  <Alert className="border-red-200 bg-red-50">
    <AlertTriangle className="h-4 w-4 text-red-600" />
    <AlertDescription className="flex items-center justify-between text-red-800">
      <span>{error}</span>
      {onRetry && (
        <Button 
          onClick={onRetry}
          variant="ghost"
          size="sm"
          className="text-red-600 hover:text-red-700"
        >
          <RefreshCw className="h-3 w-3 mr-1" />
          Retry
        </Button>
      )}
    </AlertDescription>
  </Alert>
);

// Bundle analysis for venue components
export const getVenueBundleInfo = () => {
  if (process.env.NODE_ENV === 'development') {
    return {
      lazyComponents: [
        'VenueExplorerMap',
      ],
      preloadStrategies: [
        'preloadVenueEssentials',
        'preloadVenueAdvanced',
      ],
      skeletonComponents: [
        'VenueMapSkeleton',
        'VenueCardSkeleton', 
        'VenueListSkeleton',
      ],
    };
  }
  return null;
};
