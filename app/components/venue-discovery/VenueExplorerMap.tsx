// app/components/venue-discovery/VenueExplorerMap.tsx
import React from 'react';
import { BaseMapComponent, type BaseMapComponentProps, type MarkerContext } from '~/components/maps/BaseMapComponent'; // Import BaseMapComponent, its props type, and MarkerContext
import type { VisitedPlace } from '~/types/wanderlust'; // Assuming VisitedPlace type is here
import { useWanderlustStore } from '~/stores/wanderlust'; // Import the store

// Custom marker renderer for venues
const venueMarkerRenderer = (place: VisitedPlace, context: MarkerContext) => {
  // TODO: Implement more sophisticated venue specific marker rendering logic (e.g., different icons based on category)
  console.log('Rendering venue marker for:', place.name, 'Context:', context);
  return {
    icon: place.icon || 'http://maps.google.com/mapfiles/ms/icons/red-dot.png', // Use place.icon if available, otherwise a default Google Maps icon
    title: place.name,
    // You might add other properties here like 'label' or 'infoWindowContent'
  };
};

// Placeholder components for layers and overlays
const VenueClusterLayer = () => {
  // TODO: Implement venue clustering layer - this might consume the places data directly or via context
  return null;
};

const VenueDetailsOverlay = () => {
  // TODO: Implement venue details overlay - this might consume selected place data
  return null;
};

// Placeholder handler for venue selection
const handleVenueSelection = (place: VisitedPlace) => {
  // TODO: Implement logic for handling venue selection on the map (e.g., update store, show details panel)
  console.log('Venue selected on map:', place.name);
};

export function VenueExplorerMap(props: BaseMapComponentProps) {
  // Access filteredPlaces from the store
  const { filteredPlaces } = useWanderlustStore();

  // Log filteredPlaces to check its content
  console.log('VenueExplorerMap - filteredPlaces:', filteredPlaces);

  return (
    <BaseMapComponent
      features={['markers', 'search']} // Features for venue discovery (removed 'clustering')
      customMarkerRenderer={venueMarkerRenderer}
      onPlaceSelect={handleVenueSelection}
      places={filteredPlaces} // Pass the filtered places data to BaseMapComponent
      {...props} // Spread the received props
    >
      {/* Layers and Overlays specific to Venue Discovery */}
      {/* These layers might need access to filteredPlaces as well */}
      {/* VenueClusterLayer is a placeholder and might interfere if 'clustering' feature is enabled */}
      {/* <VenueClusterLayer /> */}
      <VenueDetailsOverlay />
    </BaseMapComponent>
  );
}
