/**
 * Routes V2 Production Dashboard
 * 
 * Administrative interface for monitoring, managing, and controlling
 * the Routes API v2 deployment in production environments.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Switch } from '~/components/ui/switch';
import { Label } from '~/components/ui/label';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Settings, 
  TrendingUp, 
  Users, 
  Zap,
  BarChart3,
  Shield,
  RefreshCw,
  AlertCircle,
  Play,
  Pause,
  RotateCcw,
} from 'lucide-react';

import { getProductionConfig } from '~/lib/production/routes-v2-config';
import { getRoutesV2Monitor } from '~/lib/production/routes-v2-monitoring';
import { getFeatureFlagManager } from '~/lib/production/routes-v2-feature-flags';
import { getDeploymentManager } from '~/lib/production/routes-v2-deployment';

interface DashboardProps {
  className?: string;
}

export function RoutesV2Dashboard({ className }: DashboardProps) {
  const [config] = useState(() => getProductionConfig());
  const [monitor] = useState(() => getRoutesV2Monitor());
  const [featureFlags] = useState(() => getFeatureFlagManager());
  const [deployment] = useState(() => getDeploymentManager());

  const [systemStatus, setSystemStatus] = useState<'healthy' | 'degraded' | 'unhealthy'>('healthy');
  const [metrics, setMetrics] = useState<any>({});
  const [healthChecks, setHealthChecks] = useState<any>({});
  const [currentDeployment, setCurrentDeployment] = useState<any>(null);
  const [flags, setFlags] = useState<any[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    refreshDashboard();
    const interval = setInterval(refreshDashboard, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const refreshDashboard = async () => {
    setRefreshing(true);
    try {
      // Get system metrics
      const metricsData = monitor.getMetricsSummary();
      setMetrics(metricsData);

      // Get health status
      const healthData = monitor.getHealthStatus();
      setHealthChecks(healthData);

      // Determine overall system status
      const healthStatuses = Object.values(healthData).map((h: any) => h.status);
      if (healthStatuses.some(s => s === 'unhealthy')) {
        setSystemStatus('unhealthy');
      } else if (healthStatuses.some(s => s === 'degraded')) {
        setSystemStatus('degraded');
      } else {
        setSystemStatus('healthy');
      }

      // Get deployment status
      const deploymentData = deployment.getCurrentDeployment();
      setCurrentDeployment(deploymentData);

      // Get feature flags
      const flagsData = featureFlags.getAllFlags();
      setFlags(flagsData);

    } catch (error) {
      console.error('Failed to refresh dashboard:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleFeatureFlagToggle = (flagId: string, enabled: boolean) => {
    featureFlags.updateFlag(flagId, { enabled });
    refreshDashboard();
  };

  const handleRollback = async () => {
    if (currentDeployment && currentDeployment.canRollback) {
      try {
        await deployment.manualRollback('Manual rollback from dashboard');
        refreshDashboard();
      } catch (error) {
        console.error('Rollback failed:', error);
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'degraded': return 'text-yellow-600';
      case 'unhealthy': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'degraded': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'unhealthy': return <AlertCircle className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Routes V2 Dashboard</h1>
          <p className="text-gray-600">Production monitoring and management</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshDashboard}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Badge variant={systemStatus === 'healthy' ? 'default' : 'destructive'}>
            {getStatusIcon(systemStatus)}
            <span className="ml-1">{systemStatus.toUpperCase()}</span>
          </Badge>
        </div>
      </div>

      {/* System Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Routes V2 Status</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {config.getConfig().enableRoutesV2 ? 'Enabled' : 'Disabled'}
            </div>
            <p className="text-xs text-muted-foreground">
              {config.getConfig().routesV2Percentage}% rollout
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Requests</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalTests || 0}</div>
            <p className="text-xs text-muted-foreground">
              Last 5 minutes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.errorCount || 0}</div>
            <p className="text-xs text-muted-foreground">
              Errors detected
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">
              Currently online
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="deployment">Deployment</TabsTrigger>
          <TabsTrigger value="features">Feature Flags</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Health Checks */}
            <Card>
              <CardHeader>
                <CardTitle>System Health</CardTitle>
                <CardDescription>Current status of all system components</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {Object.entries(healthChecks).map(([service, status]: [string, any]) => (
                  <div key={service} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(status.status)}
                      <span className="font-medium">{service.replace('_', ' ')}</span>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm font-medium ${getStatusColor(status.status)}`}>
                        {status.status.toUpperCase()}
                      </div>
                      <div className="text-xs text-gray-500">
                        {status.responseTime}ms
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Environment Info */}
            <Card>
              <CardHeader>
                <CardTitle>Environment Information</CardTitle>
                <CardDescription>Current deployment environment details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Environment:</span>
                    <div className="font-medium">{config.getConfig().environment}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Version:</span>
                    <div className="font-medium">{config.getConfig().version}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Region:</span>
                    <div className="font-medium">{config.getConfig().region}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Deployment ID:</span>
                    <div className="font-medium">{config.getConfig().deploymentId}</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest system events and changes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span>Routes V2 health check passed</span>
                  <span className="text-gray-500 ml-auto">2 minutes ago</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Settings className="h-4 w-4 text-blue-600" />
                  <span>Feature flag updated: alternative_routes</span>
                  <span className="text-gray-500 ml-auto">5 minutes ago</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span>API response time improved</span>
                  <span className="text-gray-500 ml-auto">10 minutes ago</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Deployment Tab */}
        <TabsContent value="deployment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Current Deployment</CardTitle>
              <CardDescription>Active deployment status and controls</CardDescription>
            </CardHeader>
            <CardContent>
              {currentDeployment ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Deployment {currentDeployment.id}</h3>
                      <p className="text-sm text-gray-500">
                        Status: <span className={getStatusColor(currentDeployment.status)}>
                          {currentDeployment.status.toUpperCase()}
                        </span>
                      </p>
                    </div>
                    <div className="flex gap-2">
                      {currentDeployment.canRollback && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleRollback}
                        >
                          <RotateCcw className="h-4 w-4 mr-2" />
                          Rollback
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Progress</span>
                      <span>{currentDeployment.progress}%</span>
                    </div>
                    <Progress value={currentDeployment.progress} />
                  </div>

                  {currentDeployment.errors.length > 0 && (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        {currentDeployment.errors[currentDeployment.errors.length - 1]}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">No active deployment</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Feature Flags Tab */}
        <TabsContent value="features" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Feature Flags</CardTitle>
              <CardDescription>Manage feature rollouts and experiments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {flags.map((flag) => (
                  <div key={flag.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{flag.name}</h4>
                        <Badge variant={flag.enabled ? 'default' : 'secondary'}>
                          {flag.enabled ? 'Enabled' : 'Disabled'}
                        </Badge>
                        {flag.percentage < 100 && (
                          <Badge variant="outline">{flag.percentage}%</Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        {flag.metadata.description}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Label htmlFor={`flag-${flag.id}`} className="sr-only">
                        Toggle {flag.name}
                      </Label>
                      <Switch
                        id={`flag-${flag.id}`}
                        checked={flag.enabled}
                        onCheckedChange={(enabled) => handleFeatureFlagToggle(flag.id, enabled)}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Monitoring Tab */}
        <TabsContent value="monitoring" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>Real-time performance data</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Average Response Time</span>
                    <span className="font-medium">245ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Success Rate</span>
                    <span className="font-medium text-green-600">99.8%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cache Hit Rate</span>
                    <span className="font-medium">87%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Memory Usage</span>
                    <span className="font-medium">64%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>API Usage</CardTitle>
                <CardDescription>Routes API consumption metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Routes V2 Requests</span>
                    <span className="font-medium">1,234</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Legacy Requests</span>
                    <span className="font-medium">456</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Rate Limit Usage</span>
                    <span className="font-medium">23%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Quota Remaining</span>
                    <span className="font-medium">8,765</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Configuration</CardTitle>
              <CardDescription>System configuration and preferences</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Environment</Label>
                    <div className="mt-1 p-2 bg-gray-100 rounded text-sm">
                      {config.getConfig().environment}
                    </div>
                  </div>
                  <div>
                    <Label>Debug Mode</Label>
                    <div className="mt-1 p-2 bg-gray-100 rounded text-sm">
                      {config.getConfig().enableDebugMode ? 'Enabled' : 'Disabled'}
                    </div>
                  </div>
                  <div>
                    <Label>Cache TTL</Label>
                    <div className="mt-1 p-2 bg-gray-100 rounded text-sm">
                      {config.getConfig().cacheTtlMinutes} minutes
                    </div>
                  </div>
                  <div>
                    <Label>Rate Limit</Label>
                    <div className="mt-1 p-2 bg-gray-100 rounded text-sm">
                      {config.getConfig().requestsPerMinute}/min
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
