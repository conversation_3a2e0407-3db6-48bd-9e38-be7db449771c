/**
 * Routes V2 Test Component
 * 
 * Test component for validating Google Routes API v2 implementation
 * and comparing it with the legacy Directions API.
 */

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { 
  calculateRoute, 
  calculateEnhancedRoute, 
  getRouteAlternatives,
  clearRouteCache,
  getCacheStats 
} from '~/lib/route-planning-utils';
import { showSuccess, showError, showInfo } from '~/components/wanderlust/NotificationSystem';
import type { RouteWaypoint, TravelRoute } from '~/types/wanderlust';
import type { EnhancedRouteOptions } from '~/types/routes-v2';

// Test waypoints (Nashville area)
const testWaypoints: RouteWaypoint[] = [
  {
    location: { lat: 36.1627, lng: -86.7816 }, // Nashville Downtown
    stopover: false,
    placeId: 'downtown-nashville',
  },
  {
    location: { lat: 36.1263, lng: -86.6782 }, // Nashville Airport
    stopover: true,
    placeId: 'nashville-airport',
  },
  {
    location: { lat: 36.1447, lng: -86.8027 }, // Vanderbilt University
    stopover: false,
    placeId: 'vanderbilt-university',
  },
];

interface TestResult {
  apiVersion: 'v1' | 'v2';
  duration: number;
  route: TravelRoute | null;
  error: string | null;
  cacheHit: boolean;
}

export function RoutesV2Test() {
  const [isTestingBasic, setIsTestingBasic] = useState(false);
  const [isTestingEnhanced, setIsTestingEnhanced] = useState(false);
  const [isTestingAlternatives, setIsTestingAlternatives] = useState(false);
  const [basicResult, setBasicResult] = useState<TestResult | null>(null);
  const [enhancedResult, setEnhancedResult] = useState<any>(null);
  const [alternativesResult, setAlternativesResult] = useState<TravelRoute[]>([]);
  const [cacheStats, setCacheStats] = useState<any>(null);

  // Test basic route calculation
  const testBasicRoute = async () => {
    setIsTestingBasic(true);
    setBasicResult(null);
    
    try {
      showInfo('Testing basic route calculation...');
      const startTime = performance.now();
      
      const route = await calculateRoute(testWaypoints, 'DRIVING', {
        optimizeWaypointOrder: true,
        avoidTolls: false,
        avoidHighways: false,
      });
      
      const duration = performance.now() - startTime;
      
      setBasicResult({
        apiVersion: 'v1', // Will be determined by feature flags
        duration,
        route,
        error: null,
        cacheHit: false,
      });
      
      showSuccess(`Basic route calculated in ${duration.toFixed(2)}ms`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setBasicResult({
        apiVersion: 'v1',
        duration: 0,
        route: null,
        error: errorMessage,
        cacheHit: false,
      });
      
      showError('Basic route calculation failed', errorMessage);
    } finally {
      setIsTestingBasic(false);
    }
  };

  // Test enhanced route calculation with Routes V2 features
  const testEnhancedRoute = async () => {
    setIsTestingEnhanced(true);
    setEnhancedResult(null);
    
    try {
      showInfo('Testing enhanced route calculation...');
      const startTime = performance.now();
      
      const options: Partial<EnhancedRouteOptions> = {
        routingPreference: 'TRAFFIC_AWARE',
        includeAlternativeRoutes: true,
        maxAlternativeRoutes: 2,
        polylineQuality: 'HIGH_QUALITY',
        computeTollInfo: true,
        optimizeWaypointOrder: true,
      };
      
      const result = await calculateEnhancedRoute(testWaypoints, 'DRIVING', options);
      
      setEnhancedResult({
        ...result,
        totalDuration: performance.now() - startTime,
      });
      
      showSuccess(
        `Enhanced route calculated in ${result.metadata.calculationTime.toFixed(2)}ms`,
        `API: ${result.metadata.apiVersion.toUpperCase()}, Alternatives: ${result.alternativeRoutes.length}`
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setEnhancedResult({
        error: errorMessage,
        totalDuration: 0,
      });
      
      showError('Enhanced route calculation failed', errorMessage);
    } finally {
      setIsTestingEnhanced(false);
    }
  };

  // Test route alternatives
  const testRouteAlternatives = async () => {
    setIsTestingAlternatives(true);
    setAlternativesResult([]);
    
    try {
      showInfo('Testing route alternatives...');
      
      const alternatives = await getRouteAlternatives(testWaypoints, 'DRIVING', 3);
      
      setAlternativesResult(alternatives);
      
      showSuccess(
        `Found ${alternatives.length} route alternatives`,
        alternatives.map(r => `${r.estimatedDuration} (${r.estimatedDistance})`).join(', ')
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      showError('Route alternatives test failed', errorMessage);
    } finally {
      setIsTestingAlternatives(false);
    }
  };

  // Clear cache and update stats
  const handleClearCache = () => {
    clearRouteCache();
    updateCacheStats();
    showSuccess('Route cache cleared');
  };

  // Update cache statistics
  const updateCacheStats = () => {
    const stats = getCacheStats();
    setCacheStats(stats);
  };

  // Format route info for display
  const formatRouteInfo = (route: TravelRoute) => ({
    id: route.id,
    duration: route.estimatedDuration,
    distance: route.estimatedDistance,
    waypoints: route.waypoints.length,
    optimized: route.optimized,
    legs: route.legs?.length || 0,
  });

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🧪 Routes API v2 Testing Suite
            <Badge variant="outline">Development</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Basic Route Test */}
            <div className="space-y-2">
              <Button 
                onClick={testBasicRoute} 
                disabled={isTestingBasic}
                className="w-full"
              >
                {isTestingBasic ? 'Testing...' : 'Test Basic Route'}
              </Button>
              
              {basicResult && (
                <div className="text-sm space-y-1">
                  {basicResult.error ? (
                    <div className="text-red-600">❌ {basicResult.error}</div>
                  ) : basicResult.route ? (
                    <div className="text-green-600">
                      ✅ {basicResult.duration.toFixed(2)}ms
                      <br />
                      📍 {basicResult.route.waypoints.length} waypoints
                      <br />
                      ⏱️ {basicResult.route.estimatedDuration}
                      <br />
                      📏 {basicResult.route.estimatedDistance}
                    </div>
                  ) : null}
                </div>
              )}
            </div>

            {/* Enhanced Route Test */}
            <div className="space-y-2">
              <Button 
                onClick={testEnhancedRoute} 
                disabled={isTestingEnhanced}
                className="w-full"
                variant="outline"
              >
                {isTestingEnhanced ? 'Testing...' : 'Test Enhanced Route'}
              </Button>
              
              {enhancedResult && (
                <div className="text-sm space-y-1">
                  {enhancedResult.error ? (
                    <div className="text-red-600">❌ {enhancedResult.error}</div>
                  ) : enhancedResult.primaryRoute ? (
                    <div className="text-blue-600">
                      ✅ {enhancedResult.metadata.calculationTime.toFixed(2)}ms
                      <br />
                      🔧 API: {enhancedResult.metadata.apiVersion.toUpperCase()}
                      <br />
                      🛣️ {enhancedResult.alternativeRoutes.length} alternatives
                      <br />
                      💾 Cache: {enhancedResult.metadata.cacheHit ? 'HIT' : 'MISS'}
                    </div>
                  ) : null}
                </div>
              )}
            </div>

            {/* Route Alternatives Test */}
            <div className="space-y-2">
              <Button 
                onClick={testRouteAlternatives} 
                disabled={isTestingAlternatives}
                className="w-full"
                variant="secondary"
              >
                {isTestingAlternatives ? 'Testing...' : 'Test Alternatives'}
              </Button>
              
              {alternativesResult.length > 0 && (
                <div className="text-sm space-y-1">
                  <div className="text-purple-600">
                    ✅ {alternativesResult.length} routes found
                  </div>
                  {alternativesResult.slice(0, 2).map((route, index) => (
                    <div key={route.id} className="text-xs text-gray-600">
                      Route {index + 1}: {route.estimatedDuration} • {route.estimatedDistance}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Cache Management */}
          <div className="border-t pt-4">
            <div className="flex items-center justify-between">
              <div className="space-x-2">
                <Button onClick={updateCacheStats} variant="outline" size="sm">
                  Update Cache Stats
                </Button>
                <Button onClick={handleClearCache} variant="outline" size="sm">
                  Clear Cache
                </Button>
              </div>
              
              {cacheStats && (
                <div className="text-sm text-gray-600">
                  Cache: {cacheStats.size} entries
                </div>
              )}
            </div>
          </div>

          {/* Environment Info */}
          <div className="border-t pt-4 text-xs text-gray-500">
            <div className="grid grid-cols-2 gap-2">
              <div>Routes V2: {import.meta.env.VITE_USE_ROUTES_V2 || 'false'}</div>
              <div>Percentage: {import.meta.env.VITE_ROUTES_V2_PERCENTAGE || '0'}%</div>
              <div>Fallback: {import.meta.env.VITE_ENABLE_ROUTES_FALLBACK || 'true'}</div>
              <div>Debug: {import.meta.env.VITE_ROUTES_DEBUG || 'false'}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Results */}
      {(basicResult?.route || enhancedResult?.primaryRoute) && (
        <Card>
          <CardHeader>
            <CardTitle>📊 Detailed Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {basicResult?.route && (
                <div>
                  <h4 className="font-medium mb-2">Basic Route</h4>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                    {JSON.stringify(formatRouteInfo(basicResult.route), null, 2)}
                  </pre>
                </div>
              )}
              
              {enhancedResult?.primaryRoute && (
                <div>
                  <h4 className="font-medium mb-2">Enhanced Route</h4>
                  <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                    {JSON.stringify({
                      primary: formatRouteInfo(enhancedResult.primaryRoute),
                      alternatives: enhancedResult.alternativeRoutes.length,
                      metadata: enhancedResult.metadata,
                    }, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
