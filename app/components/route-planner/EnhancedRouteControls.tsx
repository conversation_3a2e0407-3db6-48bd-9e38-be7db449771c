/**
 * Enhanced Route Controls Component
 *
 * Provides advanced route calculation controls with Routes API v2 features
 * including traffic-aware routing, alternative routes, and routing preferences.
 */

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { But<PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Separator } from '~/components/ui/separator';
import { Switch } from '~/components/ui/switch';
import { Label } from '~/components/ui/label';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip';
import { useRouteCalculation } from '~/hooks/useRouteCalculation';
import {
  Car,
  Footprints,
  Bike,
  Bus,
  Route,
  Zap,
  Clock,
  MapPin,
  Settings,
  Info,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface EnhancedRouteControlsProps {
  className?: string;
  showAdvancedOptions?: boolean;
}

export function EnhancedRouteControls({
  className = '',
  showAdvancedOptions = true
}: EnhancedRouteControlsProps) {
  const [showAlternatives, setShowAlternatives] = useState(false);
  const [enableTrafficOptimization, setEnableTrafficOptimization] = useState(true);

  const {
    // State
    isCalculating,
    isCalculatingAlternatives,
    travelMode,
    routingPreference,
    alternativeRoutes,
    selectedAlternativeIndex,
    routeMetadata,
    isCalculationReady,

    // Options
    travelModes,
    routingPreferences,

    // Actions
    setTravelMode,
    setRoutingPreference,
    handleCalculateRoute,
    handleCalculateEnhancedRoute,
    handleCalculateAlternatives,
    handleSelectAlternative,
    handleClearRoute,
  } = useRouteCalculation();

  // Get routing preference color
  const getRoutingPreferenceColor = (preference: string) => {
    switch (preference) {
      case 'TRAFFIC_UNAWARE': return 'text-gray-600';
      case 'TRAFFIC_AWARE': return 'text-blue-600';
      case 'TRAFFIC_AWARE_OPTIMAL': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  // Handle enhanced route calculation
  const handleEnhancedCalculation = () => {
    if (enableTrafficOptimization) {
      handleCalculateEnhancedRoute();
    } else {
      handleCalculateRoute();
    }
  };

  return (
    <TooltipProvider>
      <Card className={`w-full ${className}`}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Route className="h-5 w-5" />
            Route Controls
            {routeMetadata && (
              <Badge variant="outline" className="ml-auto">
                {routeMetadata.apiVersion.toUpperCase()}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Travel Mode Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Travel Mode</Label>
            <div className="grid grid-cols-2 gap-2">
              {travelModes.map(({ mode, icon, label }) => {
                const IconComponent = icon === 'Car' ? Car :
                                   icon === 'Footprints' ? Footprints :
                                   icon === 'Bike' ? Bike :
                                   icon === 'Bus' ? Bus : Car;
                return (
                  <Button
                    key={mode}
                    variant={travelMode === mode ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setTravelMode(mode)}
                    className="flex items-center gap-2 justify-start"
                  >
                    <IconComponent className="h-4 w-4" />
                    {label}
                  </Button>
                );
              })}
            </div>
          </div>

          {showAdvancedOptions && (
            <>
              <Separator />

              {/* Routing Preferences */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Label className="text-sm font-medium">Routing Preference</Label>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-4 w-4 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Choose how routes are calculated with traffic data</p>
                    </TooltipContent>
                  </Tooltip>
                </div>

                <Select
                  value={routingPreference}
                  onValueChange={(value: "TRAFFIC_UNAWARE" | "TRAFFIC_AWARE" | "TRAFFIC_AWARE_OPTIMAL") => setRoutingPreference(value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {routingPreferences.map(({ value, label, description }) => (
                      <SelectItem key={value} value={value}>
                        <div className="flex flex-col">
                          <span className={getRoutingPreferenceColor(value)}>{label}</span>
                          <span className="text-xs text-gray-500">{description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Advanced Options */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Advanced Options</Label>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-blue-500" />
                      <Label htmlFor="traffic-optimization" className="text-sm">
                        Traffic Optimization
                      </Label>
                    </div>
                    <Switch
                      id="traffic-optimization"
                      checked={enableTrafficOptimization}
                      onCheckedChange={setEnableTrafficOptimization}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Route className="h-4 w-4 text-purple-500" />
                      <Label htmlFor="show-alternatives" className="text-sm">
                        Show Alternatives
                      </Label>
                    </div>
                    <Switch
                      id="show-alternatives"
                      checked={showAlternatives}
                      onCheckedChange={setShowAlternatives}
                    />
                  </div>
                </div>
              </div>
            </>
          )}

          <Separator />

          {/* Route Calculation Buttons */}
          <div className="space-y-3">
            <div className="grid grid-cols-1 gap-2">
              <Button
                onClick={handleEnhancedCalculation}
                disabled={!isCalculationReady || isCalculating}
                className="w-full"
                size="lg"
              >
                {isCalculating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Calculating...
                  </>
                ) : (
                  <>
                    <MapPin className="h-4 w-4 mr-2" />
                    {enableTrafficOptimization ? 'Calculate Enhanced Route' : 'Calculate Route'}
                  </>
                )}
              </Button>

              {showAlternatives && (
                <Button
                  onClick={handleCalculateAlternatives}
                  disabled={!isCalculationReady || isCalculatingAlternatives}
                  variant="outline"
                  className="w-full"
                >
                  {isCalculatingAlternatives ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Finding Alternatives...
                    </>
                  ) : (
                    <>
                      <Route className="h-4 w-4 mr-2" />
                      Find Alternative Routes
                    </>
                  )}
                </Button>
              )}
            </div>

            <Button
              onClick={handleClearRoute}
              variant="outline"
              size="sm"
              className="w-full"
            >
              Clear Route
            </Button>
          </div>

          {/* Route Metadata */}
          {routeMetadata && (
            <>
              <Separator />
              <div className="space-y-2">
                <Label className="text-sm font-medium">Route Information</Label>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center gap-1">
                    <Settings className="h-3 w-3" />
                    <span>API: {routeMetadata.apiVersion.toUpperCase()}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{routeMetadata.calculationTime.toFixed(0)}ms</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {routeMetadata.cacheHit ? (
                      <CheckCircle className="h-3 w-3 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 text-blue-500" />
                    )}
                    <span>{routeMetadata.cacheHit ? 'Cached' : 'Fresh'}</span>
                  </div>
                  {alternativeRoutes.length > 0 && (
                    <div className="flex items-center gap-1">
                      <Route className="h-3 w-3" />
                      <span>{alternativeRoutes.length} alternatives</span>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Alternative Routes */}
          {alternativeRoutes.length > 0 && showAlternatives && (
            <>
              <Separator />
              <div className="space-y-3">
                <Label className="text-sm font-medium">Alternative Routes</Label>
                <div className="space-y-2">
                  {alternativeRoutes.map((route, index) => (
                    <Button
                      key={route.id}
                      variant={selectedAlternativeIndex === index ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleSelectAlternative(index)}
                      className="w-full justify-between"
                    >
                      <span>Route {index + 1}</span>
                      <div className="text-xs">
                        {route.estimatedDuration} • {route.estimatedDistance}
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Help Text */}
          {!isCalculationReady && (
            <div className="text-sm text-gray-500 text-center py-2">
              Add at least 2 waypoints to calculate a route
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
