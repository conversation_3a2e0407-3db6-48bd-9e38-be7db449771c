import { useCallback, useMemo } from 'react';
import { But<PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { ScrollArea } from '~/components/ui/scroll-area';
import { SwipeableBottomSheet, useSwipeableBottomSheet } from '~/components/ui/swipeable-bottom-sheet';
import {
  Navigation,
  Clock,
  MapPin,
  ChevronUp,
  ChevronDown,
  ArrowRight,
  ArrowLeft,
  ArrowUp,
  RotateCcw,
  Route
} from 'lucide-react';
import { useWanderlustStore } from '~/stores/wanderlust';
import { useMapContext } from '~/components/maps/MapProvider';

export function TurnByTurnDirections() {
  const {
    currentSnap,
    snapTo,
  } = useSwipeableBottomSheet(0); // Start at 10% (collapsed)

  const {
    routePlanning,
    itinerary,
  } = useWanderlustStore();

  // Get real route data from MapProvider (which uses useRoutesV2)
  const { currentRoute, routeDetails } = useMapContext();

  // Handle snap changes for different content views
  const handleSnapChange = useCallback((_snapIndex: number) => {
    // snapIndex 0: 10% - Collapsed summary
    // snapIndex 1: 50% - Route overview
    // snapIndex 2: 90% - Full turn-by-turn directions
  }, []);

  // Extract real turn-by-turn directions from route data
  const directions = useMemo(() => {
    if (!routeDetails?.legs) return [];

    const allSteps: Array<{
      instruction: string;
      distance: string;
      duration: string;
      maneuver: string;
      streetName: string;
    }> = [];

    routeDetails.legs.forEach((leg: any) => {
      leg.steps.forEach((step: any) => {
        // Clean HTML from instructions
        const cleanInstruction = step.instruction.replace(/<[^>]*>/g, '');

        // Extract street name from instruction or use a fallback
        const streetNameMatch = cleanInstruction.match(/on\s+([^,]+)/i) ||
                               cleanInstruction.match(/onto\s+([^,]+)/i) ||
                               cleanInstruction.match(/toward\s+([^,]+)/i);
        const streetName = streetNameMatch ? streetNameMatch[1].trim() : 'Road';

        // Determine maneuver type from instruction
        let maneuver = 'straight';
        const lowerInstruction = cleanInstruction.toLowerCase();
        if (lowerInstruction.includes('turn left') || lowerInstruction.includes('left onto')) {
          maneuver = 'turn-left';
        } else if (lowerInstruction.includes('turn right') || lowerInstruction.includes('right onto')) {
          maneuver = 'turn-right';
        } else if (lowerInstruction.includes('u-turn')) {
          maneuver = 'u-turn';
        } else if (lowerInstruction.includes('arrive') || lowerInstruction.includes('destination')) {
          maneuver = 'arrive';
        }

        allSteps.push({
          instruction: cleanInstruction,
          distance: step.distance,
          duration: step.duration,
          maneuver,
          streetName,
        });
      });
    });

    return allSteps;
  }, [routeDetails]);

  // Don't show if no route or less than 2 waypoints
  if (!currentRoute || itinerary.length < 2) {
    return null;
  }

  // Get appropriate icon for maneuver type
  const getManeuverIcon = (maneuver: string) => {
    switch (maneuver) {
      case 'turn-left':
        return ArrowLeft;
      case 'turn-right':
        return ArrowRight;
      case 'straight':
        return ArrowUp;
      case 'u-turn':
        return RotateCcw;
      case 'arrive':
        return MapPin;
      default:
        return ArrowRight;
    }
  };

  // Render collapsed summary content
  const renderCollapsedContent = () => (
    <div className="px-4 py-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Navigation className="h-4 w-4 text-yellow-400" />
          <div>
            <div className="text-white font-medium text-sm">
              {directions.length} steps
            </div>
            <div className="text-white/60 text-xs">
              {currentRoute.estimatedDuration}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {routePlanning.isPlanning && (
            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-yellow-400"></div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => snapTo(1)}
            className="text-yellow-400 hover:text-yellow-300 hover:bg-yellow-500/10 p-1"
          >
            <ChevronUp className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );

  // Render route overview content
  const renderRouteOverview = () => (
    <div className="px-4 py-3 space-y-4">
      {/* Route Summary Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Route className="h-5 w-5 text-yellow-400" />
          <div>
            <h3 className="text-white font-medium text-base">Route Overview</h3>
            <div className="text-white/70 text-sm">
              {currentRoute.estimatedDistance} • {currentRoute.estimatedDuration}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => snapTo(0)}
            className="text-white/60 hover:text-white hover:bg-white/10 p-1"
          >
            <ChevronDown className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => snapTo(2)}
            className="text-yellow-400 hover:text-yellow-300 hover:bg-yellow-500/10 p-1"
          >
            <ChevronUp className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Route Stats */}
      <div className="grid grid-cols-3 gap-3">
        <div className="bg-white/5 rounded-lg p-3 text-center">
          <MapPin className="h-4 w-4 text-yellow-400 mx-auto mb-1" />
          <div className="text-white text-sm font-medium">{currentRoute.estimatedDistance}</div>
          <div className="text-white/60 text-xs">Distance</div>
        </div>
        <div className="bg-white/5 rounded-lg p-3 text-center">
          <Clock className="h-4 w-4 text-yellow-400 mx-auto mb-1" />
          <div className="text-white text-sm font-medium">{currentRoute.estimatedDuration}</div>
          <div className="text-white/60 text-xs">Duration</div>
        </div>
        <div className="bg-white/5 rounded-lg p-3 text-center">
          <Navigation className="h-4 w-4 text-yellow-400 mx-auto mb-1" />
          <div className="text-white text-sm font-medium">{directions.length}</div>
          <div className="text-white/60 text-xs">Steps</div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="flex space-x-2">
        <Button
          size="sm"
          onClick={() => snapTo(2)}
          className="flex-1 bg-yellow-500 text-black hover:bg-yellow-400 font-medium"
        >
          <Navigation className="h-3 w-3 mr-2" />
          View Directions
        </Button>
      </div>
    </div>
  );

  return (
    <SwipeableBottomSheet
      snapPoints={['10%', '50%', '90%']}
      initialSnap={0}
      onSnapChange={handleSnapChange}
      className="bg-gradient-to-br from-black via-gray-900 to-red-900"
      contentClassName="overflow-y-auto"
    >
      {/* Content based on current snap position */}
      {currentSnap === 0 && renderCollapsedContent()}
      {currentSnap === 1 && renderRouteOverview()}
      {currentSnap === 2 && (
        <div className="h-full flex flex-col">
          {/* Full Directions Header */}
          <div className="px-4 py-3 border-b border-white/10 flex-shrink-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Navigation className="h-5 w-5 text-yellow-400" />
                <div>
                  <h3 className="text-white font-medium text-base">Turn-by-Turn Directions</h3>
                  <div className="text-white/70 text-sm">
                    {directions.length} steps • {currentRoute.estimatedDuration}
                  </div>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => snapTo(1)}
                className="text-white/60 hover:text-white hover:bg-white/10 p-1"
              >
                <ChevronDown className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Scrollable Directions List */}
          <ScrollArea className="flex-1">
            <div className="p-4 space-y-3">
              {directions.map((step, index) => {
                const IconComponent = getManeuverIcon(step.maneuver);

                return (
                  <div
                    key={index}
                    className="flex items-start space-x-4 p-4 rounded-lg bg-white/5 border border-white/10 hover:border-yellow-500/30 transition-colors"
                  >
                    {/* Step Number & Icon */}
                    <div className="flex flex-col items-center space-y-2 flex-shrink-0">
                      <div className="w-8 h-8 rounded-full bg-yellow-500 text-black font-bold text-sm flex items-center justify-center">
                        {index + 1}
                      </div>
                      <IconComponent className="h-4 w-4 text-yellow-400" />
                    </div>

                    {/* Step Details */}
                    <div className="flex-1 min-w-0">
                      <div className="text-white font-medium text-sm leading-5 mb-2">
                        {step.instruction}
                      </div>
                      <div className="text-sm text-white/60 leading-4 mb-3">
                        {step.streetName}
                      </div>
                      <div className="flex flex-wrap items-center gap-2">
                        <Badge variant="outline" className="text-xs border-white/20 text-white/70 whitespace-nowrap">
                          {step.distance}
                        </Badge>
                        <Badge variant="outline" className="text-xs border-white/20 text-white/70 whitespace-nowrap">
                          {step.duration}
                        </Badge>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </div>
      )}
    </SwipeableBottomSheet>
  );
}
