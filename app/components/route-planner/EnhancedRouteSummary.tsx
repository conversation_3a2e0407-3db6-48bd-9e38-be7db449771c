/**
 * Enhanced Route Summary Component
 * 
 * Displays comprehensive route information with Routes API v2 features
 * including traffic data, toll information, and environmental metrics.
 */

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '~/components/ui/collapsible';
import { Progress } from '~/components/ui/progress';
import { useWanderlustStore } from '~/stores/wanderlust';
import { useRouteCalculation } from '~/hooks/useRouteCalculation';
import { 
  Clock, 
  MapPin, 
  DollarSign, 
  Leaf, 
  AlertTriangle, 
  Info,
  ChevronDown,
  ChevronUp,
  Navigation,
  Fuel,
  Car,
  Route,
  TrendingUp,
  Shield,
  Zap
} from 'lucide-react';

interface EnhancedRouteSummaryProps {
  className?: string;
  showDetails?: boolean;
  showEnvironmentalInfo?: boolean;
}

export function EnhancedRouteSummary({ 
  className = '',
  showDetails = true,
  showEnvironmentalInfo = true 
}: EnhancedRouteSummaryProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showTrafficDetails, setShowTrafficDetails] = useState(false);
  
  const { currentRoute } = useWanderlustStore();
  const { routeMetadata, alternativeRoutes } = useRouteCalculation();

  if (!currentRoute) {
    return (
      <Card className={className}>
        <CardContent className="py-8 text-center">
          <Navigation className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500">No route calculated</p>
          <p className="text-sm text-gray-400 mt-1">
            Add waypoints and calculate a route to see summary
          </p>
        </CardContent>
      </Card>
    );
  }

  // Mock enhanced data (in real implementation, this would come from Routes V2 response)
  const enhancedData = {
    trafficLevel: 'MODERATE' as const,
    trafficDelay: 8, // minutes
    tollCost: 4.50,
    fuelConsumption: 2.3, // liters
    co2Emissions: 5.4, // kg
    ecoScore: 78, // 0-100
    safetyScore: 92, // 0-100
    comfortScore: 85, // 0-100
    hasTrafficIncidents: true,
    incidentCount: 2,
    alternativesSaved: alternativeRoutes.length > 0 ? 12 : 0, // minutes saved vs worst alternative
  };

  // Get traffic level styling
  const getTrafficStyling = (level: string) => {
    switch (level) {
      case 'LIGHT': return { color: 'text-green-600', bg: 'bg-green-100', icon: '🟢' };
      case 'MODERATE': return { color: 'text-yellow-600', bg: 'bg-yellow-100', icon: '🟡' };
      case 'HEAVY': return { color: 'text-red-600', bg: 'bg-red-100', icon: '🔴' };
      default: return { color: 'text-gray-600', bg: 'bg-gray-100', icon: '⚪' };
    }
  };

  const trafficStyling = getTrafficStyling(enhancedData.trafficLevel);

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <Route className="h-5 w-5" />
          Route Summary
          {routeMetadata && (
            <Badge variant="outline" className="ml-auto">
              {routeMetadata.apiVersion.toUpperCase()}
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Basic Route Info */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-blue-500" />
            <div>
              <div className="font-medium">{currentRoute.estimatedDuration}</div>
              <div className="text-xs text-gray-500">
                {enhancedData.trafficDelay > 0 && (
                  <span className="text-amber-600">
                    +{enhancedData.trafficDelay}m in traffic
                  </span>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-green-500" />
            <div>
              <div className="font-medium">{currentRoute.estimatedDistance}</div>
              <div className="text-xs text-gray-500">
                {currentRoute.waypoints.length} waypoints
              </div>
            </div>
          </div>
        </div>

        {/* Traffic Status */}
        <div className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
          <div className="flex items-center gap-2">
            <span className="text-lg">{trafficStyling.icon}</span>
            <div>
              <div className="font-medium text-sm">Traffic: {enhancedData.trafficLevel}</div>
              {enhancedData.hasTrafficIncidents && (
                <div className="text-xs text-amber-600">
                  {enhancedData.incidentCount} incidents reported
                </div>
              )}
            </div>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowTrafficDetails(!showTrafficDetails)}
          >
            <Info className="h-4 w-4" />
          </Button>
        </div>

        {showTrafficDetails && (
          <div className="p-3 bg-blue-50 rounded-lg text-sm">
            <div className="font-medium mb-2">Traffic Details</div>
            <div className="space-y-1 text-gray-600">
              <div>• Current delay: {enhancedData.trafficDelay} minutes</div>
              <div>• {enhancedData.incidentCount} traffic incidents on route</div>
              <div>• Traffic data updated 2 minutes ago</div>
            </div>
          </div>
        )}

        {/* Cost Information */}
        {enhancedData.tollCost > 0 && (
          <div className="flex items-center justify-between p-3 rounded-lg border">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-green-600" />
              <div>
                <div className="font-medium text-sm">Estimated Tolls</div>
                <div className="text-xs text-gray-500">Based on current rates</div>
              </div>
            </div>
            <div className="text-lg font-bold text-green-600">
              ${enhancedData.tollCost.toFixed(2)}
            </div>
          </div>
        )}

        {/* Environmental Information */}
        {showEnvironmentalInfo && (
          <div className="space-y-3">
            <Separator />
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Leaf className="h-4 w-4 text-green-500" />
                <span className="font-medium text-sm">Environmental Impact</span>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-600">Eco Score</span>
                    <span className="font-medium">{enhancedData.ecoScore}%</span>
                  </div>
                  <Progress value={enhancedData.ecoScore} className="h-1" />
                </div>
                
                <div className="space-y-1">
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-600">Safety Score</span>
                    <span className="font-medium">{enhancedData.safetyScore}%</span>
                  </div>
                  <Progress value={enhancedData.safetyScore} className="h-1" />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div className="flex items-center gap-1">
                  <Fuel className="h-3 w-3 text-blue-500" />
                  <span>{enhancedData.fuelConsumption}L fuel</span>
                </div>
                <div className="flex items-center gap-1">
                  <Leaf className="h-3 w-3 text-green-500" />
                  <span>{enhancedData.co2Emissions}kg CO₂</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Route Optimization Info */}
        {enhancedData.alternativesSaved > 0 && (
          <div className="p-3 bg-green-50 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="font-medium text-sm text-green-800">Optimized Route</span>
            </div>
            <div className="text-xs text-green-700">
              This route saves {enhancedData.alternativesSaved} minutes compared to alternatives
            </div>
          </div>
        )}

        {/* Detailed Information */}
        {showDetails && (
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-0">
                <span className="text-sm font-medium">Route Details</span>
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            
            <CollapsibleContent className="space-y-3 mt-3">
              <Separator />
              
              {/* Route Legs */}
              {currentRoute.legs && currentRoute.legs.length > 0 && (
                <div className="space-y-2">
                  <div className="font-medium text-sm">Route Segments</div>
                  {currentRoute.legs.map((leg, index) => (
                    <div key={index} className="flex items-center justify-between text-xs p-2 bg-gray-50 rounded">
                      <span>Segment {index + 1}</span>
                      <div className="flex gap-3">
                        <span>{leg.duration.text}</span>
                        <span>{leg.distance.text}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {/* Warnings */}
              {currentRoute.warnings && currentRoute.warnings.length > 0 && (
                <div className="space-y-2">
                  <div className="font-medium text-sm">Warnings</div>
                  {currentRoute.warnings.map((warning, index) => (
                    <div key={index} className="flex items-start gap-2 text-xs text-amber-700 p-2 bg-amber-50 rounded">
                      <AlertTriangle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                      <span>{warning}</span>
                    </div>
                  ))}
                </div>
              )}
              
              {/* API Metadata */}
              {routeMetadata && (
                <div className="space-y-2">
                  <div className="font-medium text-sm">Calculation Info</div>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center gap-1">
                      <Zap className="h-3 w-3" />
                      <span>API: {routeMetadata.apiVersion.toUpperCase()}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{routeMetadata.calculationTime.toFixed(0)}ms</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Shield className="h-3 w-3" />
                      <span>{routeMetadata.cacheHit ? 'Cached' : 'Fresh'}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Route className="h-3 w-3" />
                      <span>{alternativeRoutes.length} alternatives</span>
                    </div>
                  </div>
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>
        )}

        {/* Copyright */}
        <div className="text-xs text-gray-400 text-center pt-2">
          {currentRoute.copyrights || 'Map data ©2024 Google'}
        </div>
      </CardContent>
    </Card>
  );
}
