import { useState, useMemo, useCallback } from 'react';
import {
  ChevronLeft,
  ChevronRight,
  Locate
} from 'lucide-react';
import { Button } from '~/components/ui/button';
import { ScrollArea } from '~/components/ui/scroll-area';
import { useWanderlustStore } from '~/stores/wanderlust';
import { useUnifiedPlaceManagement } from '~/hooks/useUnifiedPlaceManagement';
import { EnhancedSearchInterface } from '~/components/shared/EnhancedSearchInterface';
import { PlaceManagementPanel } from '~/components/shared/PlaceManagementPanel';
import type { CityRegion, VisitedPlace } from '~/types/wanderlust';
import { cn } from '~/lib/utils';
import { useTranslation } from 'react-i18next';

interface WaypointPanelProps {
  regions: CityRegion[];
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

export function WaypointPanel({ regions, isCollapsed, onToggleCollapse }: WaypointPanelProps) {
  const { t } = useTranslation('routePlanner');

  const placeManagement = useUnifiedPlaceManagement({
    mode: 'route-planning',
    enableWaypoints: true,
    enableReordering: true,
    maxPlaces: 25,
    enableDuplicateCheck: true,
  });

  const {
    state: {
      places: itinerary,
      selectedPlace,
    },
    actions: {
      addPlace: handleAddWaypoint,
      removePlace: handleRemoveWaypoint,
      reorderPlaces: handleReorderWaypoint,
      clearPlaces: handleClearWaypoints, // Add clearPlaces action
      selectPlace: handleSelectWaypoint, // Add selectPlace action
    },
  } = placeManagement;

  const {
    calculateRouteFromItinerary,
    routePlanning,
  } = useWanderlustStore();

  // Implement handleAddCurrentLocation
  const handleAddCurrentLocation = useCallback(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newPlace: VisitedPlace = {
            id: `current-location-${Date.now()}`,
            name: t('waypointPanel.currentLocationName'), // Translate "Current Location"
            description: { en: t('waypointPanel.currentLocationDescription'), fr: '', ar: '' }, // Translate description
            coordinates: {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            },
            category: 'transport', // Assign a suitable category
            icon: '📍',
            city: '', // Can be reverse geocoded if needed
            region: '',
            rating: 0,
            visitDate: new Date().toISOString(),
            revisitPotential: 'Worth a Look',
          };
          handleAddWaypoint(newPlace);
        },
        (error) => {
          console.error("Error getting current location:", error);
          // TODO: Show user-friendly error notification
        }
      );
    } else {
      console.warn("Geolocation is not supported by this browser.");
      // TODO: Show user-friendly warning
    }
  }, [handleAddWaypoint, t]);

  // Define handlePlaceAction for PlaceManagementPanel
  const handlePlaceAction = useCallback((action: { type: string; payload?: any }, place: VisitedPlace, index?: number) => {
    switch (action.type) {
      case 'remove':
        handleRemoveWaypoint(place.id);
        break;
      case 'reorder':
        if (action.payload && typeof index === 'number') {
          handleReorderWaypoint(action.payload.from, action.payload.to);
        }
        break;
      case 'select':
        handleSelectWaypoint(place);
        break;
      case 'clear':
        handleClearWaypoints();
        break;
      default:
        console.warn(`Unhandled place action: ${action.type}`);
    }
  }, [handleRemoveWaypoint, handleReorderWaypoint, handleSelectWaypoint, handleClearWaypoints]);

  // Define handleBulkAction for PlaceManagementPanel
  const handleBulkAction = useCallback((action: { type: string; payload?: any }, places: VisitedPlace[]) => {
    switch (action.type) {
      case 'clear':
        handleClearWaypoints();
        break;
      case 'optimize':
        console.log("TODO: Implement route optimization for selected waypoints");
        // This would typically involve calling a route optimization service
        break;
      default:
        console.warn(`Unhandled bulk action: ${action.type}`);
    }
  }, [handleClearWaypoints]);


  const handleCalculateRoute = () => {
    if (itinerary.length >= 2) {
      calculateRouteFromItinerary();
    }
  };

  // Handle place selection from search interface
  const handlePlaceSelectFromSearch = useCallback((place: VisitedPlace) => {
    handleAddWaypoint(place);
  }, [handleAddWaypoint]);

  return (
    <>
      {/* Desktop Sidebar */}
      <div className={cn(
        "hidden lg:block fixed left-0 top-[200px] bottom-0 bg-black/90 backdrop-blur-xl border-r border-[#FFD700]/30 transition-all duration-300 z-30",
        isCollapsed ? "w-16" : "w-80"
      )}>
        {/* Collapse Toggle */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleCollapse}
          className="absolute -right-3 top-4 z-40 w-6 h-6 rounded-full bg-[#FFD700] text-black hover:bg-[#FFF700] shadow-lg"
        >
          {isCollapsed ? <ChevronRight className="h-3 w-3" /> : <ChevronLeft className="h-3 w-3" />}
        </Button>

        {!isCollapsed && (
          <div className="p-4 h-full flex flex-col">
            {/* Header */}
            <div className="mb-4">
              <h3 className="text-lg font-bold text-white mb-2">{t('waypointPanel.title')}</h3>
              <p className="text-sm text-white/60">{t('waypointPanel.subtitle')}</p>
            </div>

            {/* Current Itinerary / Waypoint List */}
            <PlaceManagementPanel
              mode="waypoints"
              places={itinerary}
              enableReordering={true}
              enableBulkActions={true}
              onPlaceAction={handlePlaceAction}
              onBulkAction={handleBulkAction}
              title={t('waypointPanel.currentRouteTitle', { count: itinerary.length })}
              emptyMessage={t('waypointPanel.emptyMessage')}
              className="mb-4"
            />


            {/* Quick Actions */}
            <div className="mb-4 space-y-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleAddCurrentLocation}
                className="w-full justify-start text-white border-[#FFD700]/30 hover:bg-[#FFD700]/10"
              >
                <Locate className="h-4 w-4 mr-2" />
                {t('waypointPanel.addCurrentLocation')}
              </Button>
               {itinerary.length >= 2 && (
                      <Button
                        size="sm"
                        onClick={handleCalculateRoute}
                        disabled={routePlanning.isPlanning}
                        className="w-full justify-center bg-gradient-to-r from-[#FFD700] to-[#DC2626] text-black hover:from-[#FFF700] hover:to-[#EF4444] text-xs"
                      >
                        {routePlanning.isPlanning ? t('waypointPanel.calculatingRoute') : t('waypointPanel.calculateRoute')}
                      </Button>
                    )}
            </div>

            {/* Search Interface */}
            <EnhancedSearchInterface
              searchMode="route-planning"
              regions={regions}
              onPlaceSelect={handlePlaceSelectFromSearch}
              enableFilters={true}
              enableHistory={true}
              enableCategories={true}
              enableAdvancedFilters={true}
              placeholder={t('waypointPanel.searchPlaceholder')}
              className="flex-1 min-h-0"
            />

          </div>
        )}

        {isCollapsed && (
          <div className="p-2 flex flex-col items-center space-y-4 mt-12">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleAddCurrentLocation}
              className="w-12 h-12 p-0 text-white hover:bg-white/10"
              title={t('waypointPanel.addCurrentLocation')}
            >
              <Locate className="h-5 w-5" />
            </Button>
            <div className="text-center">
              <div className="text-xs text-white/60">{itinerary.length}</div>
              <div className="text-xs text-white/40">{t('waypointPanel.stopsLabel')}</div>
            </div>
          </div>
        )}
      </div>

      {/* Mobile Bottom Sheet - TODO: Implement mobile version */}
      <div className="lg:hidden">
        {/* Mobile implementation would go here */}
      </div>
    </>
  );
}
