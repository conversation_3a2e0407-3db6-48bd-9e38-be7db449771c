/**
 * Routes V2 Feature Guide Component
 *
 * Interactive guide showcasing new Routes API v2 features
 * and helping users understand the enhanced capabilities.
 */

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Progress } from '~/components/ui/progress';
import {
  Zap,
  Route,
  Clock,
  DollarSign,
  Leaf,
  Shield,
  TrendingUp,
  Info,
  CheckCircle,
  Star,
  ArrowRight,
  Sparkles
} from 'lucide-react';

interface RoutesV2FeatureGuideProps {
  className?: string;
  onClose?: () => void;
  showOnlyNew?: boolean;
}

export function RoutesV2FeatureGuide({
  className = '',
  onClose,
  showOnlyNew = false
}: RoutesV2FeatureGuideProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [completedFeatures, setCompletedFeatures] = useState<string[]>([]);

  const features = [
    {
      id: 'traffic-aware',
      title: 'Traffic-Aware Routing',
      icon: Clock,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      description: 'Real-time traffic data for optimal route planning',
      benefits: [
        'Avoid traffic jams and delays',
        'Get accurate arrival times',
        'Dynamic route adjustments'
      ],
      isNew: true,
    },
    {
      id: 'alternatives',
      title: 'Alternative Routes',
      icon: Route,
      color: 'text-purple-500',
      bgColor: 'bg-purple-50',
      description: 'Multiple route options with detailed comparisons',
      benefits: [
        'Compare time vs distance',
        'Choose based on preferences',
        'See cost differences'
      ],
      isNew: true,
    },
    {
      id: 'toll-info',
      title: 'Toll Information',
      icon: DollarSign,
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      description: 'Detailed toll costs and toll-free alternatives',
      benefits: [
        'Know costs upfront',
        'Find toll-free routes',
        'Budget your trips'
      ],
      isNew: true,
    },
    {
      id: 'eco-routing',
      title: 'Eco-Friendly Routes',
      icon: Leaf,
      color: 'text-emerald-500',
      bgColor: 'bg-emerald-50',
      description: 'Environmentally conscious route optimization',
      benefits: [
        'Reduce fuel consumption',
        'Lower CO₂ emissions',
        'Eco score tracking'
      ],
      isNew: true,
    },
    {
      id: 'enhanced-performance',
      title: 'Enhanced Performance',
      icon: Zap,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50',
      description: 'Faster calculations with improved caching',
      benefits: [
        '6x higher rate limits',
        'Intelligent caching',
        'Faster response times'
      ],
      isNew: false,
    },
    {
      id: 'safety-features',
      title: 'Safety & Quality',
      icon: Shield,
      color: 'text-red-500',
      bgColor: 'bg-red-50',
      description: 'Enhanced safety metrics and route quality indicators',
      benefits: [
        'Safety score ratings',
        'Incident reporting',
        'Quality assessments'
      ],
      isNew: true,
    },
  ];

  const filteredFeatures = showOnlyNew ? features.filter(f => f.isNew) : features;

  const markFeatureCompleted = (featureId: string) => {
    if (!completedFeatures.includes(featureId)) {
      setCompletedFeatures([...completedFeatures, featureId]);
    }
  };

  const completionPercentage = (completedFeatures.length / filteredFeatures.length) * 100;

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-500" />
            Routes V2 Features
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              Enhanced
            </Badge>
          </CardTitle>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              ×
            </Button>
          )}
        </div>

        {/* Progress Indicator */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Feature Exploration</span>
            <span className="font-medium">{completedFeatures.length}/{filteredFeatures.length}</span>
          </div>
          <Progress value={completionPercentage} className="h-2" />
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="features">Features</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4 mt-4">
            <div className="text-center space-y-3">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto">
                <Route className="h-8 w-8 text-white" />
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Welcome to Routes V2</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  Experience the next generation of route planning with enhanced features,
                  real-time traffic data, and intelligent optimization.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <TrendingUp className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                <div className="text-sm font-medium">6x Faster</div>
                <div className="text-xs text-gray-600">API Rate Limits</div>
              </div>

              <div className="text-center p-3 bg-green-50 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600 mx-auto mb-1" />
                <div className="text-sm font-medium">Real-time</div>
                <div className="text-xs text-gray-600">Traffic Data</div>
              </div>

              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <Route className="h-6 w-6 text-purple-600 mx-auto mb-1" />
                <div className="text-sm font-medium">3+ Routes</div>
                <div className="text-xs text-gray-600">Alternatives</div>
              </div>

              <div className="text-center p-3 bg-emerald-50 rounded-lg">
                <Leaf className="h-6 w-6 text-emerald-600 mx-auto mb-1" />
                <div className="text-sm font-medium">Eco-Friendly</div>
                <div className="text-xs text-gray-600">Options</div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <div className="font-medium text-blue-900 mb-1">Seamless Migration</div>
                  <div className="text-blue-700">
                    All existing functionality is preserved while new features are gradually enabled.
                    Your current routes and preferences remain unchanged.
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="features" className="space-y-4 mt-4">
            <div className="space-y-3">
              {filteredFeatures.map((feature) => {
                const Icon = feature.icon;
                const isCompleted = completedFeatures.includes(feature.id);

                return (
                  <Card
                    key={feature.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      isCompleted ? 'ring-2 ring-green-200 bg-green-50' : 'hover:bg-gray-50'
                    }`}
                    onClick={() => markFeatureCompleted(feature.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className={`p-2 rounded-lg ${feature.bgColor} flex-shrink-0`}>
                          <Icon className={`h-5 w-5 ${feature.color}`} />
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-sm">{feature.title}</h4>
                            {feature.isNew && (
                              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                New
                              </Badge>
                            )}
                            {isCompleted && (
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            )}
                          </div>

                          <p className="text-xs text-gray-600 mb-2">{feature.description}</p>

                          <div className="space-y-1">
                            {feature.benefits.map((benefit, index) => (
                              <div key={index} className="flex items-center gap-2 text-xs text-gray-500">
                                <div className="w-1 h-1 bg-gray-400 rounded-full flex-shrink-0"></div>
                                <span>{benefit}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        <ArrowRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {completionPercentage === 100 && (
              <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                <Star className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <div className="font-medium text-green-900 mb-1">Congratulations!</div>
                <div className="text-sm text-green-700">
                  You've explored all the new Routes V2 features. Start planning enhanced routes now!
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex gap-2 mt-6 pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            className="flex-1"
            onClick={() => setActiveTab(activeTab === 'overview' ? 'features' : 'overview')}
          >
            {activeTab === 'overview' ? 'Explore Features' : 'Back to Overview'}
          </Button>

          {onClose && (
            <Button
              size="sm"
              className="flex-1"
              onClick={onClose}
            >
              Start Using V2
            </Button>
          )}
        </div>

        {/* Environment Info */}
        <div className="mt-4 pt-3 border-t text-xs text-gray-500 text-center">
          Routes V2: {import.meta.env.VITE_USE_ROUTES_V2 === 'true' ? 'Enabled' : 'Disabled'} •
          Rollout: {import.meta.env.VITE_ROUTES_V2_PERCENTAGE || '0'}%
        </div>
      </CardContent>
    </Card>
  );
}
