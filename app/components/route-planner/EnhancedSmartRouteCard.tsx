/**
 * Enhanced Smart Route Card Component
 * 
 * Advanced route planning interface with Routes API v2 integration
 * featuring enhanced controls, alternative routes, and detailed summaries.
 */

import { useState } from 'react';
import { CardContent } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  MapPin,
  Clock,
  Route,
  ChevronUp,
  ChevronDown,
  Settings,
  BarChart3,
  Zap
} from 'lucide-react';
import { useWanderlustStore } from '~/stores/wanderlust';
import { useRouteCalculation } from '~/hooks/useRouteCalculation';
import { WaypointManager } from './shared/WaypointManager';
import { ClearRouteButton } from './shared/ClearRouteButton';
import { EnhancedRouteControls } from './EnhancedRouteControls';
import { RouteAlternatives } from './RouteAlternatives';
import { EnhancedRouteSummary } from './EnhancedRouteSummary';
import type { CityRegion } from '~/types/wanderlust';

interface EnhancedSmartRouteCardProps {
  regions: CityRegion[];
  enableAdvancedFeatures?: boolean;
  showAlternatives?: boolean;
}

export function EnhancedSmartRouteCard({ 
  regions,
  enableAdvancedFeatures = true,
  showAlternatives = true 
}: EnhancedSmartRouteCardProps) {
  const [builderMode, setBuilderMode] = useState<'building' | 'preview'>('building');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('controls');

  const { itinerary } = useWanderlustStore();

  const {
    isCurrentlyCalculating,
    isCalculatingAlternatives,
    currentRoute,
    isCalculationReady,
    alternativeRoutes,
    routeMetadata,
    handleClearRoute,
    handleClearRouteAndItinerary,
  } = useRouteCalculation();

  // Switch to preview mode after route calculation
  const handleRouteCalculated = () => {
    if (currentRoute) {
      setBuilderMode('preview');
      setActiveTab('summary');
    }
  };

  // Get header status info
  const getHeaderStatus = () => {
    if (currentRoute) {
      return {
        distance: currentRoute.estimatedDistance,
        duration: currentRoute.estimatedDuration,
        waypoints: itinerary.length,
        apiVersion: routeMetadata?.apiVersion,
        alternatives: alternativeRoutes.length,
      };
    }
    return null;
  };

  const headerStatus = getHeaderStatus();

  return (
    <div className="border-b border-yellow-500/30 bg-black/95 backdrop-blur-sm">
      {/* Collapsed Header - Always Visible */}
      <div
        className="p-4 cursor-pointer hover:bg-white/5 transition-colors"
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <div className="flex items-start justify-between gap-4">
          <div className="flex items-start space-x-3 flex-1 min-w-0">
            <Route className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="text-white font-medium text-base leading-6">Enhanced Route Planner</h3>
                {enableAdvancedFeatures && (
                  <Badge variant="outline" className="text-xs bg-blue-500/20 text-blue-300 border-blue-500/30">
                    V2
                  </Badge>
                )}
              </div>
              
              {headerStatus && (
                <div className="flex flex-wrap items-center gap-x-4 gap-y-1 text-sm text-white/70">
                  <span className="flex items-center whitespace-nowrap">
                    <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                    <span className="truncate">{headerStatus.distance}</span>
                  </span>
                  <span className="flex items-center whitespace-nowrap">
                    <Clock className="h-3 w-3 mr-1 flex-shrink-0" />
                    <span className="truncate">{headerStatus.duration}</span>
                  </span>
                  <Badge className="bg-yellow-500 text-black text-xs whitespace-nowrap">
                    {headerStatus.waypoints} stop{headerStatus.waypoints !== 1 ? 's' : ''}
                  </Badge>
                  {headerStatus.apiVersion && (
                    <Badge variant="outline" className="text-xs bg-green-500/20 text-green-300 border-green-500/30">
                      {headerStatus.apiVersion.toUpperCase()}
                    </Badge>
                  )}
                  {headerStatus.alternatives > 0 && (
                    <Badge variant="outline" className="text-xs bg-purple-500/20 text-purple-300 border-purple-500/30">
                      +{headerStatus.alternatives} alt
                    </Badge>
                  )}
                </div>
              )}
              
              {!currentRoute && itinerary.length > 0 && (
                <div className="text-sm text-white/70 leading-5">
                  {itinerary.length} waypoint{itinerary.length !== 1 ? 's' : ''} added -
                  <span className="text-yellow-400 ml-1">Calculate route to see details</span>
                </div>
              )}
              
              {!currentRoute && itinerary.length === 0 && (
                <div className="text-sm text-white/70 leading-5">
                  Add waypoints to plan your enhanced route
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2 flex-shrink-0">
            {(isCurrentlyCalculating || isCalculatingAlternatives) && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-400"></div>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="text-yellow-400 hover:text-yellow-300 hover:bg-yellow-500/10 p-2"
            >
              {isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Expandable Content */}
      {!isCollapsed && (
        <div className="border-t border-white/10">
          <CardContent className="p-4 space-y-6 max-h-[70vh] overflow-y-auto">
            {/* Building Mode */}
            {builderMode === 'building' && (
              <div className="space-y-4">
                {/* Waypoint Management */}
                <WaypointManager
                  regions={regions}
                  variant="compact"
                  showCategoryFilter={true}
                  showCurrentLocationButton={true}
                  maxPlacesHeight="h-48"
                  maxWaypointsHeight="max-h-32"
                  enableReordering={true}
                  enableDragAndDrop={true}
                  enableUpDownButtons={true}
                />

                {/* Enhanced Route Controls */}
                {enableAdvancedFeatures && isCalculationReady && (
                  <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/30 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <Zap className="h-4 w-4 text-blue-400" />
                      <h4 className="text-white font-medium text-sm">Enhanced Route Calculation</h4>
                    </div>
                    <EnhancedRouteControls 
                      showAdvancedOptions={true}
                      className="bg-transparent border-0 shadow-none"
                    />
                  </div>
                )}

                {/* Basic Calculate Button for non-advanced mode */}
                {(!enableAdvancedFeatures && isCalculationReady) && (
                  <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                    <div className="text-center mb-3">
                      <h4 className="text-white font-medium text-sm mb-1">Ready to Calculate Route</h4>
                      <p className="text-white/70 text-xs">
                        {itinerary.length} waypoints added. Click below to calculate your route.
                      </p>
                    </div>
                    <Button
                      onClick={handleRouteCalculated}
                      disabled={isCurrentlyCalculating}
                      className="w-full bg-yellow-500 text-black hover:bg-yellow-400 font-medium"
                    >
                      {isCurrentlyCalculating ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
                          Calculating...
                        </>
                      ) : (
                        <>
                          <Route className="h-4 w-4 mr-2" />
                          Calculate Route
                        </>
                      )}
                    </Button>
                  </div>
                )}

                {/* Clear Route Actions */}
                {(itinerary.length > 0 || currentRoute) && (
                  <div className="flex space-x-2 pt-2 border-t border-white/10">
                    {currentRoute && (
                      <ClearRouteButton
                        variant="route-only"
                        onClearRoute={handleClearRoute}
                        size="sm"
                        className="flex-1"
                        showConfirmation={false}
                      />
                    )}
                    {itinerary.length > 0 && (
                      <ClearRouteButton
                        variant="route-and-waypoints"
                        onClearRouteAndItinerary={handleClearRouteAndItinerary}
                        size="sm"
                        className="flex-1"
                        showConfirmation={true}
                      />
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Preview Mode */}
            {builderMode === 'preview' && currentRoute && (
              <div className="space-y-4">
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-3 bg-white/10">
                    <TabsTrigger value="summary" className="text-white data-[state=active]:bg-yellow-500 data-[state=active]:text-black">
                      <BarChart3 className="h-4 w-4 mr-1" />
                      Summary
                    </TabsTrigger>
                    <TabsTrigger value="controls" className="text-white data-[state=active]:bg-yellow-500 data-[state=active]:text-black">
                      <Settings className="h-4 w-4 mr-1" />
                      Controls
                    </TabsTrigger>
                    {showAlternatives && (
                      <TabsTrigger value="alternatives" className="text-white data-[state=active]:bg-yellow-500 data-[state=active]:text-black">
                        <Route className="h-4 w-4 mr-1" />
                        Routes
                      </TabsTrigger>
                    )}
                  </TabsList>

                  <TabsContent value="summary" className="mt-4">
                    <EnhancedRouteSummary 
                      showDetails={true}
                      showEnvironmentalInfo={enableAdvancedFeatures}
                      className="bg-transparent border-white/20"
                    />
                  </TabsContent>

                  <TabsContent value="controls" className="mt-4">
                    <EnhancedRouteControls 
                      showAdvancedOptions={enableAdvancedFeatures}
                      className="bg-transparent border-white/20"
                    />
                  </TabsContent>

                  {showAlternatives && (
                    <TabsContent value="alternatives" className="mt-4">
                      <RouteAlternatives 
                        showComparison={enableAdvancedFeatures}
                        maxAlternatives={3}
                        className="bg-transparent border-white/20"
                      />
                    </TabsContent>
                  )}
                </Tabs>

                {/* Action Buttons */}
                <div className="flex space-x-2 pt-4 border-t border-white/10">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setBuilderMode('building')}
                    className="flex-1 border-white/20 text-white hover:bg-white/5"
                  >
                    Edit Route
                  </Button>
                  <ClearRouteButton
                    variant="route-only"
                    onClearRoute={() => {
                      handleClearRoute();
                      setBuilderMode('building');
                    }}
                    size="sm"
                    className="flex-1"
                    showConfirmation={false}
                  />
                </div>
              </div>
            )}

            {/* Loading State */}
            {(isCurrentlyCalculating || isCalculatingAlternatives) && (
              <div className="flex items-center justify-center p-6">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto mb-2"></div>
                  <div className="text-white/60 text-sm">
                    {isCalculatingAlternatives ? 'Finding alternative routes...' : 'Calculating optimal route...'}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </div>
      )}
    </div>
  );
}
