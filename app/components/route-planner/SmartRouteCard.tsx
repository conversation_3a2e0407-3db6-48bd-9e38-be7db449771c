import { useState } from 'react';
import { CardContent } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import {
  MapPin,
  Clock,
  Route,
  Car,
  Footprints,
  Bike,
  Bus,
  ChevronUp,
  ChevronDown,
  Settings,
  Zap
} from 'lucide-react';
import { useWanderlustStore } from '~/stores/wanderlust';
import { useRouteCalculation } from '~/hooks/useRouteCalculation';
import { useMapContext } from '~/components/maps/MapProvider';
import { WaypointManager } from './shared/WaypointManager';
import { ClearRouteButton } from './shared/ClearRouteButton';
import { RouteQualityIndicators } from './enhanced/RouteQualityIndicators';
import { ProgressiveLoadingStates } from './enhanced/ProgressiveLoadingStates';
import { cn } from '~/lib/utils';
import type { CityRegion } from '~/types/wanderlust';

interface SmartRouteCardProps {
  regions: CityRegion[];
}

export function SmartRouteCard({ regions }: SmartRouteCardProps) {
  const [builderMode, setBuilderMode] = useState<'building' | 'preview'>('building');
  const [disclosureLevel, setDisclosureLevel] = useState<'essential' | 'quick' | 'advanced'>('essential');

  const { itinerary } = useWanderlustStore();

  // Get route data from MapProvider (which uses useRoutesV2)
  const {
    currentRoute: mapCurrentRoute,
    alternativeRoutes: mapAlternativeRoutes,
    isCalculatingRoute,
    calculateRoute: mapCalculateRoute,
    clearRoute: mapClearRoute
  } = useMapContext();

  const {
    isCurrentlyCalculating,
    travelMode,
    currentRoute,
    isCalculationReady,
    travelModes,
    alternativeRoutes,
    handleTravelModeChange,
    handleClearRouteAndItinerary,
  } = useRouteCalculation();

  // Use map routes if available, otherwise fall back to store routes
  const activeRoute = mapCurrentRoute || currentRoute;
  const activeAlternatives = mapAlternativeRoutes || alternativeRoutes;
  const isCalculating = isCalculatingRoute || isCurrentlyCalculating;

  // Enhanced route calculation that switches to preview mode
  const handleCalculateRouteLocal = async () => {
    console.log('🚀 SmartRouteCard: Starting route calculation');

    if (itinerary.length < 2) {
      console.warn('❌ SmartRouteCard: Not enough waypoints');
      return;
    }

    try {
      // Convert itinerary to waypoints for Routes V2
      const waypoints = itinerary.map(place => ({
        location: {
          lat: place.coordinates.latitude,
          lng: place.coordinates.longitude,
        },
        stopover: true,
        placeId: place.id,
      }));

      console.log('🗺️ SmartRouteCard: Calling MapProvider calculateRoute with waypoints:', waypoints);

      // Use MapProvider's route calculation (which uses useRoutesV2)
      const result = await mapCalculateRoute(waypoints, travelMode, {
        includeAlternativeRoutes: true,
        maxAlternativeRoutes: 3,
        polylineQuality: 'HIGH_QUALITY',
        computeTollInfo: true,
        // Don't set routing preference - let transformer handle it based on travel mode
      });

      if (result) {
        setBuilderMode('preview');
        console.log('✅ SmartRouteCard: Route calculated successfully, switching to preview mode');
      }
    } catch (error) {
      console.error('❌ SmartRouteCard: Route calculation failed:', error);
    }
  };

  // Level 1: Essential Info (Always Visible)
  const renderEssentialInfo = () => (
    <div className="p-4">
      <div className="flex items-start justify-between gap-4">
        <div className="flex items-start space-x-3 flex-1 min-w-0">
          <Route className="h-5 w-5 text-yellow-400 mt-0.5 flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <h3 className="text-white font-medium text-base leading-6 mb-1">Route Builder</h3>
            {activeRoute && (
              <div className="flex flex-wrap items-center gap-x-4 gap-y-1 text-sm text-white/70">
                <span className="flex items-center whitespace-nowrap">
                  <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
                  <span className="truncate">{activeRoute.estimatedDistance}</span>
                </span>
                <span className="flex items-center whitespace-nowrap">
                  <Clock className="h-3 w-3 mr-1 flex-shrink-0" />
                  <span className="truncate">{activeRoute.estimatedDuration}</span>
                </span>
                <Badge className="bg-yellow-500 text-black text-xs whitespace-nowrap">
                  {itinerary.length} stop{itinerary.length !== 1 ? 's' : ''}
                </Badge>
              </div>
            )}
            {!activeRoute && itinerary.length > 0 && (
              <div className="text-sm text-white/70 leading-5">
                {itinerary.length} waypoint{itinerary.length !== 1 ? 's' : ''} added -
                <span className="text-yellow-400 ml-1">Calculate route to see details</span>
              </div>
            )}
            {!activeRoute && itinerary.length === 0 && (
              <div className="text-sm text-white/70 leading-5">
                Add waypoints to plan your route
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2 flex-shrink-0">
          {isCalculating && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-400"></div>
          )}

          {/* Contextual Action Button */}
          {isCalculationReady && disclosureLevel === 'essential' && (
            <Button
              size="sm"
              onClick={handleCalculateRouteLocal}
              disabled={isCalculating}
              className="bg-yellow-500 text-black hover:bg-yellow-400 font-medium text-xs px-3 py-1"
            >
              <Route className="h-3 w-3 mr-1" />
              Calculate
            </Button>
          )}

          {/* Disclosure Level Controls */}
          <div className="flex items-center space-x-1">
            {disclosureLevel !== 'advanced' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDisclosureLevel(disclosureLevel === 'essential' ? 'quick' : 'advanced')}
                className="text-yellow-400 hover:text-yellow-300 hover:bg-yellow-500/10 p-1"
              >
                {disclosureLevel === 'essential' ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <Settings className="h-4 w-4" />
                )}
              </Button>
            )}
            {disclosureLevel !== 'essential' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setDisclosureLevel(disclosureLevel === 'advanced' ? 'quick' : 'essential')}
                className="text-white/60 hover:text-white hover:bg-white/10 p-1"
              >
                <ChevronUp className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="border-b border-yellow-500/30 bg-black/95 backdrop-blur-sm">
      {/* Level 1: Essential Info - Always Visible */}
      {renderEssentialInfo()}

      {/* Level 2: Quick Actions (Expandable) */}
      {disclosureLevel !== 'essential' && (
        <div className="border-t border-white/10">
          <CardContent className="p-4 space-y-4">
            {/* Progressive Loading States */}
            {isCalculating && (
              <ProgressiveLoadingStates
                isCalculating={isCalculating}
                onCancel={() => {
                  // Handle cancellation if needed
                  console.log('Route calculation cancelled');
                }}
              />
            )}

            {/* Building Mode - Quick Actions */}
            {builderMode === 'building' && !isCalculating && (
              <div className="space-y-4">
                {/* Calculate Route Button - Prominent when ready */}
                {isCalculationReady && (
                  <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
                    <div className="text-center mb-3">
                      <h4 className="text-white font-medium text-sm mb-1">Ready to Calculate Route</h4>
                      <p className="text-white/70 text-xs">
                        {itinerary.length} waypoints added. Click below to calculate your route.
                      </p>
                    </div>
                    <Button
                      onClick={handleCalculateRouteLocal}
                      disabled={isCalculating}
                      className="w-full bg-yellow-500 text-black hover:bg-yellow-400 font-medium"
                    >
                      <Route className="h-4 w-4 mr-2" />
                      Calculate Route
                    </Button>
                  </div>
                )}

                {/* Waypoint Management - Compact */}
                <WaypointManager
                  regions={regions}
                  variant="compact"
                  showCategoryFilter={disclosureLevel === 'advanced'}
                  showCurrentLocationButton={true}
                  maxPlacesHeight="h-32"
                  maxWaypointsHeight="max-h-24"
                  enableReordering={true}
                  enableDragAndDrop={disclosureLevel === 'advanced'}
                  enableUpDownButtons={true}
                />
              </div>
            )}

            {/* Preview Mode - Route Quality & Quick Actions */}
            {builderMode === 'preview' && activeRoute && !isCalculating && (
              <div className="space-y-4">
                {/* Route Quality Indicators */}
                <RouteQualityIndicators
                  route={activeRoute}
                  alternativeRoutes={activeAlternatives}
                />

                {/* Travel Mode Selection - Compact */}
                <div>
                  <h4 className="text-sm font-medium text-white mb-2 flex items-center">
                    <Car className="h-4 w-4 mr-2 text-yellow-400" />
                    Travel Mode
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    {travelModes.slice(0, 4).map(({ mode, icon, label }) => {
                      const IconComponent = icon === 'Car' ? Car :
                                          icon === 'Footprints' ? Footprints :
                                          icon === 'Bike' ? Bike : Bus;
                      return (
                        <Button
                          key={mode}
                          size="sm"
                          variant={travelMode === mode ? "default" : "outline"}
                          onClick={() => handleTravelModeChange(mode)}
                          disabled={isCalculating}
                          className={cn(
                            'justify-start text-xs',
                            travelMode === mode
                              ? "bg-yellow-500 text-black hover:bg-yellow-400"
                              : "border-white/20 text-white hover:bg-white/5"
                          )}
                        >
                          <IconComponent className="h-3 w-3 mr-2" />
                          {label}
                        </Button>
                      );
                    })}
                  </div>
                </div>

                {/* Quick Recalculate */}
                <Button
                  onClick={handleCalculateRouteLocal}
                  disabled={isCalculating}
                  size="sm"
                  className="w-full bg-yellow-500/20 border border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/30"
                >
                  <Zap className="h-3 w-3 mr-2" />
                  Recalculate Route
                </Button>
              </div>
            )}

            {/* Clear Actions - Always available at Level 2+ */}
            {(itinerary.length > 0 || activeRoute) && (
              <div className="flex space-x-2 pt-2 border-t border-white/10">
                {activeRoute && (
                  <ClearRouteButton
                    variant="route-only"
                    onClearRoute={() => {
                      mapClearRoute();
                      setBuilderMode('building');
                    }}
                    size="sm"
                    className="flex-1"
                    showConfirmation={false}
                  />
                )}
                {itinerary.length > 0 && (
                  <ClearRouteButton
                    variant="route-and-waypoints"
                    onClearRouteAndItinerary={() => {
                      handleClearRouteAndItinerary();
                      setBuilderMode('building');
                    }}
                    size="sm"
                    className="flex-1"
                    showConfirmation={true}
                  />
                )}
              </div>
            )}
          </CardContent>
        </div>
      )}

      {/* Level 3: Advanced Options (On Demand) */}
      {disclosureLevel === 'advanced' && (
        <div className="border-t border-white/10">
          <CardContent className="p-4 space-y-4 max-h-[50vh] overflow-y-auto">
            {/* Advanced Waypoint Management */}
            {builderMode === 'building' && (
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Settings className="h-4 w-4 text-yellow-400" />
                  <h4 className="text-white font-medium text-sm">Advanced Options</h4>
                </div>

                {/* Full Waypoint Manager */}
                <WaypointManager
                  regions={regions}
                  variant="default"
                  showCategoryFilter={true}
                  showCurrentLocationButton={true}
                  maxPlacesHeight="h-48"
                  maxWaypointsHeight="max-h-40"
                  enableReordering={true}
                  enableDragAndDrop={true}
                  enableUpDownButtons={true}
                />
              </div>
            )}

            {/* Advanced Route Options */}
            {builderMode === 'preview' && activeRoute && (
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Settings className="h-4 w-4 text-yellow-400" />
                  <h4 className="text-white font-medium text-sm">Advanced Route Options</h4>
                </div>

                {/* All Travel Modes */}
                <div>
                  <h5 className="text-sm font-medium text-white mb-2">Travel Mode</h5>
                  <div className="grid grid-cols-2 gap-2">
                    {travelModes.map(({ mode, icon, label }) => {
                      const IconComponent = icon === 'Car' ? Car :
                                          icon === 'Footprints' ? Footprints :
                                          icon === 'Bike' ? Bike : Bus;
                      return (
                        <Button
                          key={mode}
                          size="sm"
                          variant={travelMode === mode ? "default" : "outline"}
                          onClick={() => handleTravelModeChange(mode)}
                          disabled={isCalculating}
                          className={cn(
                            'justify-start text-xs',
                            travelMode === mode
                              ? "bg-yellow-500 text-black hover:bg-yellow-400"
                              : "border-white/20 text-white hover:bg-white/5"
                          )}
                        >
                          <IconComponent className="h-3 w-3 mr-2" />
                          {label}
                        </Button>
                      );
                    })}
                  </div>
                </div>

                {/* Route Preferences */}
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-white">Route Preferences</h5>
                  <div className="grid grid-cols-1 gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="justify-start border-white/20 text-white hover:bg-white/5"
                    >
                      <Zap className="h-3 w-3 mr-2" />
                      Fastest Route
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="justify-start border-white/20 text-white hover:bg-white/5"
                    >
                      <MapPin className="h-3 w-3 mr-2" />
                      Shortest Distance
                    </Button>
                  </div>
                </div>

                {/* Edit Route Button */}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setBuilderMode('building')}
                  className="w-full border-white/20 text-white hover:bg-white/5"
                >
                  <Settings className="h-3 w-3 mr-2" />
                  Edit Route
                </Button>
              </div>
            )}
          </CardContent>
        </div>
      )}
    </div>
  );
}
