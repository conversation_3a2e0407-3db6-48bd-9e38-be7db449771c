/**
 * Lazy-Loaded Route Planner Components
 *
 * Implements React.lazy() code splitting for route planning components
 * to optimize bundle size and improve initial page load performance.
 *
 * Features:
 * - Progressive loading with route-specific skeletons
 * - Error boundaries with retry mechanisms
 * - Performance monitoring for route components
 * - Smart preloading based on user interactions
 */

import React, { Suspense, lazy, useEffect, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Skeleton } from '~/components/ui/skeleton';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader } from '~/components/ui/card';
import { Loader2, AlertTriangle, RefreshCw, Route, MapPin } from 'lucide-react';

// Lazy-loaded route planner components
const EnhancedRouteMap = lazy(() =>
  import('./EnhancedRouteMap').then(module => ({ default: module.EnhancedRouteMap }))
);

const EnhancedSmartRouteCard = lazy(() =>
  import('./EnhancedSmartRouteCard').then(module => ({ default: module.EnhancedSmartRouteCard }))
);

const RouteAlternatives = lazy(() =>
  import('./RouteAlternatives').then(module => ({ default: module.RouteAlternatives }))
);

const TurnByTurnDirections = lazy(() =>
  import('./TurnByTurnDirections').then(module => ({ default: module.TurnByTurnDirections }))
);

const WaypointPanel = lazy(() =>
  import('./WaypointPanel').then(module => ({ default: module.WaypointPanel }))
);

const RouteVisualizationTest = lazy(() =>
  import('./RouteVisualizationTest').then(module => ({ default: module.RouteVisualizationTest }))
);

// Route-specific skeleton components
const RouteMapSkeleton = () => (
  <div className="relative w-full h-full bg-gray-100 rounded-lg overflow-hidden">
    {/* Map skeleton */}
    <Skeleton className="w-full h-full" />

    {/* Route planning overlay */}
    <div className="absolute inset-0 flex items-center justify-center bg-black/5">
      <div className="bg-white rounded-lg p-6 shadow-lg flex flex-col items-center space-y-3 max-w-sm">
        <Route className="h-8 w-8 text-blue-600 animate-pulse" />
        <span className="text-sm font-medium text-gray-700">Loading route planner...</span>
        <div className="flex space-x-2">
          <Skeleton className="w-16 h-6 rounded" />
          <Skeleton className="w-20 h-6 rounded" />
          <Skeleton className="w-18 h-6 rounded" />
        </div>
      </div>
    </div>

    {/* Route controls skeleton */}
    <div className="absolute bottom-4 left-4 right-4">
      <Card>
        <CardHeader className="pb-2">
          <Skeleton className="w-32 h-5" />
        </CardHeader>
        <CardContent className="space-y-2">
          <Skeleton className="w-full h-8" />
          <div className="flex space-x-2">
            <Skeleton className="flex-1 h-6" />
            <Skeleton className="w-16 h-6" />
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
);

const RouteCardSkeleton = () => (
  <Card className="w-full">
    <CardHeader className="pb-3">
      <div className="flex items-center justify-between">
        <Skeleton className="w-24 h-6" />
        <Skeleton className="w-16 h-5" />
      </div>
    </CardHeader>
    <CardContent className="space-y-4">
      {/* Route summary skeleton */}
      <div className="flex items-center space-x-4">
        <Route className="h-5 w-5 text-gray-400" />
        <div className="flex-1 space-y-2">
          <Skeleton className="w-full h-4" />
          <Skeleton className="w-3/4 h-4" />
        </div>
      </div>

      {/* Waypoints skeleton */}
      <div className="space-y-2">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center space-x-3">
            <MapPin className="h-4 w-4 text-gray-400" />
            <Skeleton className="flex-1 h-4" />
            <Skeleton className="w-12 h-4" />
          </div>
        ))}
      </div>

      {/* Action buttons skeleton */}
      <div className="flex space-x-2 pt-2">
        <Skeleton className="flex-1 h-8" />
        <Skeleton className="w-20 h-8" />
      </div>
    </CardContent>
  </Card>
);

const DirectionsSkeleton = () => (
  <div className="space-y-3">
    <div className="flex items-center space-x-2 mb-4">
      <Route className="h-5 w-5 text-gray-400" />
      <Skeleton className="w-32 h-5" />
    </div>

    {[1, 2, 3, 4, 5].map((i) => (
      <div key={i} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
        <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center mt-0.5">
          <span className="text-xs text-gray-500">{i}</span>
        </div>
        <div className="flex-1 space-y-1">
          <Skeleton className="w-full h-4" />
          <Skeleton className="w-2/3 h-3" />
        </div>
        <Skeleton className="w-12 h-4" />
      </div>
    ))}
  </div>
);

const WaypointPanelSkeleton = () => (
  <div className="space-y-4">
    {/* Search skeleton */}
    <div className="space-y-2">
      <Skeleton className="w-20 h-4" />
      <Skeleton className="w-full h-10 rounded-lg" />
    </div>

    {/* Category filter skeleton */}
    <div className="flex space-x-2">
      {[1, 2, 3, 4].map((i) => (
        <Skeleton key={i} className="w-16 h-8 rounded-full" />
      ))}
    </div>

    {/* Places list skeleton */}
    <div className="space-y-2">
      {[1, 2, 3].map((i) => (
        <div key={i} className="flex items-center space-x-3 p-2 border rounded-lg">
          <Skeleton className="w-10 h-10 rounded" />
          <div className="flex-1 space-y-1">
            <Skeleton className="w-full h-4" />
            <Skeleton className="w-2/3 h-3" />
          </div>
          <Skeleton className="w-8 h-8 rounded" />
        </div>
      ))}
    </div>
  </div>
);

// Error fallback components
const RouteErrorFallback = ({ error, resetErrorBoundary }: {
  error: Error;
  resetErrorBoundary: () => void;
}) => (
  <Card className="w-full">
    <CardContent className="flex flex-col items-center justify-center p-8 text-center">
      <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        Route planner unavailable
      </h3>
      <p className="text-sm text-gray-600 mb-4 max-w-md">
        {error.message || 'Failed to load the route planning component. Please try again.'}
      </p>
      <Button
        onClick={resetErrorBoundary}
        variant="outline"
        size="sm"
        className="inline-flex items-center"
      >
        <RefreshCw className="h-4 w-4 mr-2" />
        Reload route planner
      </Button>
    </CardContent>
  </Card>
);

// Performance monitoring for route components
const useRouteComponentLoadTime = (componentName: string) => {
  const [loadTime, setLoadTime] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const startTime = performance.now();
    setIsLoading(true);

    const timer = setTimeout(() => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      setLoadTime(duration);
      setIsLoading(false);

      // Log route component performance
      if (process.env.NODE_ENV === 'development') {
        console.log(`Route ${componentName} load time: ${duration.toFixed(2)}ms`);
      }
    }, 100);

    return () => {
      clearTimeout(timer);
      setIsLoading(false);
    };
  }, [componentName]);

  return { loadTime, isLoading };
};

// Preloading strategies for route components
export const preloadRouteEssentials = () => {
  // Preload core route planning components
  import('./EnhancedRouteMap');
  import('./EnhancedSmartRouteCard');
  import('./WaypointPanel');
};

export const preloadRouteAdvanced = () => {
  // Preload advanced route features
  import('./RouteAlternatives');
  import('./TurnByTurnDirections');
  import('./RouteVisualizationTest');
};

// Lazy wrapper components with route-specific optimizations
export const LazyEnhancedRouteMap = React.forwardRef<
  HTMLDivElement,
  Omit<React.ComponentProps<typeof EnhancedRouteMap>, 'ref'>
>((props, ref) => {
  const { loadTime, isLoading } = useRouteComponentLoadTime('EnhancedRouteMap');

  return (
    <ErrorBoundary
      FallbackComponent={RouteErrorFallback}
      onError={(error) => {
        console.error('EnhancedRouteMap failed to load:', error);
      }}
    >
      <Suspense fallback={<RouteMapSkeleton />}>
        <div ref={ref}>
          <EnhancedRouteMap {...props} />
        </div>
      </Suspense>
    </ErrorBoundary>
  );
});

LazyEnhancedRouteMap.displayName = 'LazyEnhancedRouteMap';

export const LazyEnhancedSmartRouteCard = (props: any) => {
  const { loadTime, isLoading } = useRouteComponentLoadTime('EnhancedSmartRouteCard');

  return (
    <ErrorBoundary
      FallbackComponent={RouteErrorFallback}
      onError={(error) => {
        console.error('EnhancedSmartRouteCard failed to load:', error);
      }}
    >
      <Suspense fallback={<RouteCardSkeleton />}>
        <EnhancedSmartRouteCard {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

export const LazyRouteAlternatives = (props: Omit<React.ComponentProps<typeof RouteAlternatives>, 'ref'>) => {
  return (
    <ErrorBoundary
      FallbackComponent={RouteErrorFallback}
      onError={(error) => {
        console.error('RouteAlternatives failed to load:', error);
      }}
    >
      <Suspense fallback={<RouteCardSkeleton />}>
        <RouteAlternatives {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

export const LazyTurnByTurnDirections = (props: Omit<React.ComponentProps<typeof TurnByTurnDirections>, 'ref'>) => {
  return (
    <ErrorBoundary
      FallbackComponent={RouteErrorFallback}
      onError={(error) => {
        console.error('TurnByTurnDirections failed to load:', error);
      }}
    >
      <Suspense fallback={<DirectionsSkeleton />}>
        <TurnByTurnDirections {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

export const LazyWaypointPanel = (props: Omit<React.ComponentProps<typeof WaypointPanel>, 'ref'>) => {
  return (
    <ErrorBoundary
      FallbackComponent={RouteErrorFallback}
      onError={(error) => {
        console.error('WaypointPanel failed to load:', error);
      }}
    >
      <Suspense fallback={<WaypointPanelSkeleton />}>
        <WaypointPanel {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

export const LazyRouteVisualizationTest = (props: Omit<React.ComponentProps<typeof RouteVisualizationTest>, 'ref'>) => {
  return (
    <ErrorBoundary
      FallbackComponent={RouteErrorFallback}
      onError={(error) => {
        console.error('RouteVisualizationTest failed to load:', error);
      }}
    >
      <Suspense fallback={<RouteCardSkeleton />}>
        <RouteVisualizationTest {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};
