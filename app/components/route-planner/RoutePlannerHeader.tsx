import { Route, Share, Download, Settings } from 'lucide-react';
import { But<PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';

export function RoutePlannerHeader() {
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: 'FIFA Club World Cup 2025™ Route Plan',
        text: 'Check out my route plan for the FIFA Club World Cup 2025™',
        url: window.location.href,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const handleExport = () => {
    // TODO: Implement route export functionality
    console.log('Export route functionality to be implemented');
  };

  const handleSettings = () => {
    // TODO: Implement route settings
    console.log('Route settings functionality to be implemented');
  };

  return (
    <header className="relative bg-gradient-to-r from-black via-gray-900 to-black border-b border-[#FFD700]/30">
      {/* FIFA Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-r from-[#FFD700]/20 to-[#DC2626]/20"></div>
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <defs>
            <pattern id="fifa-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
              <circle cx="10" cy="10" r="1" fill="#FFD700" opacity="0.3" />
              <circle cx="5" cy="5" r="0.5" fill="#DC2626" opacity="0.2" />
              <circle cx="15" cy="15" r="0.5" fill="#FFD700" opacity="0.2" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#fifa-pattern)" />
        </svg>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-4 sm:py-6">
          {/* Left Section - Title and Context */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#FFD700] to-[#DC2626] rounded-xl shadow-lg">
              <Route className="h-5 w-5 sm:h-6 sm:w-6 text-black" />
            </div>
            <div>
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-white">
                Route Planner
              </h1>
              <div className="flex items-center space-x-2 mt-1">
                <Badge 
                  variant="outline" 
                  className="text-xs border-[#FFD700] text-[#FFD700] bg-[#FFD700]/10"
                >
                  FIFA Club World Cup 2025™
                </Badge>
                <span className="text-xs sm:text-sm text-white/60">
                  Plan your perfect route
                </span>
              </div>
            </div>
          </div>

          {/* Right Section - Action Buttons */}
          <div className="flex items-center space-x-2">
            {/* Share Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleShare}
              className="hidden sm:flex items-center space-x-2 text-white hover:bg-white/10 hover:text-[#FFD700] transition-all"
            >
              <Share className="h-4 w-4" />
              <span className="hidden lg:inline">Share</span>
            </Button>

            {/* Export Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleExport}
              className="hidden sm:flex items-center space-x-2 text-white hover:bg-white/10 hover:text-[#FFD700] transition-all"
            >
              <Download className="h-4 w-4" />
              <span className="hidden lg:inline">Export</span>
            </Button>

            {/* Settings Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSettings}
              className="flex items-center space-x-2 text-white hover:bg-white/10 hover:text-[#FFD700] transition-all"
            >
              <Settings className="h-4 w-4" />
              <span className="hidden lg:inline">Settings</span>
            </Button>

            {/* Mobile Action Menu */}
            <div className="sm:hidden">
              <Button
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/10"
              >
                <Share className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Quick Stats Bar */}
        <div className="pb-4 border-t border-white/10 pt-4">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-6 text-white/70">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Start Point</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-[#FFD700] rounded-full"></div>
                <span>Waypoints</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span>End Point</span>
              </div>
            </div>
            <div className="text-white/60 text-xs">
              Powered by Google Maps
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
