/**
 * Route Alternatives Component
 * 
 * Displays and manages alternative routes from Google Routes API v2
 * with detailed comparison metrics and selection capabilities.
 */

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Separator } from '~/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Progress } from '~/components/ui/progress';
import { useRouteCalculation } from '~/hooks/useRouteCalculation';
import type { TravelRoute } from '~/lib/types'; // Corrected import path
import {
  Route,
  Clock, 
  MapPin, 
  DollarSign, 
  Zap, 
  TrendingUp,
  TrendingDown,
  Minus,
  CheckCircle,
  Navigation,
  AlertTriangle,
  Info
} from 'lucide-react';

interface RouteAlternativesProps {
  className?: string;
  showComparison?: boolean;
  maxAlternatives?: number;
}

interface RouteMetrics {
  duration: number; // seconds
  distance: number; // meters
  estimatedCost?: number;
  trafficLevel?: 'LOW' | 'MEDIUM' | 'HIGH';
  ecoScore?: number; // 0-100
}

export function RouteAlternatives({ 
  className = '',
  showComparison = true,
  maxAlternatives = 3 
}: RouteAlternativesProps) {
  const [selectedTab, setSelectedTab] = useState('routes');
  
  const {
    alternativeRoutes,
    selectedAlternativeIndex,
    routeMetadata,
    handleSelectAlternative,
    isCalculatingAlternatives,
  } = useRouteCalculation();

  // Parse route metrics
  const parseRouteMetrics = (route: TravelRoute): RouteMetrics => {
    // Parse duration (e.g., "25 min" -> 1500 seconds)
    const durationMatch = route.estimatedDuration?.match(/(\d+)\s*(hr|min)/g);
    let duration = 0;
    if (durationMatch) {
      durationMatch.forEach(part => {
        const [, value, unit] = part.match(/(\d+)\s*(hr|min)/) || [];
        if (unit === 'hr') duration += parseInt(value) * 3600;
        if (unit === 'min') duration += parseInt(value) * 60;
      });
    }

    // Parse distance (e.g., "15.2 km" -> 15200 meters)
    const distanceMatch = route.estimatedDistance?.match(/(\d+\.?\d*)\s*(km|m)/);
    let distance = 0;
    if (distanceMatch) {
      const [, value, unit] = distanceMatch;
      distance = parseFloat(value) * (unit === 'km' ? 1000 : 1);
    }

    return {
      duration,
      distance,
      estimatedCost: route.estimatedCost,
      trafficLevel: route.trafficLevel,
      ecoScore: route.ecoScore,
    };
  };

  // Calculate comparison metrics
  const getComparisonMetrics = () => {
    if (alternativeRoutes.length === 0) return [];
    
    const metrics = alternativeRoutes.map(parseRouteMetrics);
    const baseline = metrics[0]; // First route as baseline
    
    return metrics.map((metric, index) => ({
      routeIndex: index,
      route: alternativeRoutes[index],
      metrics: metric,
      comparison: {
        durationDiff: metric.duration - baseline.duration,
        distanceDiff: metric.distance - baseline.distance,
        durationPercent: ((metric.duration - baseline.duration) / baseline.duration) * 100,
        distancePercent: ((metric.distance - baseline.distance) / baseline.distance) * 100,
      }
    }));
  };

  // Format duration
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  // Format distance
  const formatDistance = (meters: number): string => {
    if (meters >= 1000) return `${(meters / 1000).toFixed(1)} km`;
    return `${meters} m`;
  };

  // Get comparison icon
  const getComparisonIcon = (value: number) => {
    if (value > 5) return <TrendingUp className="h-4 w-4 text-red-500" />;
    if (value < -5) return <TrendingDown className="h-4 w-4 text-green-500" />;
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  // Get traffic level color
  const getTrafficColor = (level: string) => {
    switch (level) {
      case 'LOW': return 'text-green-600 bg-green-100';
      case 'MEDIUM': return 'text-yellow-600 bg-yellow-100';
      case 'HIGH': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const comparisonData = getComparisonMetrics();

  if (alternativeRoutes.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="py-8 text-center">
          <Route className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500">No alternative routes available</p>
          <p className="text-sm text-gray-400 mt-1">
            Calculate a route to see alternatives
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <Route className="h-5 w-5" />
          Route Alternatives
          <Badge variant="outline" className="ml-auto">
            {alternativeRoutes.length} routes
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="routes">Routes</TabsTrigger>
            {showComparison && (
              <TabsTrigger value="comparison">Compare</TabsTrigger>
            )}
          </TabsList>
          
          <TabsContent value="routes" className="space-y-4 mt-4">
            {alternativeRoutes.map((route, index) => {
              const metrics = parseRouteMetrics(route);
              const isSelected = selectedAlternativeIndex === index;
              
              return (
                <Card 
                  key={route.id}
                  className={`cursor-pointer transition-all ${
                    isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleSelectAlternative(index)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-medium ${
                          isSelected ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-600'
                        }`}>
                          {index + 1}
                        </div>
                        <span className="font-medium">Route {index + 1}</span>
                        {isSelected && (
                          <CheckCircle className="h-4 w-4 text-blue-500" />
                        )}
                      </div>
                      
                      <Badge 
                        variant="outline" 
                        className={getTrafficColor(metrics.trafficLevel || 'MEDIUM')}
                      >
                        {metrics.trafficLevel} Traffic
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 mb-3">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{route.estimatedDuration}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{route.estimatedDistance}</span>
                      </div>
                    </div>
                    
                    {/* Route Quality Indicators */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-600">Eco Score</span>
                        <span className="font-medium">{metrics.ecoScore}%</span>
                      </div>
                      <Progress value={metrics.ecoScore} className="h-1" />
                    </div>
                    
                    {route.warnings && route.warnings.length > 0 && (
                      <div className="mt-3 flex items-center gap-2 text-xs text-amber-600">
                        <AlertTriangle className="h-3 w-3" />
                        <span>{route.warnings[0]}</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>
          
          {showComparison && (
            <TabsContent value="comparison" className="space-y-4 mt-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Info className="h-4 w-4" />
                  Comparison vs. Route 1 (baseline)
                </div>
                
                {comparisonData.map(({ routeIndex, route, metrics, comparison }) => (
                  <Card key={route.id} className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Route {routeIndex + 1}</span>
                        {routeIndex === selectedAlternativeIndex && (
                          <CheckCircle className="h-4 w-4 text-blue-500" />
                        )}
                      </div>
                      
                      {routeIndex === 0 && (
                        <Badge variant="outline">Baseline</Badge>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      {/* Duration Comparison */}
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Duration</span>
                          <div className="flex items-center gap-1">
                            {getComparisonIcon(comparison.durationPercent)}
                            <span className={`text-xs ${
                              comparison.durationPercent > 5 ? 'text-red-600' :
                              comparison.durationPercent < -5 ? 'text-green-600' :
                              'text-gray-600'
                            }`}>
                              {comparison.durationPercent > 0 ? '+' : ''}
                              {comparison.durationPercent.toFixed(0)}%
                            </span>
                          </div>
                        </div>
                        <div className="text-sm font-medium">
                          {formatDuration(metrics.duration)}
                        </div>
                      </div>
                      
                      {/* Distance Comparison */}
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Distance</span>
                          <div className="flex items-center gap-1">
                            {getComparisonIcon(comparison.distancePercent)}
                            <span className={`text-xs ${
                              comparison.distancePercent > 5 ? 'text-red-600' :
                              comparison.distancePercent < -5 ? 'text-green-600' :
                              'text-gray-600'
                            }`}>
                              {comparison.distancePercent > 0 ? '+' : ''}
                              {comparison.distancePercent.toFixed(0)}%
                            </span>
                          </div>
                        </div>
                        <div className="text-sm font-medium">
                          {formatDistance(metrics.distance)}
                        </div>
                      </div>
                    </div>
                    
                    {routeIndex !== selectedAlternativeIndex && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSelectAlternative(routeIndex)}
                        className="w-full mt-3"
                      >
                        <Navigation className="h-4 w-4 mr-2" />
                        Select This Route
                      </Button>
                    )}
                  </Card>
                ))}
              </div>
            </TabsContent>
          )}
        </Tabs>
        
        {/* API Information */}
        {routeMetadata && (
          <>
            <Separator className="my-4" />
            <div className="text-xs text-gray-500 text-center">
              Powered by Google Routes API {routeMetadata.apiVersion.toUpperCase()} • 
              Calculated in {routeMetadata.calculationTime.toFixed(0)}ms
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
