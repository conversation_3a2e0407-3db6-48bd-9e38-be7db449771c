import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { useWanderlustStore } from '~/stores/wanderlust';
import { useRouteCalculation } from '~/hooks/useRouteCalculation';
import { ClearRouteButton } from './shared/ClearRouteButton';
import { showSuccess, showInfo } from '~/components/wanderlust/NotificationSystem';
import type { VisitedPlace } from '~/types/wanderlust';

// Test waypoints for route visualization testing
const testWaypoints: VisitedPlace[] = [
  {
    id: 'test-1',
    name: '<PERSON><PERSON><PERSON>',
    description: { en: 'Tallest building in the world', fr: 'Plus haut bâtiment du monde', ar: 'أطول مبنى في العالم' },
    category: 'landmark',
    coordinates: { latitude: 25.1972, longitude: 55.2744 },
    city: 'Dubai',
    region: 'Downtown Dubai',
    revisitPotential: 'Highly Recommend',
    icon: '🏗️'
  },
  {
    id: 'test-2',
    name: 'Dubai Mall',
    description: { en: 'Largest shopping mall', fr: 'Plus grand centre commercial', ar: 'أكبر مركز تسوق' },
    category: 'shopping',
    coordinates: { latitude: 25.1975, longitude: 55.2796 },
    city: 'Dubai',
    region: 'Downtown Dubai',
    revisitPotential: 'Worth a Look',
    icon: '🛍️'
  },
  {
    id: 'test-3',
    name: 'Dubai Fountain',
    description: { en: 'Musical fountain show', fr: 'Spectacle de fontaine musicale', ar: 'عرض النافورة الموسيقية' },
    category: 'entertainment',
    coordinates: { latitude: 25.1951, longitude: 55.2744 },
    city: 'Dubai',
    region: 'Downtown Dubai',
    revisitPotential: 'Highly Recommend',
    icon: '⛲'
  }
];

export function RouteVisualizationTest() {
  const [testCount, setTestCount] = useState(0);

  const {
    itinerary,
    currentRoute,
    addToItinerary,
    clearItinerary,
    routePlanning
  } = useWanderlustStore();

  const {
    isCurrentlyCalculating,
    handleCalculateRoute,
    handleClearRoute,
    handleClearRouteAndItinerary,
  } = useRouteCalculation();

  const addTestWaypoints = () => {
    testWaypoints.forEach(waypoint => {
      addToItinerary(waypoint);
    });
    showSuccess('Test waypoints added');
  };

  const testRouteCalculation = async () => {
    setTestCount(prev => prev + 1);
    showInfo(`Starting route calculation test #${testCount + 1}`);
    await handleCalculateRoute();
  };

  const testMultipleRecalculations = async () => {
    for (let i = 1; i <= 3; i++) {
      showInfo(`Route recalculation test ${i}/3 - This should clear previous route`);
      await handleCalculateRoute();
      await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds between calculations
    }
    showSuccess('Multiple recalculation test completed - Check console for clearing logs');
  };

  const testClearingSequence = async () => {
    showInfo('Testing clearing sequence...');

    // Calculate route
    await handleCalculateRoute();
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Clear route only
    showInfo('Clearing route only (keeping waypoints)');
    handleClearRoute();
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Recalculate
    showInfo('Recalculating route');
    await handleCalculateRoute();
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Clear everything
    showInfo('Clearing route and waypoints');
    handleClearRouteAndItinerary();

    showSuccess('Clearing sequence test completed - Check console for detailed logs');
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card className="bg-black/95 border-yellow-500/30">
        <CardHeader>
          <CardTitle className="text-white">Route Visualization Test Suite</CardTitle>
          <p className="text-white/70 text-sm">
            Test route calculation, clearing, and visualization updates
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Test Status */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/5 rounded-lg p-3 border border-white/10">
              <h4 className="text-white font-medium text-sm mb-1">Waypoints</h4>
              <p className="text-white/70 text-xs">{itinerary.length} added</p>
            </div>
            <div className="bg-white/5 rounded-lg p-3 border border-white/10">
              <h4 className="text-white font-medium text-sm mb-1">Route Status</h4>
              <p className="text-white/70 text-xs">
                {currentRoute ? 'Active' : 'None'}
                {isCurrentlyCalculating && ' (Calculating...)'}
              </p>
            </div>
            <div className="bg-white/5 rounded-lg p-3 border border-white/10">
              <h4 className="text-white font-medium text-sm mb-1">Tests Run</h4>
              <p className="text-white/70 text-xs">{testCount} calculations</p>
            </div>
          </div>

          {/* Test Controls */}
          <div className="space-y-4">
            <h3 className="text-white font-medium">Test Controls</h3>

            {/* Setup Tests */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Button
                onClick={addTestWaypoints}
                disabled={itinerary.length >= 3}
                variant="outline"
                className="border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10"
              >
                Add Test Waypoints
              </Button>
              <Button
                onClick={clearItinerary}
                disabled={itinerary.length === 0}
                variant="outline"
                className="border-red-500/30 text-red-400 hover:bg-red-500/10"
              >
                Clear Waypoints
              </Button>
            </div>

            {/* Route Tests */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <Button
                onClick={testRouteCalculation}
                disabled={itinerary.length < 2 || isCurrentlyCalculating}
                className="bg-yellow-500 text-black hover:bg-yellow-400"
              >
                {isCurrentlyCalculating ? 'Calculating...' : 'Test Route Calculation'}
              </Button>
              <Button
                onClick={testMultipleRecalculations}
                disabled={itinerary.length < 2 || isCurrentlyCalculating}
                variant="outline"
                className="border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10"
              >
                Test Multiple Recalculations
              </Button>
              <Button
                onClick={testClearingSequence}
                disabled={itinerary.length < 2 || isCurrentlyCalculating}
                variant="outline"
                className="border-blue-500/30 text-blue-400 hover:bg-blue-500/10"
              >
                Test Clearing Sequence
              </Button>
            </div>

            {/* Clear Tests */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <ClearRouteButton
                variant="route-only"
                onClearRoute={handleClearRoute}
                disabled={!currentRoute}
                showConfirmation={false}
                className="w-full"
              />
              <ClearRouteButton
                variant="route-and-waypoints"
                onClearRouteAndItinerary={handleClearRouteAndItinerary}
                disabled={itinerary.length === 0 && !currentRoute}
                showConfirmation={true}
                className="w-full"
              />
            </div>
          </div>

          {/* Current State Display */}
          {(itinerary.length > 0 || currentRoute) && (
            <div className="space-y-3">
              <h3 className="text-white font-medium">Current State</h3>

              {/* Waypoints */}
              {itinerary.length > 0 && (
                <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                  <h4 className="text-white font-medium text-sm mb-2">Waypoints ({itinerary.length})</h4>
                  <div className="space-y-1">
                    {itinerary.map((waypoint, index) => (
                      <div key={waypoint.id} className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {index + 1}
                        </Badge>
                        <span className="text-white/70 text-xs">{waypoint.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Route Info */}
              {currentRoute && (
                <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                  <h4 className="text-white font-medium text-sm mb-2">Active Route</h4>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <span className="text-white/60">Distance:</span>
                      <span className="text-white ml-1">{currentRoute.estimatedDistance || 'N/A'}</span>
                    </div>
                    <div>
                      <span className="text-white/60">Duration:</span>
                      <span className="text-white ml-1">{currentRoute.estimatedDuration || 'N/A'}</span>
                    </div>
                    <div>
                      <span className="text-white/60">Mode:</span>
                      <span className="text-white ml-1">{currentRoute.travelMode}</span>
                    </div>
                    <div>
                      <span className="text-white/60">Waypoints:</span>
                      <span className="text-white ml-1">{currentRoute.waypoints.length}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Planning State */}
              {routePlanning.isPlanning && (
                <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-400"></div>
                    <span className="text-yellow-400 text-sm">Route calculation in progress...</span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Instructions */}
          <div className="bg-white/5 rounded-lg p-3 border border-white/10">
            <h4 className="text-white font-medium text-xs mb-2">Test Instructions:</h4>
            <ul className="text-white/70 text-xs space-y-1">
              <li>1. <strong>Add test waypoints</strong> to create a route</li>
              <li>2. <strong>Calculate route</strong> and observe map visualization</li>
              <li>3. <strong>Test multiple recalculations</strong> to verify proper clearing</li>
              <li>4. <strong>Test clearing sequence</strong> for comprehensive clearing test</li>
              <li>5. <strong>Use clear buttons</strong> to test route removal</li>
              <li>6. <strong>Check browser console</strong> for detailed clearing logs</li>
              <li>7. <strong>Verify no overlapping polylines</strong> appear on the map</li>
            </ul>
          </div>

          {/* Debug Console Info */}
          <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3">
            <h4 className="text-blue-400 font-medium text-xs mb-2">🔍 Debug Information:</h4>
            <p className="text-blue-300/70 text-xs">
              Open browser console (F12) to see detailed logs about route clearing,
              map object cleanup, and store state changes. Look for 🧹, ✅, and ❌ emojis
              to track the clearing process.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
