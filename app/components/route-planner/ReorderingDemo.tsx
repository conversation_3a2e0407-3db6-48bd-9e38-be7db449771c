import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { SortableWaypointItem } from './shared/SortableWaypointItem';
import { showSuccess } from '~/components/wanderlust/NotificationSystem';
import type { VisitedPlace } from '~/types/wanderlust';

// Demo waypoints for testing
const demoWaypoints: VisitedPlace[] = [
  {
    id: 'demo-1',
    name: 'Bur<PERSON>',
    description: { en: 'Tallest building in the world', fr: 'Plus haut bâtiment du monde', ar: 'أطول مبنى في العالم' },
    category: 'landmark',
    coordinates: { latitude: 25.1972, longitude: 55.2744 },
    city: 'Dubai',
    region: 'Downtown Dubai',
    revisitPotential: 'Highly Recommend',
    icon: '🏗️'
  },
  {
    id: 'demo-2',
    name: 'Dubai Mall',
    description: { en: 'Largest shopping mall', fr: 'Plus grand centre commercial', ar: 'أكبر مركز تسوق' },
    category: 'shopping',
    coordinates: { latitude: 25.1975, longitude: 55.2796 },
    city: 'Dubai',
    region: 'Downtown Dubai',
    revisitPotential: 'Worth a Look',
    icon: '🛍️'
  },
  {
    id: 'demo-3',
    name: 'Dubai Fountain',
    description: { en: 'Musical fountain show', fr: 'Spectacle de fontaine musicale', ar: 'عرض النافورة الموسيقية' },
    category: 'entertainment',
    coordinates: { latitude: 25.1951, longitude: 55.2744 },
    city: 'Dubai',
    region: 'Downtown Dubai',
    revisitPotential: 'Highly Recommend',
    icon: '⛲'
  },
  {
    id: 'demo-4',
    name: 'Gold Souk',
    description: { en: 'Traditional gold market', fr: 'Marché de l\'or traditionnel', ar: 'سوق الذهب التقليدي' },
    category: 'shopping',
    coordinates: { latitude: 25.2677, longitude: 55.2962 },
    city: 'Dubai',
    region: 'Deira',
    revisitPotential: 'Worth a Look',
    icon: '🏪'
  }
];

export function ReorderingDemo() {
  const [waypoints, setWaypoints] = useState<VisitedPlace[]>(demoWaypoints);
  const [enableDragAndDrop, setEnableDragAndDrop] = useState(true);
  const [enableUpDownButtons, setEnableUpDownButtons] = useState(true);

  const handleRemoveWaypoint = (index: number) => {
    const removed = waypoints[index];
    setWaypoints(prev => prev.filter((_, i) => i !== index));
    showSuccess(`${removed.name} removed from route`);
  };

  const handleReorderWaypoint = (fromIndex: number, toIndex: number) => {
    setWaypoints(prev => {
      const newWaypoints = [...prev];
      const [removed] = newWaypoints.splice(fromIndex, 1);
      newWaypoints.splice(toIndex, 0, removed);
      return newWaypoints;
    });
    showSuccess('Waypoint order updated');
  };

  const resetWaypoints = () => {
    setWaypoints(demoWaypoints);
    showSuccess('Waypoints reset to original order');
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card className="bg-black/95 border-yellow-500/30">
        <CardHeader>
          <CardTitle className="text-white">Waypoint Reordering Demo</CardTitle>
          <p className="text-white/70 text-sm">
            Test drag-and-drop and up/down button reordering functionality
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Controls */}
          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setEnableDragAndDrop(!enableDragAndDrop)}
              className={`border-yellow-500/30 ${
                enableDragAndDrop 
                  ? 'bg-yellow-500 text-black' 
                  : 'text-yellow-400 hover:bg-yellow-500/10'
              }`}
            >
              Drag & Drop: {enableDragAndDrop ? 'ON' : 'OFF'}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setEnableUpDownButtons(!enableUpDownButtons)}
              className={`border-yellow-500/30 ${
                enableUpDownButtons 
                  ? 'bg-yellow-500 text-black' 
                  : 'text-yellow-400 hover:bg-yellow-500/10'
              }`}
            >
              Up/Down Buttons: {enableUpDownButtons ? 'ON' : 'OFF'}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={resetWaypoints}
              className="border-white/20 text-white hover:bg-white/5"
            >
              Reset Order
            </Button>
          </div>

          {/* Waypoint List */}
          <div className="space-y-2">
            <h3 className="text-white font-medium text-sm">
              Route ({waypoints.length} stops)
            </h3>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {waypoints.map((waypoint, index) => (
                <SortableWaypointItem
                  key={waypoint.id}
                  waypoint={waypoint}
                  index={index}
                  totalItems={waypoints.length}
                  onRemove={handleRemoveWaypoint}
                  onReorder={handleReorderWaypoint}
                  showNumbers={true}
                  variant="default"
                  enableDragAndDrop={enableDragAndDrop}
                  enableUpDownButtons={enableUpDownButtons}
                />
              ))}
            </div>
          </div>

          {/* Instructions */}
          <div className="bg-white/5 rounded-lg p-3 border border-white/10">
            <h4 className="text-white font-medium text-xs mb-2">Instructions:</h4>
            <ul className="text-white/70 text-xs space-y-1">
              <li>• <strong>Drag & Drop:</strong> Click and drag the grip handle to reorder</li>
              <li>• <strong>Up/Down Buttons:</strong> Use the chevron buttons to move items</li>
              <li>• <strong>Remove:</strong> Click the X button to remove waypoints</li>
              <li>• Toggle controls above to test different interaction modes</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
