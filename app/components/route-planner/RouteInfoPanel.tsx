import { useState } from 'react';
import {
  X,
  Navigation,
  Clock,
  MapPin,
  Route as RouteIcon,
  Car,
  Footprints,
  Bike,
  Bus,
  Zap,
  RotateCcw,
  ArrowUp,
  AlertTriangle,
  Info,
  AlertCircle
} from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { Switch } from '~/components/ui/switch';
import { useWanderlustStore } from '~/stores/wanderlust';

export function RouteInfoPanel() {
  const [activeTab, setActiveTab] = useState('overview');

  const {
    currentRoute,
    routePlanning,
    itinerary,
    setShowDirections,
    setCurrentRoute,
    optimizeCurrentRoute,
    updateOptimizationOptions,
    calculateRouteFromItinerary,
  } = useWanderlustStore();

  const handleClose = () => {
    setShowDirections(false);
    setCurrentRoute(null);
  };

  const handleOptimizeRoute = async () => {
    if (currentRoute) {
      await optimizeCurrentRoute();
    }
  };

  const handleRecalculateRoute = async () => {
    await calculateRouteFromItinerary();
  };

  const getTravelModeIcon = (mode: string) => {
    switch (mode) {
      case 'DRIVING': return <Car className="h-4 w-4" />;
      case 'WALKING': return <Footprints className="h-4 w-4" />;
      case 'BICYCLING': return <Bike className="h-4 w-4" />;
      case 'TRANSIT': return <Bus className="h-4 w-4" />;
      default: return <Navigation className="h-4 w-4" />;
    }
  };

  const formatTravelMode = (mode: string) => {
    switch (mode) {
      case 'DRIVING': return 'Driving';
      case 'WALKING': return 'Walking';
      case 'BICYCLING': return 'Cycling';
      case 'TRANSIT': return 'Transit';
      default: return mode;
    }
  };

  return (
    <>
      {/* Desktop Floating Panel */}
      <div className="hidden lg:block fixed bottom-4 right-4 w-96 max-h-[60vh] z-40">
        <Card className="shadow-2xl border-2 border-[#FFD700]/30 bg-black/90 backdrop-blur-xl">
          <CardHeader className="pb-3 bg-gradient-to-r from-[#FFD700]/20 to-[#DC2626]/20 border-b border-[#FFD700]/30">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center text-white">
                <RouteIcon className="h-5 w-5 mr-2 text-[#FFD700]" />
                Route Information
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="h-8 w-8 p-0 text-white hover:bg-white/10"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3 bg-black/50">
                <TabsTrigger value="overview" className="text-white data-[state=active]:bg-[#FFD700] data-[state=active]:text-black">
                  Overview
                </TabsTrigger>
                <TabsTrigger value="details" className="text-white data-[state=active]:bg-[#FFD700] data-[state=active]:text-black">
                  Details
                </TabsTrigger>
                <TabsTrigger value="settings" className="text-white data-[state=active]:bg-[#FFD700] data-[state=active]:text-black">
                  Settings
                </TabsTrigger>
              </TabsList>

              <div className="p-4 max-h-[40vh] overflow-y-auto">
                <TabsContent value="overview" className="mt-0 space-y-4">
                  {/* Route Summary */}
                  {currentRoute ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-white/5 rounded-lg p-3 border border-[#FFD700]/20">
                          <div className="flex items-center text-[#FFD700] mb-1">
                            <MapPin className="h-4 w-4 mr-1" />
                            <span className="text-xs font-medium">Distance</span>
                          </div>
                          <div className="text-white font-bold">
                            {currentRoute.estimatedDistance || 'Calculating...'}
                          </div>
                        </div>
                        <div className="bg-white/5 rounded-lg p-3 border border-[#FFD700]/20">
                          <div className="flex items-center text-[#FFD700] mb-1">
                            <Clock className="h-4 w-4 mr-1" />
                            <span className="text-xs font-medium">Duration</span>
                          </div>
                          <div className="text-white font-bold">
                            {currentRoute.estimatedDuration || 'Calculating...'}
                          </div>
                        </div>
                      </div>

                      <div className="bg-white/5 rounded-lg p-3 border border-[#FFD700]/20">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center text-[#FFD700]">
                            {getTravelModeIcon(currentRoute.travelMode)}
                            <span className="text-sm font-medium ml-1">Travel Mode</span>
                          </div>
                          <Badge variant="outline" className="border-[#FFD700] text-[#FFD700]">
                            {formatTravelMode(currentRoute.travelMode)}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-white/60">Route Optimized</span>
                          <Badge
                            variant={currentRoute.optimized ? "default" : "outline"}
                            className={currentRoute.optimized ? "bg-green-500 text-white" : "border-yellow-500 text-yellow-500"}
                          >
                            {currentRoute.optimized ? 'Yes' : 'No'}
                          </Badge>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="space-y-2">
                        <Button
                          onClick={handleOptimizeRoute}
                          disabled={routePlanning.isOptimizing || currentRoute.optimized}
                          className="w-full bg-gradient-to-r from-[#FFD700] to-[#DC2626] text-black hover:from-[#FFF700] hover:to-[#EF4444] font-bold"
                        >
                          {routePlanning.isOptimizing ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2" />
                              Optimizing...
                            </>
                          ) : (
                            <>
                              <Zap className="h-4 w-4 mr-2" />
                              Optimize Route
                            </>
                          )}
                        </Button>

                        <Button
                          variant="outline"
                          onClick={handleRecalculateRoute}
                          disabled={routePlanning.isPlanning}
                          className="w-full border-[#FFD700]/30 text-white hover:bg-[#FFD700]/10"
                        >
                          <RotateCcw className="h-4 w-4 mr-2" />
                          Recalculate
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      {routePlanning.isPlanning ? (
                        <div>
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#FFD700] mx-auto mb-4" />
                          <p className="text-white">Calculating route...</p>
                          <p className="text-white/60 text-sm mt-1">This may take a few moments</p>
                        </div>
                      ) : (
                        <div>
                          <RouteIcon className="h-12 w-12 text-[#FFD700] mx-auto mb-4" />
                          <p className="text-white">No route calculated yet</p>
                          <p className="text-white/60 text-sm mt-1">Add waypoints and calculate a route</p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Error Display */}
                  {routePlanning.planningError && (
                    <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
                      <p className="text-red-300 text-sm">{routePlanning.planningError}</p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="details" className="mt-0 space-y-4">
                  {/* Route Summary */}
                  {currentRoute && (
                    <div className="bg-white/5 rounded-lg p-3 border border-[#FFD700]/20">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-medium text-white flex items-center">
                          <RouteIcon className="h-4 w-4 mr-2 text-[#FFD700]" />
                          Route Summary
                        </h4>
                        <Badge variant="outline" className="border-[#FFD700] text-[#FFD700] text-xs">
                          {formatTravelMode(currentRoute.travelMode)}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-3 text-xs">
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-3 w-3 text-[#FFD700]" />
                          <span className="text-white/60">Total Distance:</span>
                          <span className="text-white font-medium">{currentRoute.estimatedDistance}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="h-3 w-3 text-[#FFD700]" />
                          <span className="text-white/60">Est. Time:</span>
                          <span className="text-white font-medium">{currentRoute.estimatedDuration}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Navigation className="h-3 w-3 text-[#FFD700]" />
                          <span className="text-white/60">Waypoints:</span>
                          <span className="text-white font-medium">{itinerary.length}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Zap className="h-3 w-3 text-[#FFD700]" />
                          <span className="text-white/60">Optimized:</span>
                          <span className={`font-medium ${currentRoute.optimized ? 'text-green-400' : 'text-yellow-400'}`}>
                            {currentRoute.optimized ? 'Yes' : 'No'}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Route Legs with Enhanced Details */}
                  {currentRoute?.legs && currentRoute.legs.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-white mb-3 flex items-center">
                        <MapPin className="h-4 w-4 mr-2 text-[#FFD700]" />
                        Route Segments ({currentRoute.legs.length})
                      </h4>
                      <div className="space-y-3 max-h-60 overflow-y-auto">
                        {currentRoute.legs.map((leg, index) => (
                          <div key={index} className="bg-white/5 rounded-lg p-3 border border-white/10">
                            <div className="flex justify-between items-start mb-2">
                              <div className="flex items-center space-x-2">
                                <div className="w-5 h-5 rounded-full bg-[#FFD700] text-black text-xs font-bold flex items-center justify-center">
                                  {index + 1}
                                </div>
                                <span className="text-sm font-medium text-white">
                                  {itinerary[index]?.name} → {itinerary[index + 1]?.name}
                                </span>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-2 text-xs mb-2">
                              <div className="flex items-center space-x-1">
                                <MapPin className="h-3 w-3 text-[#FFD700]" />
                                <span className="text-white/60">Distance:</span>
                                <span className="text-white font-medium">{leg.distance.text}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3 text-[#FFD700]" />
                                <span className="text-white/60">Duration:</span>
                                <span className="text-white font-medium">{leg.duration.text}</span>
                              </div>
                            </div>

                            {/* Turn-by-turn directions preview */}
                            {leg.steps && leg.steps.length > 0 && (
                              <div className="mt-2">
                                <div className="text-xs text-white/60 mb-1">
                                  {leg.steps.length} step{leg.steps.length !== 1 ? 's' : ''}
                                </div>
                                <div className="space-y-1 max-h-20 overflow-y-auto">
                                  {leg.steps.slice(0, 3).map((step, stepIndex) => (
                                    <div key={stepIndex} className="flex items-start space-x-2 text-xs">
                                      <div className="w-4 h-4 rounded-full bg-white/10 flex items-center justify-center mt-0.5">
                                        <ArrowUp className="h-2 w-2 text-white/60" />
                                      </div>
                                      <div className="flex-1 text-white/70 leading-tight">
                                        <div dangerouslySetInnerHTML={{
                                          __html: step.html_instructions.replace(/<[^>]*>/g, '').substring(0, 50) + '...'
                                        }} />
                                        <div className="text-white/50 text-xs">
                                          {step.distance.text} • {step.duration.text}
                                        </div>
                                      </div>
                                    </div>
                                  ))}
                                  {leg.steps.length > 3 && (
                                    <div className="text-xs text-white/50 text-center py-1">
                                      +{leg.steps.length - 3} more steps
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Route Alternatives */}
                  {routePlanning.alternativeRoutes && routePlanning.alternativeRoutes.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-white mb-3 flex items-center">
                        <Navigation className="h-4 w-4 mr-2 text-[#FFD700]" />
                        Alternative Routes ({routePlanning.alternativeRoutes.length})
                      </h4>
                      <div className="space-y-2">
                        {routePlanning.alternativeRoutes.map((route, index) => (
                          <div
                            key={index}
                            className={`p-3 rounded-lg border cursor-pointer transition-all ${
                              routePlanning.selectedRouteIndex === index
                                ? 'bg-[#FFD700]/10 border-[#FFD700]'
                                : 'bg-white/5 border-white/10 hover:border-[#FFD700]/50'
                            }`}
                          >
                            <div className="flex justify-between items-center">
                              <div className="flex items-center space-x-2">
                                <Badge variant="outline" className="text-xs">
                                  Route {index + 1}
                                </Badge>
                                {index === routePlanning.selectedRouteIndex && (
                                  <Badge className="text-xs bg-[#FFD700] text-black">Current</Badge>
                                )}
                              </div>
                              <div className="text-xs text-white/60">
                                {route.estimatedDistance} • {route.estimatedDuration}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Route Warnings & Advisories */}
                  <div>
                    <h4 className="text-sm font-medium text-white mb-3 flex items-center">
                      <AlertTriangle className="h-4 w-4 mr-2 text-yellow-500" />
                      Route Information
                    </h4>
                    <div className="space-y-2">
                      {routePlanning.optimizationOptions.avoidTolls && (
                        <div className="flex items-center space-x-2 p-2 bg-blue-500/10 rounded-lg border border-blue-500/20">
                          <Info className="h-4 w-4 text-blue-400" />
                          <span className="text-xs text-blue-300">Route avoids toll roads</span>
                        </div>
                      )}
                      {routePlanning.optimizationOptions.avoidHighways && (
                        <div className="flex items-center space-x-2 p-2 bg-blue-500/10 rounded-lg border border-blue-500/20">
                          <Info className="h-4 w-4 text-blue-400" />
                          <span className="text-xs text-blue-300">Route avoids highways</span>
                        </div>
                      )}
                      {routePlanning.optimizationOptions.avoidFerries && (
                        <div className="flex items-center space-x-2 p-2 bg-blue-500/10 rounded-lg border border-blue-500/20">
                          <Info className="h-4 w-4 text-blue-400" />
                          <span className="text-xs text-blue-300">Route avoids ferries</span>
                        </div>
                      )}
                      {currentRoute?.optimized && (
                        <div className="flex items-center space-x-2 p-2 bg-green-500/10 rounded-lg border border-green-500/20">
                          <Zap className="h-4 w-4 text-green-400" />
                          <span className="text-xs text-green-300">Waypoint order has been optimized for efficiency</span>
                        </div>
                      )}
                      {!currentRoute && (
                        <div className="flex items-center space-x-2 p-2 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                          <AlertCircle className="h-4 w-4 text-yellow-400" />
                          <span className="text-xs text-yellow-300">Add at least 2 waypoints to calculate a route</span>
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="settings" className="mt-0 space-y-4">
                  {/* Optimization Options */}
                  <div>
                    <h4 className="text-sm font-medium text-white mb-3">Route Optimization</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-white">Avoid Tolls</span>
                        <Switch
                          checked={routePlanning.optimizationOptions.avoidTolls}
                          onCheckedChange={(checked) =>
                            updateOptimizationOptions({ avoidTolls: checked })
                          }
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-white">Avoid Highways</span>
                        <Switch
                          checked={routePlanning.optimizationOptions.avoidHighways}
                          onCheckedChange={(checked) =>
                            updateOptimizationOptions({ avoidHighways: checked })
                          }
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-white">Avoid Ferries</span>
                        <Switch
                          checked={routePlanning.optimizationOptions.avoidFerries}
                          onCheckedChange={(checked) =>
                            updateOptimizationOptions({ avoidFerries: checked })
                          }
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-white">Optimize Waypoint Order</span>
                        <Switch
                          checked={routePlanning.optimizationOptions.optimizeWaypointOrder}
                          onCheckedChange={(checked) =>
                            updateOptimizationOptions({ optimizeWaypointOrder: checked })
                          }
                        />
                      </div>
                    </div>
                  </div>

                  {/* Travel Mode Selection */}
                  <div>
                    <h4 className="text-sm font-medium text-white mb-3">Travel Mode</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {[
                        { mode: 'DRIVING', icon: Car, label: 'Driving' },
                        { mode: 'WALKING', icon: Footprints, label: 'Walking' },
                        { mode: 'BICYCLING', icon: Bike, label: 'Cycling' },
                        { mode: 'TRANSIT', icon: Bus, label: 'Transit' },
                      ].map(({ mode, icon: Icon, label }) => (
                        <Button
                          key={mode}
                          variant={routePlanning.routePreferences.preferredTravelMode === mode ? "default" : "outline"}
                          size="sm"
                          className={`justify-start ${
                            routePlanning.routePreferences.preferredTravelMode === mode
                              ? "bg-[#FFD700] text-black hover:bg-[#FFF700]"
                              : "border-[#FFD700]/30 text-white hover:bg-[#FFD700]/10"
                          }`}
                          onClick={() => {
                            // This would need to be implemented in the store
                            console.log('Change travel mode to:', mode);
                          }}
                        >
                          <Icon className="h-4 w-4 mr-2" />
                          {label}
                        </Button>
                      ))}
                    </div>
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      {/* Mobile Bottom Sheet - TODO: Implement mobile version */}
      <div className="lg:hidden">
        {/* Mobile implementation would go here */}
      </div>
    </>
  );
}
