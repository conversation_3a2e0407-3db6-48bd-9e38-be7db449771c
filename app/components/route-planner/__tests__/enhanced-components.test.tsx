/**
 * Integration Tests for Enhanced Route Planning Components
 * 
 * Tests the React components that integrate with Routes API v2
 * including user interactions, state management, and API integration.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EnhancedRouteControls } from '../EnhancedRouteControls';
import { RouteAlternatives } from '../RouteAlternatives';
import { EnhancedRouteSummary } from '../EnhancedRouteSummary';
import { RoutesV2FeatureGuide } from '../RoutesV2FeatureGuide';

// Mock the hooks and stores
vi.mock('~/hooks/useRouteCalculation', () => ({
  useRouteCalculation: () => ({
    isCurrentlyCalculating: false,
    isCalculatingAlternatives: false,
    travelMode: 'DRIVING',
    routingPreference: 'TRAFFIC_AWARE',
    alternativeRoutes: [],
    selectedAlternativeIndex: 0,
    routeMetadata: {
      apiVersion: 'v2' as const,
      calculationTime: 1250,
      cacheHit: false,
    },
    isCalculationReady: true,
    travelModes: [
      { mode: 'DRIVING' as const, icon: 'Car', label: 'Driving' },
      { mode: 'WALKING' as const, icon: 'Footprints', label: 'Walking' },
      { mode: 'BICYCLING' as const, icon: 'Bike', label: 'Cycling' },
      { mode: 'TRANSIT' as const, icon: 'Bus', label: 'Transit' },
    ],
    routingPreferences: [
      { value: 'TRAFFIC_UNAWARE' as const, label: 'Fastest Route', description: 'Ignores current traffic' },
      { value: 'TRAFFIC_AWARE' as const, label: 'Traffic Aware', description: 'Considers current traffic' },
      { value: 'TRAFFIC_AWARE_OPTIMAL' as const, label: 'Optimal Route', description: 'Best route with traffic optimization' },
    ],
    setTravelMode: vi.fn(),
    setRoutingPreference: vi.fn(),
    handleCalculateRoute: vi.fn(),
    handleCalculateEnhancedRoute: vi.fn(),
    handleCalculateAlternatives: vi.fn(),
    handleSelectAlternative: vi.fn(),
    handleClearRoute: vi.fn(),
  }),
}));

vi.mock('~/stores/wanderlust', () => ({
  useWanderlustStore: () => ({
    currentRoute: {
      id: 'test-route-1',
      name: 'Test Route',
      waypoints: [],
      optimized: false,
      travelMode: 'DRIVING',
      estimatedDuration: '25 min',
      estimatedDistance: '15.2 km',
      polyline: 'mock_polyline',
      legs: [],
      warnings: ['Construction ahead'],
      copyrights: 'Map data ©2024 Google',
      createdAt: new Date().toISOString(),
      lastOptimized: new Date().toISOString(),
    },
    itinerary: [],
  }),
}));

// Mock environment variables
Object.defineProperty(import.meta, 'env', {
  value: {
    VITE_USE_ROUTES_V2: 'true',
    VITE_ROUTES_V2_PERCENTAGE: '100',
    VITE_ENABLE_ROUTES_FALLBACK: 'true',
    VITE_ROUTES_DEBUG: 'true',
  },
});

describe('EnhancedRouteControls', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render travel mode selection', () => {
    render(<EnhancedRouteControls />);
    
    expect(screen.getByText('Travel Mode')).toBeInTheDocument();
    expect(screen.getByText('Driving')).toBeInTheDocument();
    expect(screen.getByText('Walking')).toBeInTheDocument();
    expect(screen.getByText('Cycling')).toBeInTheDocument();
    expect(screen.getByText('Transit')).toBeInTheDocument();
  });

  it('should render routing preferences when advanced options are enabled', () => {
    render(<EnhancedRouteControls showAdvancedOptions={true} />);
    
    expect(screen.getByText('Routing Preference')).toBeInTheDocument();
    expect(screen.getByText('Traffic Optimization')).toBeInTheDocument();
    expect(screen.getByText('Show Alternatives')).toBeInTheDocument();
  });

  it('should hide advanced options when disabled', () => {
    render(<EnhancedRouteControls showAdvancedOptions={false} />);
    
    expect(screen.queryByText('Routing Preference')).not.toBeInTheDocument();
    expect(screen.queryByText('Traffic Optimization')).not.toBeInTheDocument();
  });

  it('should display route metadata when available', () => {
    render(<EnhancedRouteControls />);
    
    expect(screen.getByText('Route Information')).toBeInTheDocument();
    expect(screen.getByText('API: V2')).toBeInTheDocument();
    expect(screen.getByText('1250ms')).toBeInTheDocument();
    expect(screen.getByText('Fresh')).toBeInTheDocument();
  });

  it('should handle travel mode selection', async () => {
    const mockSetTravelMode = vi.fn();
    vi.mocked(require('~/hooks/useRouteCalculation').useRouteCalculation).mockReturnValue({
      ...require('~/hooks/useRouteCalculation').useRouteCalculation(),
      setTravelMode: mockSetTravelMode,
    });

    render(<EnhancedRouteControls />);
    
    const walkingButton = screen.getByText('Walking');
    await user.click(walkingButton);
    
    expect(mockSetTravelMode).toHaveBeenCalledWith('WALKING');
  });

  it('should handle enhanced route calculation', async () => {
    const mockCalculateEnhanced = vi.fn();
    vi.mocked(require('~/hooks/useRouteCalculation').useRouteCalculation).mockReturnValue({
      ...require('~/hooks/useRouteCalculation').useRouteCalculation(),
      handleCalculateEnhancedRoute: mockCalculateEnhanced,
    });

    render(<EnhancedRouteControls />);
    
    const calculateButton = screen.getByText('Calculate Enhanced Route');
    await user.click(calculateButton);
    
    expect(mockCalculateEnhanced).toHaveBeenCalled();
  });

  it('should show loading state during calculation', () => {
    vi.mocked(require('~/hooks/useRouteCalculation').useRouteCalculation).mockReturnValue({
      ...require('~/hooks/useRouteCalculation').useRouteCalculation(),
      isCurrentlyCalculating: true,
    });

    render(<EnhancedRouteControls />);
    
    expect(screen.getByText('Calculating...')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /calculating/i })).toBeDisabled();
  });
});

describe('RouteAlternatives', () => {
  const mockAlternativeRoutes = [
    {
      id: 'route-1',
      name: 'Route 1',
      estimatedDuration: '25 min',
      estimatedDistance: '15.2 km',
      waypoints: [],
      optimized: false,
      travelMode: 'DRIVING' as const,
      warnings: [],
    },
    {
      id: 'route-2',
      name: 'Route 2',
      estimatedDuration: '30 min',
      estimatedDistance: '18.5 km',
      waypoints: [],
      optimized: false,
      travelMode: 'DRIVING' as const,
      warnings: ['Heavy traffic'],
    },
  ];

  beforeEach(() => {
    vi.mocked(require('~/hooks/useRouteCalculation').useRouteCalculation).mockReturnValue({
      ...require('~/hooks/useRouteCalculation').useRouteCalculation(),
      alternativeRoutes: mockAlternativeRoutes,
      selectedAlternativeIndex: 0,
    });
  });

  it('should display alternative routes', () => {
    render(<RouteAlternatives />);
    
    expect(screen.getByText('Route Alternatives')).toBeInTheDocument();
    expect(screen.getByText('2 routes')).toBeInTheDocument();
    expect(screen.getByText('Route 1')).toBeInTheDocument();
    expect(screen.getByText('Route 2')).toBeInTheDocument();
  });

  it('should show route details', () => {
    render(<RouteAlternatives />);
    
    expect(screen.getByText('25 min')).toBeInTheDocument();
    expect(screen.getByText('15.2 km')).toBeInTheDocument();
    expect(screen.getByText('30 min')).toBeInTheDocument();
    expect(screen.getByText('18.5 km')).toBeInTheDocument();
  });

  it('should handle route selection', async () => {
    const user = userEvent.setup();
    const mockSelectAlternative = vi.fn();
    
    vi.mocked(require('~/hooks/useRouteCalculation').useRouteCalculation).mockReturnValue({
      ...require('~/hooks/useRouteCalculation').useRouteCalculation(),
      alternativeRoutes: mockAlternativeRoutes,
      handleSelectAlternative: mockSelectAlternative,
    });

    render(<RouteAlternatives />);
    
    const route2Card = screen.getByText('Route 2').closest('div');
    if (route2Card) {
      await user.click(route2Card);
      expect(mockSelectAlternative).toHaveBeenCalledWith(1);
    }
  });

  it('should show comparison view when enabled', () => {
    render(<RouteAlternatives showComparison={true} />);
    
    expect(screen.getByText('Routes')).toBeInTheDocument();
    expect(screen.getByText('Compare')).toBeInTheDocument();
  });

  it('should display empty state when no alternatives', () => {
    vi.mocked(require('~/hooks/useRouteCalculation').useRouteCalculation).mockReturnValue({
      ...require('~/hooks/useRouteCalculation').useRouteCalculation(),
      alternativeRoutes: [],
    });

    render(<RouteAlternatives />);
    
    expect(screen.getByText('No alternative routes available')).toBeInTheDocument();
    expect(screen.getByText('Calculate a route to see alternatives')).toBeInTheDocument();
  });
});

describe('EnhancedRouteSummary', () => {
  it('should display route summary information', () => {
    render(<EnhancedRouteSummary />);
    
    expect(screen.getByText('Route Summary')).toBeInTheDocument();
    expect(screen.getByText('25 min')).toBeInTheDocument();
    expect(screen.getByText('15.2 km')).toBeInTheDocument();
  });

  it('should show environmental information when enabled', () => {
    render(<EnhancedRouteSummary showEnvironmentalInfo={true} />);
    
    expect(screen.getByText('Environmental Impact')).toBeInTheDocument();
    expect(screen.getByText('Eco Score')).toBeInTheDocument();
    expect(screen.getByText('Safety Score')).toBeInTheDocument();
  });

  it('should hide environmental info when disabled', () => {
    render(<EnhancedRouteSummary showEnvironmentalInfo={false} />);
    
    expect(screen.queryByText('Environmental Impact')).not.toBeInTheDocument();
  });

  it('should display route warnings', () => {
    render(<EnhancedRouteSummary />);
    
    expect(screen.getByText('Warnings')).toBeInTheDocument();
    expect(screen.getByText('Construction ahead')).toBeInTheDocument();
  });

  it('should show empty state when no route', () => {
    vi.mocked(require('~/stores/wanderlust').useWanderlustStore).mockReturnValue({
      currentRoute: null,
      itinerary: [],
    });

    render(<EnhancedRouteSummary />);
    
    expect(screen.getByText('No route calculated')).toBeInTheDocument();
    expect(screen.getByText('Add waypoints and calculate a route to see summary')).toBeInTheDocument();
  });

  it('should expand and collapse detailed information', async () => {
    const user = userEvent.setup();
    render(<EnhancedRouteSummary showDetails={true} />);
    
    const detailsButton = screen.getByText('Route Details');
    await user.click(detailsButton);
    
    await waitFor(() => {
      expect(screen.getByText('Calculation Info')).toBeInTheDocument();
    });
  });
});

describe('RoutesV2FeatureGuide', () => {
  it('should display feature guide overview', () => {
    render(<RoutesV2FeatureGuide />);
    
    expect(screen.getByText('Routes V2 Features')).toBeInTheDocument();
    expect(screen.getByText('Welcome to Routes V2')).toBeInTheDocument();
    expect(screen.getByText('6x Faster')).toBeInTheDocument();
    expect(screen.getByText('Real-time')).toBeInTheDocument();
  });

  it('should show feature list', async () => {
    const user = userEvent.setup();
    render(<RoutesV2FeatureGuide />);
    
    const featuresTab = screen.getByText('Features');
    await user.click(featuresTab);
    
    expect(screen.getByText('Traffic-Aware Routing')).toBeInTheDocument();
    expect(screen.getByText('Alternative Routes')).toBeInTheDocument();
    expect(screen.getByText('Toll Information')).toBeInTheDocument();
    expect(screen.getByText('Eco-Friendly Routes')).toBeInTheDocument();
  });

  it('should handle feature completion tracking', async () => {
    const user = userEvent.setup();
    render(<RoutesV2FeatureGuide />);
    
    const featuresTab = screen.getByText('Features');
    await user.click(featuresTab);
    
    const trafficFeature = screen.getByText('Traffic-Aware Routing').closest('div');
    if (trafficFeature) {
      await user.click(trafficFeature);
      
      await waitFor(() => {
        expect(screen.getByText('1/6')).toBeInTheDocument();
      });
    }
  });

  it('should show environment configuration', () => {
    render(<RoutesV2FeatureGuide />);
    
    expect(screen.getByText('Routes V2: Enabled')).toBeInTheDocument();
    expect(screen.getByText('Rollout: 100%')).toBeInTheDocument();
  });

  it('should handle close callback', async () => {
    const user = userEvent.setup();
    const mockOnClose = vi.fn();
    
    render(<RoutesV2FeatureGuide onClose={mockOnClose} />);
    
    const closeButton = screen.getByText('×');
    await user.click(closeButton);
    
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should filter to new features only when requested', () => {
    render(<RoutesV2FeatureGuide showOnlyNew={true} />);
    
    const featuresTab = screen.getByText('Features');
    fireEvent.click(featuresTab);
    
    // Should show new features but not enhanced performance (not marked as new)
    expect(screen.getByText('Traffic-Aware Routing')).toBeInTheDocument();
    expect(screen.queryByText('Enhanced Performance')).not.toBeInTheDocument();
  });
});
