import { useState } from 'react';
import { Button } from '~/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '~/components/ui/alert-dialog';
import { Trash2, Route } from 'lucide-react';
import { cn } from '~/lib/utils';

interface ClearRouteButtonProps {
  onClearRoute?: () => void;
  onClearRouteAndItinerary?: () => void;
  variant?: 'route-only' | 'route-and-waypoints';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  showConfirmation?: boolean;
  disabled?: boolean;
  isLoading?: boolean;
}

export function ClearRouteButton({
  onClearRoute,
  onClearRouteAndItinerary,
  variant = 'route-and-waypoints',
  size = 'sm',
  className = '',
  showConfirmation = true,
  disabled = false,
  isLoading = false
}: ClearRouteButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  const isRouteOnly = variant === 'route-only';
  const handleAction = isRouteOnly ? onClearRoute : onClearRouteAndItinerary;

  const buttonText = isRouteOnly ? 'Clear Route' : 'Clear All';
  const buttonIcon = isRouteOnly ? Route : Trash2;
  const IconComponent = buttonIcon;

  const dialogTitle = isRouteOnly 
    ? 'Clear Route?' 
    : 'Clear Route and Waypoints?';
  
  const dialogDescription = isRouteOnly
    ? 'This will remove the current route visualization from the map. Your waypoints will remain.'
    : 'This will remove all waypoints and the current route. This action cannot be undone.';

  const handleClick = () => {
    if (showConfirmation) {
      setIsOpen(true);
    } else {
      handleAction?.();
    }
  };

  const handleConfirm = () => {
    handleAction?.();
    setIsOpen(false);
  };

  if (!handleAction) {
    return null;
  }

  const buttonElement = (
    <Button
      size={size}
      variant="outline"
      onClick={handleClick}
      disabled={disabled || isLoading}
      className={cn(
        "border-red-500/30 text-red-400 hover:bg-red-500/10 hover:text-red-300 hover:border-red-400/50",
        "transition-all duration-200",
        className
      )}
    >
      {isLoading ? (
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-400 mr-2" />
      ) : (
        <IconComponent className="h-4 w-4 mr-2" />
      )}
      {isLoading ? 'Clearing...' : buttonText}
    </Button>
  );

  if (!showConfirmation) {
    return buttonElement;
  }

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        {buttonElement}
      </AlertDialogTrigger>
      <AlertDialogContent className="bg-black/95 border-red-500/30">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-white flex items-center">
            <IconComponent className="h-5 w-5 mr-2 text-red-400" />
            {dialogTitle}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-white/70">
            {dialogDescription}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel className="border-white/20 text-white hover:bg-white/5">
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            className="bg-red-500 text-white hover:bg-red-600"
          >
            {isRouteOnly ? 'Clear Route' : 'Clear All'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
