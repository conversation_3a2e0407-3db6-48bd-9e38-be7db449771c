import { ScrollArea } from '~/components/ui/scroll-area';
import { SortableWaypointItem } from './SortableWaypointItem';
import type { VisitedPlace } from '~/types/wanderlust';

interface WaypointListProps {
  waypoints: VisitedPlace[];
  onRemoveWaypoint: (index: number) => void;
  onReorderWaypoint?: (fromIndex: number, toIndex: number) => void;
  className?: string;
  maxHeight?: string;
  showNumbers?: boolean;
  variant?: 'default' | 'compact';
  enableDragAndDrop?: boolean;
  enableUpDownButtons?: boolean;
  enableReordering?: boolean;
}

export function WaypointList({
  waypoints,
  onRemoveWaypoint,
  onReorderWaypoint,
  className = '',
  maxHeight = 'max-h-32',
  showNumbers = true,
  variant = 'default',
  enableDragAndDrop = true,
  enableUpDownButtons = true,
  enableReordering = true
}: WaypointListProps) {
  if (waypoints.length === 0) {
    return null;
  }

  // If reordering is disabled or no reorder handler, fall back to simple list
  if (!enableReordering || !onReorderWaypoint) {
    return (
      <div className={className}>
        <ScrollArea className={maxHeight}>
          <div className="space-y-2">
            {waypoints.map((waypoint, index) => (
              <SortableWaypointItem
                key={waypoint.id}
                waypoint={waypoint}
                index={index}
                totalItems={waypoints.length}
                onRemove={onRemoveWaypoint}
                onReorder={() => {}} // No-op when reordering disabled
                showNumbers={showNumbers}
                variant={variant}
                enableDragAndDrop={false}
                enableUpDownButtons={false}
              />
            ))}
          </div>
        </ScrollArea>
      </div>
    );
  }

  return (
    <div className={className}>
      <ScrollArea className={maxHeight}>
        <div className="space-y-2">
          {waypoints.map((waypoint, index) => (
            <SortableWaypointItem
              key={waypoint.id}
              waypoint={waypoint}
              index={index}
              totalItems={waypoints.length}
              onRemove={onRemoveWaypoint}
              onReorder={onReorderWaypoint}
              showNumbers={showNumbers}
              variant={variant}
              enableDragAndDrop={enableDragAndDrop}
              enableUpDownButtons={enableUpDownButtons}
            />
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
