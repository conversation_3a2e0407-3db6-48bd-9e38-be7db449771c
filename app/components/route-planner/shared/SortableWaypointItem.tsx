import { useState, useRef } from 'react';
import { Button } from '~/components/ui/button';
import { X, GripVertical, ChevronUp, ChevronDown } from 'lucide-react';
import { useGestures } from '~/hooks/useGestures';
import { cn } from '~/lib/utils';
import type { VisitedPlace } from '~/types/wanderlust';

interface SortableWaypointItemProps {
  waypoint: VisitedPlace;
  index: number;
  totalItems: number;
  onRemove: (index: number) => void;
  onReorder: (fromIndex: number, toIndex: number) => void;
  showNumbers?: boolean;
  variant?: 'default' | 'compact';
  enableDragAndDrop?: boolean;
  enableUpDownButtons?: boolean;
}

export function SortableWaypointItem({
  waypoint,
  index,
  totalItems,
  onRemove,
  onReorder,
  showNumbers = true,
  variant = 'default',
  enableDragAndDrop = true,
  enableUpDownButtons = true
}: SortableWaypointItemProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const itemRef = useRef<HTMLDivElement>(null);
  const dragStartPos = useRef({ x: 0, y: 0 });

  const isCompact = variant === 'compact';
  const canMoveUp = index > 0;
  const canMoveDown = index < totalItems - 1;

  // Handle up/down button reordering
  const handleMoveUp = () => {
    if (canMoveUp) {
      onReorder(index, index - 1);
    }
  };

  const handleMoveDown = () => {
    if (canMoveDown) {
      onReorder(index, index + 1);
    }
  };

  // Drag and drop functionality using the existing gesture system
  const { ref: gestureRef } = useGestures({
    onDragStart: ({ x, y }) => {
      if (!enableDragAndDrop) return;
      
      setIsDragging(true);
      dragStartPos.current = { x, y };
      setDragOffset({ x: 0, y: 0 });
      
      // Add visual feedback
      if (itemRef.current) {
        itemRef.current.style.zIndex = '1000';
        itemRef.current.style.transform = 'scale(1.02)';
      }
    },
    onDragMove: ({ deltaX, deltaY }) => {
      if (!enableDragAndDrop || !isDragging) return;
      
      setDragOffset({ x: deltaX, y: deltaY });
      
      // Calculate which item we're hovering over
      const itemHeight = itemRef.current?.offsetHeight || 60;
      const newIndex = Math.round(index + deltaY / itemHeight);
      const clampedIndex = Math.max(0, Math.min(totalItems - 1, newIndex));
      
      if (clampedIndex !== index) {
        setDragOverIndex(clampedIndex);
      } else {
        setDragOverIndex(null);
      }
    },
    onDragEnd: ({ deltaY }) => {
      if (!enableDragAndDrop || !isDragging) return;
      
      setIsDragging(false);
      setDragOffset({ x: 0, y: 0 });
      
      // Reset visual feedback
      if (itemRef.current) {
        itemRef.current.style.zIndex = '';
        itemRef.current.style.transform = '';
      }
      
      // Perform reorder if significant movement
      if (Math.abs(deltaY) > 30 && dragOverIndex !== null && dragOverIndex !== index) {
        onReorder(index, dragOverIndex);
      }
      
      setDragOverIndex(null);
    },
    config: {
      swipeThreshold: 10, // Lower threshold for drag sensitivity
      velocityThreshold: 0.1,
    },
    disabled: !enableDragAndDrop
  });

  // Combine refs
  const combinedRef = (node: HTMLDivElement | null) => {
    itemRef.current = node;
    if (gestureRef) {
      gestureRef.current = node;
    }
  };

  return (
    <div
      ref={combinedRef}
      className={cn(
        "flex items-center justify-between bg-white/5 rounded-lg border border-white/10 transition-all duration-200",
        isCompact ? 'p-2' : 'p-2',
        isDragging && "shadow-lg border-yellow-400/50 bg-white/10",
        dragOverIndex !== null && dragOverIndex !== index && "border-yellow-400/30",
        enableDragAndDrop && "cursor-grab active:cursor-grabbing"
      )}
      style={{
        transform: isDragging ? `translate(${dragOffset.x}px, ${dragOffset.y}px)` : undefined,
        transition: isDragging ? 'none' : 'transform 200ms ease-out',
      }}
    >
      <div className="flex items-center space-x-3 flex-1">
        {/* Drag Handle */}
        {enableDragAndDrop && (
          <div className="text-white/40 hover:text-white/60 transition-colors">
            <GripVertical className="h-4 w-4" />
          </div>
        )}
        
        {/* Waypoint Number */}
        {showNumbers && (
          <div className="w-6 h-6 rounded-full bg-yellow-400 text-black text-xs font-bold flex items-center justify-center flex-shrink-0">
            {index + 1}
          </div>
        )}
        
        {/* Waypoint Info */}
        <div className="flex-1 min-w-0">
          <div className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-white truncate`}>
            {waypoint.name}
          </div>
          <div className={`${isCompact ? 'text-xs' : 'text-xs'} text-white/60 truncate`}>
            {waypoint.city}
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center space-x-1">
        {/* Up/Down Buttons */}
        {enableUpDownButtons && (
          <div className="flex flex-col">
            <Button
              size="sm"
              variant="ghost"
              onClick={handleMoveUp}
              disabled={!canMoveUp}
              className={cn(
                "h-4 w-6 p-0 text-white/60 hover:text-white hover:bg-white/10",
                !canMoveUp && "opacity-30 cursor-not-allowed"
              )}
              title="Move up"
            >
              <ChevronUp className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleMoveDown}
              disabled={!canMoveDown}
              className={cn(
                "h-4 w-6 p-0 text-white/60 hover:text-white hover:bg-white/10",
                !canMoveDown && "opacity-30 cursor-not-allowed"
              )}
              title="Move down"
            >
              <ChevronDown className="h-3 w-3" />
            </Button>
          </div>
        )}
        
        {/* Remove Button */}
        <Button
          size="sm"
          variant="ghost"
          onClick={() => onRemove(index)}
          className="h-6 w-6 p-0 text-red-400 hover:text-red-300 hover:bg-red-500/10"
          title="Remove from route"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
