import { Button } from '~/components/ui/button';
import { MapPin, Target } from 'lucide-react';
import { useWaypointManagement } from '~/hooks/useWaypointManagement';
import { usePlaceSearch } from '~/hooks/usePlaceSearch';
import { useRouteCalculation } from '~/hooks/useRouteCalculation';
import { WaypointList } from './WaypointList';
import { PlaceSearchInput } from './PlaceSearchInput';
import { CategoryFilter } from './CategoryFilter';
import { PlacesList } from './PlacesList';
import { ClearRouteButton } from './ClearRouteButton';
import type { CityRegion } from '~/types/wanderlust';

interface WaypointManagerProps {
  regions: CityRegion[];
  className?: string;
  variant?: 'default' | 'compact';
  showCategoryFilter?: boolean;
  showCurrentLocationButton?: boolean;
  maxPlacesHeight?: string;
  maxWaypointsHeight?: string;
  enableReordering?: boolean;
  enableDragAndDrop?: boolean;
  enableUpDownButtons?: boolean;
  showClearRouteButtons?: boolean;
}

export function WaypointManager({
  regions,
  className = '',
  variant = 'default',
  showCategoryFilter = true,
  showCurrentLocationButton = true,
  maxPlacesHeight = 'h-48',
  maxWaypointsHeight = 'max-h-32',
  enableReordering = true,
  enableDragAndDrop = true,
  enableUpDownButtons = true,
  showClearRouteButtons = false
}: WaypointManagerProps) {
  const {
    itinerary,
    handleAddWaypoint,
    handleRemoveWaypointByIndex,
    handleReorderWaypoint,
    handleAddCurrentLocation,
  } = useWaypointManagement();

  const {
    currentRoute,
    handleClearRoute,
    handleClearRouteAndItinerary,
  } = useRouteCalculation();

  const {
    searchQuery,
    setSearchQuery,
    selectedCategory,
    setSelectedCategory,
    filteredPlaces,
    categories,
    getCategoryIcon,
  } = usePlaceSearch(regions, itinerary);

  const isCompact = variant === 'compact';

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Current Waypoints */}
      {itinerary.length > 0 && (
        <div>
          <h4 className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-white mb-2 flex items-center`}>
            <MapPin className="h-4 w-4 mr-2 text-yellow-400" />
            Current Route ({itinerary.length} stops)
          </h4>
          <WaypointList
            waypoints={itinerary}
            onRemoveWaypoint={handleRemoveWaypointByIndex}
            onReorderWaypoint={handleReorderWaypoint}
            maxHeight={maxWaypointsHeight}
            variant={variant}
            enableReordering={enableReordering}
            enableDragAndDrop={enableDragAndDrop}
            enableUpDownButtons={enableUpDownButtons}
          />
        </div>
      )}

      {/* Quick Actions */}
      {showCurrentLocationButton && (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleAddCurrentLocation}
            className="flex-1 border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10"
          >
            <Target className="h-4 w-4 mr-2" />
            Add Current Location
          </Button>
        </div>
      )}

      {/* Search and Add Places */}
      <div className="space-y-3">
        <PlaceSearchInput
          value={searchQuery}
          onChange={setSearchQuery}
          placeholder="Search destinations..."
        />

        {/* Category Filter */}
        {showCategoryFilter && (
          <CategoryFilter
            categories={categories}
            selectedCategory={selectedCategory}
            onCategoryChange={setSelectedCategory}
            getCategoryIcon={getCategoryIcon}
            variant={variant}
          />
        )}

        {/* Places List */}
        <PlacesList
          places={filteredPlaces}
          onAddPlace={handleAddWaypoint}
          searchQuery={searchQuery}
          maxHeight={maxPlacesHeight}
          variant={variant}
        />
      </div>

      {/* Clear Route Actions */}
      {showClearRouteButtons && (itinerary.length > 0 || currentRoute) && (
        <div className="pt-3 border-t border-white/10">
          <div className="flex space-x-2">
            {currentRoute && (
              <ClearRouteButton
                variant="route-only"
                onClearRoute={handleClearRoute}
                size="sm"
                className="flex-1"
                showConfirmation={false}
              />
            )}
            {itinerary.length > 0 && (
              <ClearRouteButton
                variant="route-and-waypoints"
                onClearRouteAndItinerary={handleClearRouteAndItinerary}
                size="sm"
                className="flex-1"
                showConfirmation={true}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
}
