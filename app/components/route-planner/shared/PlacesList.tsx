import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Plus } from 'lucide-react';
import type { VisitedPlace } from '~/types/wanderlust';

interface PlacesListProps {
  places: VisitedPlace[];
  onAddPlace: (place: VisitedPlace) => void;
  searchQuery: string;
  className?: string;
  maxHeight?: string;
  variant?: 'default' | 'compact';
}

export function PlacesList({
  places,
  onAddPlace,
  searchQuery,
  className = '',
  maxHeight = 'h-48',
  variant = 'default'
}: PlacesListProps) {
  const isCompact = variant === 'compact';

  return (
    <div className={className}>
      <ScrollArea className={maxHeight}>
        <div className="space-y-2">
          {places.map(place => (
            <div
              key={place.id}
              className={`flex items-center justify-between ${
                isCompact ? 'p-2' : 'p-3'
              } bg-white/5 rounded-lg border border-white/10 hover:border-yellow-400/50 transition-colors cursor-pointer`}
              onClick={() => onAddPlace(place)}
            >
              <div className="flex-1">
                <div className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-white`}>
                  {place.name}
                </div>
                <div className={`${isCompact ? 'text-xs' : 'text-xs'} text-white/60`}>
                  {place.city}
                </div>
                <div className="flex items-center mt-1 space-x-1">
                  <Badge 
                    variant="outline" 
                    className={`${isCompact ? 'text-xs' : 'text-xs'} border-white/20 text-white/70`}
                  >
                    {place.category}
                  </Badge>
                  {place.rating && (
                    <Badge 
                      variant="outline" 
                      className={`${isCompact ? 'text-xs' : 'text-xs'} border-yellow-500/30 text-yellow-500`}
                    >
                      ★ {place.rating}
                    </Badge>
                  )}
                </div>
              </div>
              <Plus className="h-4 w-4 text-yellow-400" />
            </div>
          ))}
          {places.length === 0 && (
            <div className="text-center text-white/60 py-4">
              {searchQuery ? 'No places match your search' : 'All places added to route'}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
