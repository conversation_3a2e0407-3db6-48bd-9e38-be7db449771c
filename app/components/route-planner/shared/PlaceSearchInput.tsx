import { Input } from '~/components/ui/input';
import { Search } from 'lucide-react';

interface PlaceSearchInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export function PlaceSearchInput({
  value,
  onChange,
  placeholder = "Search destinations...",
  className = ""
}: PlaceSearchInputProps) {
  return (
    <div className={`relative ${className}`}>
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
      <Input
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="pl-10 bg-white/5 border-white/20 text-white placeholder-white/50 focus:border-yellow-400"
      />
    </div>
  );
}
