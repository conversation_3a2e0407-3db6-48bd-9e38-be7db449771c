import { Button } from '~/components/ui/button';
import { ScrollArea } from '~/components/ui/scroll-area';
import { cn } from '~/lib/utils';

interface CategoryFilterProps {
  categories: string[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  getCategoryIcon: (category: string) => string;
  className?: string;
  variant?: 'default' | 'compact';
}

export function CategoryFilter({
  categories,
  selectedCategory,
  onCategoryChange,
  getCategoryIcon,
  className = '',
  variant = 'default'
}: CategoryFilterProps) {
  const isCompact = variant === 'compact';

  return (
    <div className={className}>
      <ScrollArea className="w-full">
        <div className={`flex ${isCompact ? 'space-x-1' : 'space-x-2'} pb-2`}>
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size={isCompact ? "sm" : "sm"}
              onClick={() => onCategoryChange(category)}
              className={cn(
                "whitespace-nowrap",
                isCompact ? "text-xs px-2" : "text-xs",
                selectedCategory === category
                  ? "bg-yellow-500 text-black hover:bg-yellow-400"
                  : "border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10"
              )}
            >
              {getCategoryIcon(category)} {category === 'all' ? 'All' : category}
            </Button>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}
