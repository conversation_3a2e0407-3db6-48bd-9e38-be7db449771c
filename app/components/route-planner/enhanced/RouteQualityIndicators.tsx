/**
 * Route Quality Indicators Component
 * 
 * Displays route quality metrics and contextual information for the FIFA route planner.
 */

import React from 'react';
import { Badge } from '~/components/ui/badge';
import { 
  Clock, 
  MapPin, 
  Zap, 
  AlertTriangle, 
  CheckCircle, 
  TrendingUp,
  Fuel,
  DollarSign
} from 'lucide-react';
import { cn } from '~/lib/utils';
import type { EnhancedTravelRoute } from '~/types/routes-v2';

interface RouteQualityIndicatorsProps {
  route?: EnhancedTravelRoute;
  alternativeRoutes?: EnhancedTravelRoute[];
  className?: string;
}

export function RouteQualityIndicators({ 
  route, 
  alternativeRoutes = [], 
  className 
}: RouteQualityIndicatorsProps) {
  if (!route) return null;

  // Calculate quality metrics
  const getRouteQuality = () => {
    const metrics = {
      efficiency: 85, // Based on distance vs optimal
      traffic: 70,    // Based on current traffic conditions
      reliability: 90, // Based on road conditions and historical data
      eco: 75,        // Environmental impact score
    };

    const overall = Math.round((metrics.efficiency + metrics.traffic + metrics.reliability + metrics.eco) / 4);
    
    return { ...metrics, overall };
  };

  const quality = getRouteQuality();

  // Get quality color based on score
  const getQualityColor = (score: number) => {
    if (score >= 80) return 'text-green-400';
    if (score >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  // Get quality badge variant
  const getQualityBadge = (score: number) => {
    if (score >= 80) return 'bg-green-500/20 text-green-400 border-green-500/30';
    if (score >= 60) return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
    return 'bg-red-500/20 text-red-400 border-red-500/30';
  };

  return (
    <div className={cn('space-y-3', className)}>
      {/* Overall Quality Score */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <CheckCircle className="h-4 w-4 text-yellow-400" />
          <span className="text-white font-medium text-sm">Route Quality</span>
        </div>
        <Badge className={cn('text-xs font-semibold', getQualityBadge(quality.overall))}>
          {quality.overall}% Optimal
        </Badge>
      </div>

      {/* Quality Metrics Grid */}
      <div className="grid grid-cols-2 gap-2">
        {/* Efficiency */}
        <div className="bg-white/5 rounded-lg p-3">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center space-x-1">
              <TrendingUp className="h-3 w-3 text-yellow-400" />
              <span className="text-white/70 text-xs">Efficiency</span>
            </div>
            <span className={cn('text-xs font-medium', getQualityColor(quality.efficiency))}>
              {quality.efficiency}%
            </span>
          </div>
          <div className="w-full bg-white/10 rounded-full h-1">
            <div 
              className="bg-yellow-400 h-1 rounded-full transition-all duration-300"
              style={{ width: `${quality.efficiency}%` }}
            />
          </div>
        </div>

        {/* Traffic */}
        <div className="bg-white/5 rounded-lg p-3">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3 text-yellow-400" />
              <span className="text-white/70 text-xs">Traffic</span>
            </div>
            <span className={cn('text-xs font-medium', getQualityColor(quality.traffic))}>
              {quality.traffic}%
            </span>
          </div>
          <div className="w-full bg-white/10 rounded-full h-1">
            <div 
              className="bg-yellow-400 h-1 rounded-full transition-all duration-300"
              style={{ width: `${quality.traffic}%` }}
            />
          </div>
        </div>

        {/* Reliability */}
        <div className="bg-white/5 rounded-lg p-3">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center space-x-1">
              <MapPin className="h-3 w-3 text-yellow-400" />
              <span className="text-white/70 text-xs">Reliability</span>
            </div>
            <span className={cn('text-xs font-medium', getQualityColor(quality.reliability))}>
              {quality.reliability}%
            </span>
          </div>
          <div className="w-full bg-white/10 rounded-full h-1">
            <div 
              className="bg-yellow-400 h-1 rounded-full transition-all duration-300"
              style={{ width: `${quality.reliability}%` }}
            />
          </div>
        </div>

        {/* Eco Score */}
        <div className="bg-white/5 rounded-lg p-3">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center space-x-1">
              <Fuel className="h-3 w-3 text-yellow-400" />
              <span className="text-white/70 text-xs">Eco</span>
            </div>
            <span className={cn('text-xs font-medium', getQualityColor(quality.eco))}>
              {quality.eco}%
            </span>
          </div>
          <div className="w-full bg-white/10 rounded-full h-1">
            <div 
              className="bg-yellow-400 h-1 rounded-full transition-all duration-300"
              style={{ width: `${quality.eco}%` }}
            />
          </div>
        </div>
      </div>

      {/* Route Insights */}
      <div className="space-y-2">
        {/* Traffic Alert */}
        {quality.traffic < 70 && (
          <div className="flex items-start space-x-2 p-2 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
            <AlertTriangle className="h-3 w-3 text-yellow-400 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-yellow-200">
              <span className="font-medium">Traffic Alert:</span> Moderate delays expected on this route.
            </div>
          </div>
        )}

        {/* Eco Tip */}
        {quality.eco >= 80 && (
          <div className="flex items-start space-x-2 p-2 bg-green-500/10 border border-green-500/30 rounded-lg">
            <Zap className="h-3 w-3 text-green-400 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-green-200">
              <span className="font-medium">Eco-Friendly:</span> This route minimizes fuel consumption.
            </div>
          </div>
        )}

        {/* Alternative Routes Available */}
        {alternativeRoutes.length > 0 && (
          <div className="flex items-start space-x-2 p-2 bg-blue-500/10 border border-blue-500/30 rounded-lg">
            <MapPin className="h-3 w-3 text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-blue-200">
              <span className="font-medium">Alternatives:</span> {alternativeRoutes.length} other route{alternativeRoutes.length > 1 ? 's' : ''} available.
            </div>
          </div>
        )}
      </div>

      {/* Cost Information (if available) */}
      {route.tollInfo && (
        <div className="bg-white/5 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-yellow-400" />
              <span className="text-white font-medium text-sm">Estimated Cost</span>
            </div>
            <div className="text-right">
              {route.tollInfo.estimatedCost && (
                <div className="text-white text-sm font-medium">
                  {route.tollInfo.currency || '$'}{route.tollInfo.estimatedCost.toFixed(2)}
                </div>
              )}
              <div className="text-white/60 text-xs">Tolls included</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
