/**
 * Progressive Loading States Component
 * 
 * Enhanced loading indicators with step-by-step progress for route calculation.
 * Designed for the FIFA Club World Cup 2025™ route planner.
 */

import React, { useState, useEffect } from 'react';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { 
  Loader2, 
  MapPin, 
  Route, 
  Clock, 
  AlertTriangle, 
  RefreshCw,
  CheckCircle,
  XCircle,
  Navigation,
  Zap
} from 'lucide-react';
import { cn } from '~/lib/utils';

interface CalculationStep {
  id: string;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  estimatedDuration: number; // in milliseconds
}

interface ProgressiveLoadingStatesProps {
  isCalculating: boolean;
  error?: string | null;
  onRetry?: () => void;
  onCancel?: () => void;
  className?: string;
}

const CALCULATION_STEPS: CalculationStep[] = [
  {
    id: 'analyzing',
    label: 'Analyzing Waypoints',
    description: 'Processing your selected destinations',
    icon: MapPin,
    estimatedDuration: 500,
  },
  {
    id: 'calculating',
    label: 'Calculating Routes',
    description: 'Finding optimal paths between locations',
    icon: Route,
    estimatedDuration: 1200,
  },
  {
    id: 'traffic',
    label: 'Fetching Traffic Data',
    description: 'Getting real-time traffic information',
    icon: Clock,
    estimatedDuration: 800,
  },
  {
    id: 'alternatives',
    label: 'Generating Alternatives',
    description: 'Creating alternative route options',
    icon: Navigation,
    estimatedDuration: 600,
  },
  {
    id: 'optimizing',
    label: 'Optimizing Results',
    description: 'Finalizing route recommendations',
    icon: Zap,
    estimatedDuration: 400,
  },
];

export function ProgressiveLoadingStates({
  isCalculating,
  error,
  onRetry,
  onCancel,
  className,
}: ProgressiveLoadingStatesProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());

  // Simulate step progression during calculation
  useEffect(() => {
    if (!isCalculating) {
      setCurrentStep(0);
      setCompletedSteps(new Set());
      return;
    }

    let timeoutId: NodeJS.Timeout;
    let stepIndex = 0;

    const progressToNextStep = () => {
      if (stepIndex < CALCULATION_STEPS.length) {
        setCurrentStep(stepIndex);
        
        timeoutId = setTimeout(() => {
          setCompletedSteps(prev => new Set([...prev, stepIndex]));
          stepIndex++;
          
          if (stepIndex < CALCULATION_STEPS.length) {
            progressToNextStep();
          }
        }, CALCULATION_STEPS[stepIndex].estimatedDuration);
      }
    };

    progressToNextStep();

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [isCalculating]);

  // Render skeleton loading state
  const renderSkeleton = () => (
    <div className="space-y-3 animate-pulse">
      {/* Route Card Skeleton */}
      <div className="bg-white/5 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="w-5 h-5 bg-white/20 rounded-full" />
            <div className="w-24 h-4 bg-white/20 rounded" />
          </div>
          <div className="w-16 h-6 bg-white/20 rounded-full" />
        </div>
        <div className="grid grid-cols-2 gap-3">
          <div className="w-full h-8 bg-white/20 rounded" />
          <div className="w-full h-8 bg-white/20 rounded" />
        </div>
      </div>

      {/* Steps Skeleton */}
      <div className="space-y-2">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg">
            <div className="w-6 h-6 bg-white/20 rounded-full" />
            <div className="flex-1">
              <div className="w-32 h-3 bg-white/20 rounded mb-1" />
              <div className="w-24 h-2 bg-white/20 rounded" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // Render error state
  const renderError = () => (
    <div className="space-y-4">
      <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <XCircle className="h-5 w-5 text-red-400 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <h4 className="text-red-400 font-medium text-sm mb-1">
              Route Calculation Failed
            </h4>
            <p className="text-red-200 text-xs leading-relaxed mb-3">
              {error || 'Unable to calculate route. Please check your waypoints and try again.'}
            </p>
            
            {/* Error Recovery Actions */}
            <div className="flex flex-wrap gap-2">
              {onRetry && (
                <Button
                  size="sm"
                  onClick={onRetry}
                  className="bg-red-500/20 text-red-400 hover:bg-red-500/30 border border-red-500/30"
                >
                  <RefreshCw className="h-3 w-3 mr-2" />
                  Try Again
                </Button>
              )}
              {onCancel && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={onCancel}
                  className="text-white/60 hover:text-white hover:bg-white/10"
                >
                  Cancel
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Helpful Suggestions */}
      <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
        <div className="flex items-start space-x-2">
          <AlertTriangle className="h-4 w-4 text-yellow-400 mt-0.5 flex-shrink-0" />
          <div>
            <h5 className="text-yellow-400 font-medium text-xs mb-1">Suggestions:</h5>
            <ul className="text-yellow-200 text-xs space-y-1">
              <li>• Ensure you have at least 2 waypoints selected</li>
              <li>• Check your internet connection</li>
              <li>• Try selecting different locations</li>
              <li>• Reduce the number of waypoints if you have many</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  // Render calculation progress
  const renderProgress = () => (
    <div className="space-y-4">
      {/* Overall Progress */}
      <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <Loader2 className="h-5 w-5 text-yellow-400 animate-spin" />
            <div>
              <h4 className="text-white font-medium text-sm">Calculating Route</h4>
              <p className="text-white/70 text-xs">
                {CALCULATION_STEPS[currentStep]?.description || 'Processing...'}
              </p>
            </div>
          </div>
          <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30 text-xs">
            {Math.round(((completedSteps.size) / CALCULATION_STEPS.length) * 100)}%
          </Badge>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-white/10 rounded-full h-2">
          <div 
            className="bg-yellow-400 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ 
              width: `${((completedSteps.size + 0.5) / CALCULATION_STEPS.length) * 100}%` 
            }}
          />
        </div>
      </div>

      {/* Step Details */}
      <div className="space-y-2">
        {CALCULATION_STEPS.map((step, index) => {
          const isCompleted = completedSteps.has(index);
          const isCurrent = currentStep === index;
          const isPending = index > currentStep;

          const IconComponent = step.icon;

          return (
            <div
              key={step.id}
              className={cn(
                'flex items-center space-x-3 p-3 rounded-lg transition-all duration-300',
                isCompleted && 'bg-green-500/10 border border-green-500/30',
                isCurrent && 'bg-yellow-500/10 border border-yellow-500/30',
                isPending && 'bg-white/5 border border-white/10'
              )}
            >
              <div className={cn(
                'w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0',
                isCompleted && 'bg-green-500/20',
                isCurrent && 'bg-yellow-500/20',
                isPending && 'bg-white/10'
              )}>
                {isCompleted ? (
                  <CheckCircle className="h-4 w-4 text-green-400" />
                ) : isCurrent ? (
                  <Loader2 className="h-4 w-4 text-yellow-400 animate-spin" />
                ) : (
                  <IconComponent className={cn(
                    'h-4 w-4',
                    isPending ? 'text-white/40' : 'text-white/60'
                  )} />
                )}
              </div>

              <div className="flex-1 min-w-0">
                <div className={cn(
                  'font-medium text-sm',
                  isCompleted && 'text-green-400',
                  isCurrent && 'text-yellow-400',
                  isPending && 'text-white/60'
                )}>
                  {step.label}
                </div>
                <div className={cn(
                  'text-xs',
                  isCompleted && 'text-green-200',
                  isCurrent && 'text-yellow-200',
                  isPending && 'text-white/40'
                )}>
                  {step.description}
                </div>
              </div>

              {isCompleted && (
                <Badge className="bg-green-500/20 text-green-400 border-green-500/30 text-xs">
                  Done
                </Badge>
              )}
            </div>
          );
        })}
      </div>

      {/* Cancel Action */}
      {onCancel && (
        <div className="flex justify-center pt-2">
          <Button
            size="sm"
            variant="ghost"
            onClick={onCancel}
            className="text-white/60 hover:text-white hover:bg-white/10"
          >
            Cancel Calculation
          </Button>
        </div>
      )}
    </div>
  );

  if (error) {
    return (
      <div className={cn('space-y-4', className)}>
        {renderError()}
      </div>
    );
  }

  if (isCalculating) {
    return (
      <div className={cn('space-y-4', className)}>
        {renderProgress()}
        {renderSkeleton()}
      </div>
    );
  }

  return null;
}

// Hook for managing loading states
export function useProgressiveLoading() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);

  const startLoading = () => {
    setIsLoading(true);
    setError(null);
    setCurrentStep(0);
  };

  const stopLoading = () => {
    setIsLoading(false);
    setCurrentStep(0);
  };

  const setLoadingError = (errorMessage: string) => {
    setError(errorMessage);
    setIsLoading(false);
  };

  const clearError = () => {
    setError(null);
  };

  return {
    isLoading,
    error,
    currentStep,
    startLoading,
    stopLoading,
    setLoadingError,
    clearError,
  };
}
