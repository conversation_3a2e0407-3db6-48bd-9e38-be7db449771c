/**
 * Swipeable Bottom Sheet Component
 * 
 * Mobile-optimized bottom sheet with snap points and gesture support.
 * Designed for the FIFA Club World Cup 2025™ route planner.
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { cn } from '~/lib/utils';

export interface SwipeableBottomSheetProps {
  children: React.ReactNode;
  snapPoints: string[]; // e.g., ['10%', '50%', '90%']
  initialSnap?: number; // Index of initial snap point
  onSnapChange?: (snapIndex: number) => void;
  className?: string;
  handleClassName?: string;
  contentClassName?: string;
  isOpen?: boolean;
  onClose?: () => void;
  enableBackdrop?: boolean;
  backdropClassName?: string;
}

export function SwipeableBottomSheet({
  children,
  snapPoints = ['10%', '50%', '90%'],
  initialSnap = 0,
  onSnapChange,
  className,
  handleClassName,
  contentClassName,
  isOpen = true,
  onClose,
  enableBackdrop = false,
  backdropClassName,
}: SwipeableBottomSheetProps) {
  const [currentSnap, setCurrentSnap] = useState(initialSnap);
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const [currentY, setCurrentY] = useState(0);
  const sheetRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // Convert percentage to pixels
  const getSnapPosition = useCallback((snapPoint: string): number => {
    const percentage = parseFloat(snapPoint.replace('%', ''));
    return window.innerHeight * (1 - percentage / 100);
  }, []);

  // Get current snap position
  const getCurrentPosition = useCallback((): number => {
    return getSnapPosition(snapPoints[currentSnap]);
  }, [currentSnap, snapPoints, getSnapPosition]);

  // Find nearest snap point
  const findNearestSnap = useCallback((position: number): number => {
    let nearestIndex = 0;
    let nearestDistance = Math.abs(position - getSnapPosition(snapPoints[0]));

    snapPoints.forEach((snapPoint, index) => {
      const snapPosition = getSnapPosition(snapPoint);
      const distance = Math.abs(position - snapPosition);
      if (distance < nearestDistance) {
        nearestDistance = distance;
        nearestIndex = index;
      }
    });

    return nearestIndex;
  }, [snapPoints, getSnapPosition]);

  // Handle touch start
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    setIsDragging(true);
    setStartY(e.touches[0].clientY);
    setCurrentY(e.touches[0].clientY);
  }, []);

  // Handle touch move
  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isDragging) return;

    const touchY = e.touches[0].clientY;
    setCurrentY(touchY);

    const deltaY = touchY - startY;
    const currentPosition = getCurrentPosition();
    const newPosition = Math.max(0, Math.min(window.innerHeight, currentPosition + deltaY));

    if (sheetRef.current) {
      sheetRef.current.style.transform = `translateY(${newPosition}px)`;
    }
  }, [isDragging, startY, getCurrentPosition]);

  // Handle touch end
  const handleTouchEnd = useCallback(() => {
    if (!isDragging) return;

    setIsDragging(false);
    
    const deltaY = currentY - startY;
    const currentPosition = getCurrentPosition();
    const newPosition = currentPosition + deltaY;
    
    const nearestSnapIndex = findNearestSnap(newPosition);
    
    // Check if we should close the sheet (swipe down from first snap point)
    if (nearestSnapIndex === 0 && deltaY > 100 && onClose) {
      onClose();
      return;
    }

    setCurrentSnap(nearestSnapIndex);
    onSnapChange?.(nearestSnapIndex);

    // Animate to snap position
    if (sheetRef.current) {
      sheetRef.current.style.transition = 'transform 0.3s cubic-bezier(0.32, 0.72, 0, 1)';
      sheetRef.current.style.transform = `translateY(${getSnapPosition(snapPoints[nearestSnapIndex])}px)`;
      
      setTimeout(() => {
        if (sheetRef.current) {
          sheetRef.current.style.transition = '';
        }
      }, 300);
    }
  }, [isDragging, currentY, startY, getCurrentPosition, findNearestSnap, onSnapChange, snapPoints, getSnapPosition, onClose]);

  // Handle mouse events for desktop
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    setStartY(e.clientY);
    setCurrentY(e.clientY);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return;

    setCurrentY(e.clientY);
    const deltaY = e.clientY - startY;
    const currentPosition = getCurrentPosition();
    const newPosition = Math.max(0, Math.min(window.innerHeight, currentPosition + deltaY));

    if (sheetRef.current) {
      sheetRef.current.style.transform = `translateY(${newPosition}px)`;
    }
  }, [isDragging, startY, getCurrentPosition]);

  const handleMouseUp = useCallback(() => {
    if (!isDragging) return;

    setIsDragging(false);
    
    const deltaY = currentY - startY;
    const currentPosition = getCurrentPosition();
    const newPosition = currentPosition + deltaY;
    
    const nearestSnapIndex = findNearestSnap(newPosition);
    setCurrentSnap(nearestSnapIndex);
    onSnapChange?.(nearestSnapIndex);

    if (sheetRef.current) {
      sheetRef.current.style.transition = 'transform 0.3s cubic-bezier(0.32, 0.72, 0, 1)';
      sheetRef.current.style.transform = `translateY(${getSnapPosition(snapPoints[nearestSnapIndex])}px)`;
      
      setTimeout(() => {
        if (sheetRef.current) {
          sheetRef.current.style.transition = '';
        }
      }, 300);
    }
  }, [isDragging, currentY, startY, getCurrentPosition, findNearestSnap, onSnapChange, snapPoints, getSnapPosition]);

  // Set up mouse event listeners
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // Initialize position
  useEffect(() => {
    if (sheetRef.current && isOpen) {
      const initialPosition = getSnapPosition(snapPoints[currentSnap]);
      sheetRef.current.style.transform = `translateY(${initialPosition}px)`;
    }
  }, [isOpen, currentSnap, snapPoints, getSnapPosition]);

  // Handle snap point changes
  const snapToPoint = useCallback((snapIndex: number) => {
    if (snapIndex >= 0 && snapIndex < snapPoints.length) {
      setCurrentSnap(snapIndex);
      onSnapChange?.(snapIndex);
      
      if (sheetRef.current) {
        sheetRef.current.style.transition = 'transform 0.3s cubic-bezier(0.32, 0.72, 0, 1)';
        sheetRef.current.style.transform = `translateY(${getSnapPosition(snapPoints[snapIndex])}px)`;
        
        setTimeout(() => {
          if (sheetRef.current) {
            sheetRef.current.style.transition = '';
          }
        }, 300);
      }
    }
  }, [snapPoints, getSnapPosition, onSnapChange]);

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      {enableBackdrop && (
        <div
          className={cn(
            'fixed inset-0 bg-black/50 z-40',
            backdropClassName
          )}
          onClick={onClose}
        />
      )}

      {/* Bottom Sheet */}
      <div
        ref={sheetRef}
        className={cn(
          'fixed left-0 right-0 bottom-0 z-50',
          'bg-gradient-to-br from-black via-gray-900 to-red-900',
          'border-t border-yellow-500/30',
          'rounded-t-xl shadow-2xl',
          'touch-none select-none',
          className
        )}
        style={{
          height: '100vh',
          transform: `translateY(${getCurrentPosition()}px)`,
        }}
      >
        {/* Drag Handle */}
        <div
          className={cn(
            'flex justify-center py-3 cursor-grab active:cursor-grabbing',
            handleClassName
          )}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onMouseDown={handleMouseDown}
        >
          <div className="w-12 h-1 bg-white/30 rounded-full" />
        </div>

        {/* Content */}
        <div
          ref={contentRef}
          className={cn(
            'h-full overflow-hidden',
            contentClassName
          )}
        >
          {children}
        </div>
      </div>
    </>
  );
}

// Hook for controlling bottom sheet
export function useSwipeableBottomSheet(initialSnap: number = 0) {
  const [currentSnap, setCurrentSnap] = useState(initialSnap);
  const [isOpen, setIsOpen] = useState(true);

  const snapTo = useCallback((snapIndex: number) => {
    setCurrentSnap(snapIndex);
  }, []);

  const open = useCallback(() => {
    setIsOpen(true);
  }, []);

  const close = useCallback(() => {
    setIsOpen(false);
  }, []);

  return {
    currentSnap,
    isOpen,
    snapTo,
    open,
    close,
    setCurrentSnap,
  };
}
