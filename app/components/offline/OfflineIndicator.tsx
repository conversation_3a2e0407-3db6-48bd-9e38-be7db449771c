/**
 * Offline Indicator Component
 * 
 * FIFA Club World Cup 2025™ themed offline status indicator with smooth animations.
 * Features:
 * - Top-right positioned status indicator using shadcn/ui Alert and Badge
 * - FIFA black/gold/red color scheme with black-to-red gradient background
 * - Slide-down entrance using Framer Motion (consistent with Priority 2.1 patterns)
 * - States: Online (hidden), Offline (visible with cached data count), Syncing (progress indicator)
 * - Mobile UX: Collapsible to icon-only on small screens, expandable on tap
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  Database, 
  Clock, 
  ChevronDown, 
  ChevronUp 
} from 'lucide-react';
import { Alert, AlertDescription } from '~/components/ui/alert';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { cn } from '~/lib/utils';

export interface OfflineStatus {
  isOnline: boolean;
  isSyncing: boolean;
  cachedPlacesCount: number;
  cachedRoutesCount: number;
  lastSyncTime: Date | null;
  pendingActionsCount: number;
  cacheSize: number; // in bytes
}

interface OfflineIndicatorProps {
  status: OfflineStatus;
  onSync?: () => void;
  onClearCache?: () => void;
  className?: string;
}

export function OfflineIndicator({ 
  status, 
  onSync, 
  onClearCache, 
  className 
}: OfflineIndicatorProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Auto-collapse on mobile after 5 seconds
  useEffect(() => {
    if (isMobile && isExpanded) {
      const timer = setTimeout(() => setIsExpanded(false), 5000);
      return () => clearTimeout(timer);
    }
  }, [isMobile, isExpanded]);

  // Don't show indicator when online (unless syncing)
  if (status.isOnline && !status.isSyncing) {
    return null;
  }

  const formatCacheSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
  };

  const formatLastSync = (date: Date | null): string => {
    if (!date) return 'Never';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    return date.toLocaleDateString();
  };

  const getStatusIcon = () => {
    if (status.isSyncing) {
      return (
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
        >
          <RefreshCw className="h-4 w-4" />
        </motion.div>
      );
    }
    return status.isOnline ? <Wifi className="h-4 w-4" /> : <WifiOff className="h-4 w-4" />;
  };

  const getStatusText = () => {
    if (status.isSyncing) return 'Syncing...';
    return status.isOnline ? 'Online' : 'Offline';
  };

  const getStatusVariant = () => {
    if (status.isSyncing) return 'default';
    return status.isOnline ? 'default' : 'destructive';
  };

  // Animation variants following Priority 2.1 patterns
  const slideDownVariants = {
    hidden: { 
      opacity: 0, 
      y: -20, 
      scale: 0.95 
    },
    visible: { 
      opacity: 1, 
      y: 0, 
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 25,
        duration: 0.4
      }
    },
    exit: { 
      opacity: 0, 
      y: -20, 
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };

  const expandVariants = {
    collapsed: { height: 'auto', opacity: 1 },
    expanded: { 
      height: 'auto', 
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        variants={slideDownVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
        className={cn(
          "fixed top-4 right-4 z-50 max-w-sm",
          "fifa-gradient-bg", // FIFA black-to-red gradient
          className
        )}
        style={{
          background: status.isOnline 
            ? 'linear-gradient(135deg, #000000 0%, #1a1a1a 100%)'
            : 'linear-gradient(135deg, #000000 0%, #DC2626 100%)'
        }}
      >
        <Alert className="border-0 bg-transparent text-white shadow-2xl backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <Badge 
                variant={getStatusVariant()}
                className="fifa-badge"
              >
                {getStatusText()}
              </Badge>
              
              {!status.isOnline && (
                <Badge variant="outline" className="text-xs border-white/30 text-white/80">
                  {status.cachedPlacesCount} cached
                </Badge>
              )}
            </div>

            {/* Mobile toggle button */}
            {isMobile && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="h-6 w-6 p-0 text-white/80 hover:text-white hover:bg-white/10"
              >
                {isExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
              </Button>
            )}
          </div>

          {/* Expanded content */}
          <AnimatePresence>
            {(!isMobile || isExpanded) && (
              <motion.div
                variants={expandVariants}
                initial="collapsed"
                animate="expanded"
                exit="collapsed"
                className="mt-3 space-y-2"
              >
                <AlertDescription className="text-white/90 text-sm">
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center space-x-1">
                      <Database className="h-3 w-3" />
                      <span>{status.cachedPlacesCount} places</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span>🗺️</span>
                      <span>{status.cachedRoutesCount} routes</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="h-3 w-3" />
                      <span>{formatLastSync(status.lastSyncTime)}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span>💾</span>
                      <span>{formatCacheSize(status.cacheSize)}</span>
                    </div>
                  </div>

                  {status.pendingActionsCount > 0 && (
                    <div className="mt-2 p-2 bg-yellow-500/20 rounded border border-yellow-500/30">
                      <span className="text-yellow-200 text-xs">
                        {status.pendingActionsCount} actions queued for sync
                      </span>
                    </div>
                  )}
                </AlertDescription>

                {/* Action buttons */}
                <div className="flex space-x-2 pt-2">
                  {onSync && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onSync}
                      disabled={status.isSyncing}
                      className="text-xs border-white/30 text-white hover:bg-white/10 hover:text-white"
                    >
                      {status.isSyncing ? (
                        <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                      ) : (
                        <RefreshCw className="h-3 w-3 mr-1" />
                      )}
                      Sync
                    </Button>
                  )}
                  
                  {onClearCache && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onClearCache}
                      className="text-xs border-white/30 text-white hover:bg-white/10 hover:text-white"
                    >
                      Clear Cache
                    </Button>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </Alert>
      </motion.div>
    </AnimatePresence>
  );
}

// Custom CSS classes for FIFA theming (to be added to global CSS)
export const offlineIndicatorStyles = `
  .fifa-gradient-bg {
    background: linear-gradient(135deg, #000000 0%, #DC2626 100%);
    border: 1px solid rgba(255, 215, 0, 0.2);
  }
  
  .fifa-badge {
    background: rgba(255, 215, 0, 0.9);
    color: #000000;
    font-weight: 600;
  }
  
  .fifa-badge.destructive {
    background: rgba(220, 38, 38, 0.9);
    color: #ffffff;
  }
`;
