/**
 * Tests for OfflineIndicator Component
 * 
 * Validates FIFA Club World Cup 2025™ themed offline status indicator
 * with smooth animations and mobile UX patterns.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { OfflineIndicator } from '../OfflineIndicator';
import type { OfflineStatus } from '../OfflineIndicator';

// Mock Framer Motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  Wifi: () => <div data-testid="wifi-icon">Wifi</div>,
  WifiOff: () => <div data-testid="wifi-off-icon">WifiOff</div>,
  RefreshCw: () => <div data-testid="refresh-icon">RefreshCw</div>,
  Database: () => <div data-testid="database-icon">Database</div>,
  Clock: () => <div data-testid="clock-icon">Clock</div>,
  ChevronDown: () => <div data-testid="chevron-down-icon">ChevronDown</div>,
  ChevronUp: () => <div data-testid="chevron-up-icon">ChevronUp</div>,
}));

// Mock window.innerWidth for mobile detection
Object.defineProperty(window, 'innerWidth', {
  writable: true,
  configurable: true,
  value: 1024,
});

describe('OfflineIndicator', () => {
  const mockOnSync = vi.fn();
  const mockOnClearCache = vi.fn();

  const defaultOfflineStatus: OfflineStatus = {
    isOnline: false,
    isSyncing: false,
    cachedPlacesCount: 5,
    cachedRoutesCount: 3,
    lastSyncTime: new Date('2024-01-01T12:00:00Z'),
    pendingActionsCount: 2,
    cacheSize: 1024 * 1024, // 1MB
  };

  beforeEach(() => {
    vi.clearAllMocks();
    window.innerWidth = 1024; // Desktop by default
  });

  describe('visibility', () => {
    it('should not render when online and not syncing', () => {
      const onlineStatus: OfflineStatus = {
        ...defaultOfflineStatus,
        isOnline: true,
        isSyncing: false,
      };

      const { container } = render(
        <OfflineIndicator status={onlineStatus} />
      );

      expect(container.firstChild).toBeNull();
    });

    it('should render when offline', () => {
      render(
        <OfflineIndicator 
          status={defaultOfflineStatus}
          onSync={mockOnSync}
          onClearCache={mockOnClearCache}
        />
      );

      expect(screen.getByText('Offline')).toBeInTheDocument();
      expect(screen.getByTestId('wifi-off-icon')).toBeInTheDocument();
    });

    it('should render when online but syncing', () => {
      const syncingStatus: OfflineStatus = {
        ...defaultOfflineStatus,
        isOnline: true,
        isSyncing: true,
      };

      render(
        <OfflineIndicator status={syncingStatus} />
      );

      expect(screen.getByText('Syncing...')).toBeInTheDocument();
      expect(screen.getByTestId('refresh-icon')).toBeInTheDocument();
    });
  });

  describe('status display', () => {
    it('should display correct offline status information', () => {
      render(
        <OfflineIndicator status={defaultOfflineStatus} />
      );

      expect(screen.getByText('Offline')).toBeInTheDocument();
      expect(screen.getByText('5 cached')).toBeInTheDocument();
      expect(screen.getByText('5 places')).toBeInTheDocument();
      expect(screen.getByText('3 routes')).toBeInTheDocument();
      expect(screen.getByText('1.0MB')).toBeInTheDocument();
    });

    it('should display pending actions when present', () => {
      render(
        <OfflineIndicator status={defaultOfflineStatus} />
      );

      expect(screen.getByText('2 actions queued for sync')).toBeInTheDocument();
    });

    it('should format last sync time correctly', () => {
      render(
        <OfflineIndicator status={defaultOfflineStatus} />
      );

      // Should show relative time
      expect(screen.getByText(/ago/)).toBeInTheDocument();
    });

    it('should show "Never" when no last sync time', () => {
      const statusWithoutSync: OfflineStatus = {
        ...defaultOfflineStatus,
        lastSyncTime: null,
      };

      render(
        <OfflineIndicator status={statusWithoutSync} />
      );

      expect(screen.getByText('Never')).toBeInTheDocument();
    });
  });

  describe('mobile behavior', () => {
    beforeEach(() => {
      window.innerWidth = 600; // Mobile width
      window.dispatchEvent(new Event('resize'));
    });

    it('should show toggle button on mobile', async () => {
      render(
        <OfflineIndicator status={defaultOfflineStatus} />
      );

      await waitFor(() => {
        expect(screen.getByTestId('chevron-down-icon')).toBeInTheDocument();
      });
    });

    it('should toggle expanded state on mobile', async () => {
      render(
        <OfflineIndicator status={defaultOfflineStatus} />
      );

      const toggleButton = screen.getByRole('button');
      
      // Initially collapsed on mobile
      expect(screen.getByTestId('chevron-down-icon')).toBeInTheDocument();

      // Click to expand
      fireEvent.click(toggleButton);

      await waitFor(() => {
        expect(screen.getByTestId('chevron-up-icon')).toBeInTheDocument();
      });
    });
  });

  describe('actions', () => {
    it('should call onSync when sync button is clicked', () => {
      render(
        <OfflineIndicator 
          status={defaultOfflineStatus}
          onSync={mockOnSync}
        />
      );

      const syncButton = screen.getByText('Sync');
      fireEvent.click(syncButton);

      expect(mockOnSync).toHaveBeenCalledTimes(1);
    });

    it('should call onClearCache when clear cache button is clicked', () => {
      render(
        <OfflineIndicator 
          status={defaultOfflineStatus}
          onClearCache={mockOnClearCache}
        />
      );

      const clearButton = screen.getByText('Clear Cache');
      fireEvent.click(clearButton);

      expect(mockOnClearCache).toHaveBeenCalledTimes(1);
    });

    it('should disable sync button when syncing', () => {
      const syncingStatus: OfflineStatus = {
        ...defaultOfflineStatus,
        isSyncing: true,
      };

      render(
        <OfflineIndicator 
          status={syncingStatus}
          onSync={mockOnSync}
        />
      );

      const syncButton = screen.getByText('Sync');
      expect(syncButton).toBeDisabled();
    });
  });

  describe('FIFA theming', () => {
    it('should apply FIFA gradient background for offline state', () => {
      render(
        <OfflineIndicator status={defaultOfflineStatus} />
      );

      const container = screen.getByRole('alert').parentElement;
      expect(container).toHaveStyle({
        background: 'linear-gradient(135deg, #000000 0%, #DC2626 100%)'
      });
    });

    it('should apply different gradient for online syncing state', () => {
      const syncingStatus: OfflineStatus = {
        ...defaultOfflineStatus,
        isOnline: true,
        isSyncing: true,
      };

      render(
        <OfflineIndicator status={syncingStatus} />
      );

      const container = screen.getByRole('alert').parentElement;
      expect(container).toHaveStyle({
        background: 'linear-gradient(135deg, #000000 0%, #1a1a1a 100%)'
      });
    });
  });

  describe('cache size formatting', () => {
    it('should format bytes correctly', () => {
      const statusWithBytes: OfflineStatus = {
        ...defaultOfflineStatus,
        cacheSize: 512,
      };

      render(
        <OfflineIndicator status={statusWithBytes} />
      );

      expect(screen.getByText('512B')).toBeInTheDocument();
    });

    it('should format kilobytes correctly', () => {
      const statusWithKB: OfflineStatus = {
        ...defaultOfflineStatus,
        cacheSize: 1536, // 1.5KB
      };

      render(
        <OfflineIndicator status={statusWithKB} />
      );

      expect(screen.getByText('1.5KB')).toBeInTheDocument();
    });

    it('should format megabytes correctly', () => {
      const statusWithMB: OfflineStatus = {
        ...defaultOfflineStatus,
        cacheSize: 2 * 1024 * 1024, // 2MB
      };

      render(
        <OfflineIndicator status={statusWithMB} />
      );

      expect(screen.getByText('2.0MB')).toBeInTheDocument();
    });
  });

  describe('accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(
        <OfflineIndicator status={defaultOfflineStatus} />
      );

      expect(screen.getByRole('alert')).toBeInTheDocument();
    });

    it('should have keyboard accessible buttons', () => {
      render(
        <OfflineIndicator 
          status={defaultOfflineStatus}
          onSync={mockOnSync}
        />
      );

      const syncButton = screen.getByText('Sync');
      expect(syncButton).toHaveAttribute('type', 'button');
    });
  });
});
