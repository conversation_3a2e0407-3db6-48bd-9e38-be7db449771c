/**
 * Offline Demo Component
 * 
 * Demonstrates the offline functionality implementation for FIFA Club World Cup 2025™.
 * Features:
 * - Live offline status indicator
 * - Cache statistics display
 * - Manual sync controls
 * - Service worker status
 * - Queue management
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Wifi, 
  WifiOff, 
  Database, 
  RefreshCw, 
  Trash2, 
  Settings, 
  Activity,
  Clock,
  HardDrive
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { OfflineIndicator } from './OfflineIndicator';
import { useOfflineStatus, useServiceWorkerRegistration } from '~/hooks/useOfflineStatus';
import { useOfflineSync } from '~/hooks/useOfflineSync';

export function OfflineDemo() {
  const { 
    offlineStatus, 
    isServiceWorkerReady, 
    cacheStats, 
    getCacheStats, 
    clearCache, 
    forceBgSync,
    formatCacheSize 
  } = useOfflineStatus();
  
  const { 
    registration, 
    isRegistering, 
    registrationError 
  } = useServiceWorkerRegistration();
  
  const { 
    queueAction, 
    clearQueue, 
    queue 
  } = useOfflineSync();

  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefreshStats = async () => {
    setIsRefreshing(true);
    await getCacheStats();
    setTimeout(() => setIsRefreshing(false), 500);
  };

  const handleTestOfflineAction = () => {
    queueAction('place-search', {
      query: 'FIFA Stadium',
      location: { lat: 25.2854, lng: 51.5310 }, // Qatar coordinates
      timestamp: Date.now()
    });
  };

  const getCacheUsagePercentage = () => {
    if (!cacheStats) return 0;
    const maxSize = 50 * 1024 * 1024; // 50MB
    return (cacheStats.totalSize / maxSize) * 100;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black p-6">
      {/* FIFA Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center mb-8"
      >
        <h1 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-red-600 mb-2">
          FIFA Club World Cup 2025™
        </h1>
        <p className="text-white/80 text-lg">Offline Support Implementation Demo</p>
      </motion.div>

      {/* Offline Indicator */}
      <OfflineIndicator 
        status={offlineStatus}
        onSync={forceBgSync}
        onClearCache={() => clearCache()}
        className="mb-6"
      />

      <div className="max-w-6xl mx-auto space-y-6">
        {/* Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-black/50 border-yellow-500/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white">Connection Status</CardTitle>
              {offlineStatus.isOnline ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {offlineStatus.isOnline ? 'Online' : 'Offline'}
              </div>
              <p className="text-xs text-white/60">
                {offlineStatus.isSyncing ? 'Syncing...' : 'Ready'}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-black/50 border-yellow-500/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white">Cached Data</CardTitle>
              <Database className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {offlineStatus.cachedPlacesCount + offlineStatus.cachedRoutesCount}
              </div>
              <p className="text-xs text-white/60">
                {offlineStatus.cachedPlacesCount} places, {offlineStatus.cachedRoutesCount} routes
              </p>
            </CardContent>
          </Card>

          <Card className="bg-black/50 border-yellow-500/30">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-white">Pending Actions</CardTitle>
              <Clock className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {offlineStatus.pendingActionsCount}
              </div>
              <p className="text-xs text-white/60">
                Queued for sync
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Information */}
        <Tabs defaultValue="cache" className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-black/50">
            <TabsTrigger value="cache" className="text-white data-[state=active]:bg-yellow-500 data-[state=active]:text-black">
              Cache Stats
            </TabsTrigger>
            <TabsTrigger value="queue" className="text-white data-[state=active]:bg-yellow-500 data-[state=active]:text-black">
              Sync Queue
            </TabsTrigger>
            <TabsTrigger value="service-worker" className="text-white data-[state=active]:bg-yellow-500 data-[state=active]:text-black">
              Service Worker
            </TabsTrigger>
            <TabsTrigger value="controls" className="text-white data-[state=active]:bg-yellow-500 data-[state=active]:text-black">
              Controls
            </TabsTrigger>
          </TabsList>

          <TabsContent value="cache" className="space-y-4">
            <Card className="bg-black/50 border-yellow-500/30">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white">Cache Statistics</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefreshStats}
                    disabled={isRefreshing}
                    className="border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/10"
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {cacheStats ? (
                  <>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-white/80">Cache Usage</span>
                        <span className="text-white">{formatCacheSize(cacheStats.totalSize)} / 50MB</span>
                      </div>
                      <Progress value={getCacheUsagePercentage()} className="h-2" />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      {Object.entries(cacheStats.caches).map(([name, stats]) => (
                        <div key={name} className="p-3 bg-white/5 rounded-lg">
                          <div className="text-sm font-medium text-white">{name}</div>
                          <div className="text-xs text-white/60">
                            {stats.count} items • {formatCacheSize(stats.size)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8 text-white/60">
                    No cache statistics available
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="queue" className="space-y-4">
            <Card className="bg-black/50 border-yellow-500/30">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white">Sync Queue ({queue.length})</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearQueue}
                    className="border-red-500/50 text-red-400 hover:bg-red-500/10"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear Queue
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {queue.length > 0 ? (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {queue.map((action) => (
                      <div key={action.id} className="p-3 bg-white/5 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <Badge variant="outline" className="text-xs">
                              {action.type}
                            </Badge>
                            <div className="text-sm text-white/80 mt-1">
                              Retry {action.retryCount}/{action.maxRetries}
                            </div>
                          </div>
                          <div className="text-xs text-white/60">
                            {new Date(action.timestamp).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-white/60">
                    No actions in queue
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="service-worker" className="space-y-4">
            <Card className="bg-black/50 border-yellow-500/30">
              <CardHeader>
                <CardTitle className="text-white">Service Worker Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm text-white/80">Registration Status</div>
                    <Badge variant={registration ? "default" : "destructive"}>
                      {registration ? "Registered" : "Not Registered"}
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-sm text-white/80">Ready Status</div>
                    <Badge variant={isServiceWorkerReady ? "default" : "secondary"}>
                      {isServiceWorkerReady ? "Ready" : "Not Ready"}
                    </Badge>
                  </div>
                </div>

                {registrationError && (
                  <div className="p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
                    <div className="text-sm text-red-400">Error: {registrationError}</div>
                  </div>
                )}

                {isRegistering && (
                  <div className="text-center py-4">
                    <RefreshCw className="h-6 w-6 animate-spin mx-auto text-yellow-400" />
                    <div className="text-sm text-white/60 mt-2">Registering Service Worker...</div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="controls" className="space-y-4">
            <Card className="bg-black/50 border-yellow-500/30">
              <CardHeader>
                <CardTitle className="text-white">Test Controls</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    onClick={handleTestOfflineAction}
                    className="bg-yellow-500 text-black hover:bg-yellow-600"
                  >
                    <Activity className="h-4 w-4 mr-2" />
                    Test Offline Action
                  </Button>
                  
                  <Button
                    onClick={forceBgSync}
                    variant="outline"
                    className="border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/10"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Force Sync
                  </Button>
                  
                  <Button
                    onClick={() => clearCache()}
                    variant="outline"
                    className="border-red-500/50 text-red-400 hover:bg-red-500/10"
                  >
                    <HardDrive className="h-4 w-4 mr-2" />
                    Clear All Cache
                  </Button>
                  
                  <Button
                    onClick={clearQueue}
                    variant="outline"
                    className="border-orange-500/50 text-orange-400 hover:bg-orange-500/10"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear Queue
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
