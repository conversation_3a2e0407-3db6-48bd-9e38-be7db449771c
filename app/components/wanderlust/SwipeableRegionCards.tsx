/**
 * SwipeableRegionCards Component for WL-004 Mobile Navigation Patterns
 * 
 * Horizontal swipe navigation for region selection on mobile with snap-to-card
 * behavior, touch indicators, and accessibility fallback.
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '~/lib/utils';
import { useGestures } from '~/hooks/useGestures';
import type { SwipeableRegionCardsProps } from '~/types/wanderlust';
import {
  clamp,
  findClosestSnapPoint,
  calculateSnapWithVelocity,
  animateValue,
  prefersReducedMotion,
} from '~/lib/gesture-utils';

const CARD_WIDTH = 280; // Width of each card in pixels
const CARD_GAP = 16; // Gap between cards in pixels

export function SwipeableRegionCards({
  regions,
  currentRegion,
  onRegionChange,
  className,
}: SwipeableRegionCardsProps) {
  const [scrollOffset, setScrollOffset] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const cancelAnimationRef = useRef<(() => void) | null>(null);

  // Calculate snap positions for each card
  const getSnapPositions = useCallback(() => {
    return regions.map((_, index) => -(index * (CARD_WIDTH + CARD_GAP)));
  }, [regions]);

  // Get the maximum scroll offset (negative value)
  const getMaxOffset = useCallback(() => {
    if (!containerRef.current) return 0;
    const containerWidth = containerRef.current.clientWidth;
    const totalWidth = regions.length * (CARD_WIDTH + CARD_GAP) - CARD_GAP;
    return Math.min(0, containerWidth - totalWidth);
  }, [regions.length]);

  // Animate to a specific offset
  const animateToOffset = useCallback(
    (targetOffset: number, onComplete?: () => void) => {
      if (prefersReducedMotion()) {
        setScrollOffset(targetOffset);
        onComplete?.();
        return;
      }

      // Cancel any existing animation
      if (cancelAnimationRef.current) {
        cancelAnimationRef.current();
      }

      setIsAnimating(true);

      cancelAnimationRef.current = animateValue(
        scrollOffset,
        targetOffset,
        300, // 300ms following established animation standards
        (value) => {
          setScrollOffset(value);
        },
        () => {
          setIsAnimating(false);
          onComplete?.();
        }
      );
    },
    [scrollOffset]
  );

  // Snap to the nearest card
  const snapToNearestCard = useCallback(
    (velocity = 0) => {
      const snapPositions = getSnapPositions();
      const targetOffset = calculateSnapWithVelocity(
        scrollOffset,
        velocity,
        snapPositions,
        0.5 // velocity threshold
      );

      const clampedOffset = clamp(targetOffset, getMaxOffset(), 0);
      animateToOffset(clampedOffset);

      // Find which region this offset corresponds to
      const cardIndex = snapPositions.findIndex(pos => Math.abs(pos - clampedOffset) < 10);
      if (cardIndex >= 0 && cardIndex < regions.length) {
        const region = regions[cardIndex];
        if (region.id !== currentRegion) {
          onRegionChange(region.id);
        }
      }
    },
    [scrollOffset, getSnapPositions, getMaxOffset, animateToOffset, regions, currentRegion, onRegionChange]
  );

  // Handle swipe gestures
  const { ref: gestureRef } = useGestures({
    onSwipeLeft: () => {
      // Swipe left = move to next card (more negative offset)
      const snapPositions = getSnapPositions();
      const currentIndex = snapPositions.findIndex(pos => Math.abs(pos - scrollOffset) < 50);
      const nextIndex = Math.min(currentIndex + 1, snapPositions.length - 1);
      
      if (nextIndex !== currentIndex) {
        const targetOffset = clamp(snapPositions[nextIndex], getMaxOffset(), 0);
        animateToOffset(targetOffset);
        onRegionChange(regions[nextIndex].id);
      }
    },
    onSwipeRight: () => {
      // Swipe right = move to previous card (less negative offset)
      const snapPositions = getSnapPositions();
      const currentIndex = snapPositions.findIndex(pos => Math.abs(pos - scrollOffset) < 50);
      const prevIndex = Math.max(currentIndex - 1, 0);
      
      if (prevIndex !== currentIndex) {
        const targetOffset = snapPositions[prevIndex];
        animateToOffset(targetOffset);
        onRegionChange(regions[prevIndex].id);
      }
    },
    onDragStart: () => {
      setIsDragging(true);
      // Cancel any ongoing animation
      if (cancelAnimationRef.current) {
        cancelAnimationRef.current();
        setIsAnimating(false);
      }
    },
    onDragMove: ({ deltaX }) => {
      if (!isDragging) return;

      const newOffset = clamp(scrollOffset + deltaX, getMaxOffset(), 0);
      setScrollOffset(newOffset);
    },
    onDragEnd: ({ velocity }) => {
      setIsDragging(false);
      snapToNearestCard(velocity);
    },
    config: {
      swipeThreshold: 44, // 44px minimum for touch accessibility
      velocityThreshold: 0.3,
    },
  });

  // Auto-scroll to active region when it changes externally
  useEffect(() => {
    const activeIndex = regions.findIndex(r => r.id === currentRegion);
    if (activeIndex >= 0) {
      const snapPositions = getSnapPositions();
      const targetOffset = clamp(snapPositions[activeIndex], getMaxOffset(), 0);
      
      // Only animate if we're not currently dragging
      if (!isDragging) {
        animateToOffset(targetOffset);
      }
    }
  }, [currentRegion, regions, getSnapPositions, getMaxOffset, animateToOffset, isDragging]);

  // Navigation button handlers
  const handlePrevious = useCallback(() => {
    const activeIndex = regions.findIndex(r => r.id === currentRegion);
    if (activeIndex > 0) {
      onRegionChange(regions[activeIndex - 1].id);
    }
  }, [regions, currentRegion, onRegionChange]);

  const handleNext = useCallback(() => {
    const activeIndex = regions.findIndex(r => r.id === currentRegion);
    if (activeIndex < regions.length - 1) {
      onRegionChange(regions[activeIndex + 1].id);
    }
  }, [regions, currentRegion, onRegionChange]);

  // Cleanup animation on unmount
  useEffect(() => {
    return () => {
      if (cancelAnimationRef.current) {
        cancelAnimationRef.current();
      }
    };
  }, []);

  const activeIndex = regions.findIndex(r => r.id === currentRegion);

  return (
    <div className={cn('relative', className)}>
      {/* Navigation Buttons - Accessibility Fallback */}
      <button
        onClick={handlePrevious}
        disabled={activeIndex <= 0}
        className={cn(
          'absolute left-2 top-1/2 transform -translate-y-1/2 z-10',
          'bg-white shadow-lg rounded-full p-2 min-w-[44px] min-h-[44px]',
          'flex items-center justify-center transition-opacity',
          activeIndex <= 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
        )}
        aria-label="Previous region"
      >
        <ChevronLeft className="h-5 w-5 text-gray-600" />
      </button>

      <button
        onClick={handleNext}
        disabled={activeIndex >= regions.length - 1}
        className={cn(
          'absolute right-2 top-1/2 transform -translate-y-1/2 z-10',
          'bg-white shadow-lg rounded-full p-2 min-w-[44px] min-h-[44px]',
          'flex items-center justify-center transition-opacity',
          activeIndex >= regions.length - 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
        )}
        aria-label="Next region"
      >
        <ChevronRight className="h-5 w-5 text-gray-600" />
      </button>

      {/* Swipeable Container */}
      <div
        ref={(node) => {
          containerRef.current = node;
          gestureRef.current = node;
        }}
        className="overflow-hidden touch-manipulation"
        style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
      >
        <div
          className="flex gap-4 transition-transform"
          style={{
            transform: `translateX(${scrollOffset}px)`,
            transition: isDragging || isAnimating ? 'none' : 'transform 300ms ease-out',
          }}
        >
          {regions.map((region) => (
            <RegionCard
              key={region.id}
              region={region}
              isActive={region.id === currentRegion}
              onClick={() => onRegionChange(region.id)}
            />
          ))}
        </div>
      </div>

      {/* Touch Indicators */}
      <div className="flex justify-center mt-4 gap-2">
        {regions.map((_, index) => (
          <button
            key={index}
            onClick={() => onRegionChange(regions[index].id)}
            className={cn(
              'w-2 h-2 rounded-full transition-colors min-w-[32px] min-h-[32px] flex items-center justify-center',
              index === activeIndex ? 'bg-blue-500' : 'bg-gray-300 hover:bg-gray-400'
            )}
            aria-label={`Go to ${regions[index].name}`}
          >
            <span className="sr-only">{regions[index].name}</span>
          </button>
        ))}
      </div>
    </div>
  );
}

interface RegionCardProps {
  region: any;
  isActive: boolean;
  onClick: () => void;
}

function RegionCard({ region, isActive, onClick }: RegionCardProps) {
  return (
    <div
      onClick={onClick}
      className={cn(
        'flex-shrink-0 p-6 bg-white rounded-xl shadow-md cursor-pointer transition-all duration-300 border-2 overflow-hidden group',
        'min-w-[280px]', // Ensure consistent card width
        isActive
          ? 'border-blue-500 bg-gradient-to-br from-blue-500 to-purple-600 text-white transform -translate-y-1 shadow-lg'
          : 'border-transparent hover:border-blue-200 hover:-translate-y-1 hover:shadow-lg'
      )}
    >
      {/* Card content - same as original CityCard */}
      <div className="flex items-center mb-3">
        <span className="text-2xl mr-3">{region.icon}</span>
        <h3 className={cn(
          'text-lg font-semibold',
          isActive ? 'text-white' : 'text-gray-900'
        )}>
          {region.name}
        </h3>
      </div>

      <p className={cn(
        'text-sm mb-4',
        isActive ? 'text-white/90' : 'text-gray-600'
      )}>
        {region.description}
      </p>

      <div className="flex items-center justify-between text-xs">
        <div className={cn(
          'flex items-center space-x-1',
          isActive ? 'text-white/80' : 'text-gray-500'
        )}>
          <span>📍</span>
          <span>{region.places.length} places</span>
        </div>

        <div className={cn(
          'px-2 py-1 rounded-full text-xs font-medium',
          isActive
            ? 'bg-white/20 text-white'
            : 'bg-gray-100 text-gray-600'
        )}>
          {isActive ? 'Active' : 'Explore'}
        </div>
      </div>
    </div>
  );
}
