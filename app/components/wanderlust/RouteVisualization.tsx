/**
 * Route Visualization Component - Simplified Wrapper
 *
 * Now uses the new RouteLayer component from the simplified architecture.
 * Maintains backward compatibility while leveraging Routes v2 integration.
 */

import React from 'react';
import { RouteLayer } from '~/components/maps/MapFeatures/RouteLayer';
import { useWanderlustStore } from '~/stores/wanderlust';
import { useRoutesV2 } from '~/hooks/useRoutesV2';
import { useMapState } from '~/hooks/useMapState';
import { placesToWaypoints } from '~/lib/route-planning-utils';
import type { VisitedPlace } from '~/types/wanderlust';

interface RouteVisualizationProps {
  map?: google.maps.Map;
  showAlternatives?: boolean;
  showWaypoints?: boolean;
}

export function RouteVisualization({
  map,
  showAlternatives = true,
  showWaypoints = true
}: RouteVisualizationProps) {
  const { currentRoute, itinerary } = useWanderlustStore();
  const { currentRoute: routesV2Route, alternativeRoutes } = useRoutesV2();

  // Convert itinerary to waypoints for RouteLayer
  const waypoints = React.useMemo(() => {
    if (itinerary.length === 0) return [];
    return placesToWaypoints(itinerary);
  }, [itinerary]);

  // Use Routes v2 route if available, otherwise fall back to legacy route
  const activeRoute = routesV2Route || currentRoute;

  // Handle route click
  const handleRouteClick = React.useCallback((route: any) => {
    console.log('🗺️ Route clicked in RouteVisualization:', route);
  }, []);

  // Handle waypoint click
  const handleWaypointClick = React.useCallback((waypoint: any, index: number) => {
    console.log('📍 Waypoint clicked in RouteVisualization:', waypoint, index);
  }, []);

  // Don't render if no route
  if (!activeRoute) {
    return null;
  }

  // Use the new RouteLayer component for simplified rendering
  return (
    <RouteLayer
      route={activeRoute}
      alternativeRoutes={alternativeRoutes}
      waypoints={waypoints}
      onRouteClick={handleRouteClick}
      onWaypointClick={handleWaypointClick}
      showAlternatives={showAlternatives}
      showWaypoints={showWaypoints}
    />
  );
}

/**
 * Hook to manage route visualization state - Simplified
 */
export function useRouteVisualization() {
  const { currentRoute } = useWanderlustStore();
  const { panTo } = useMapState();

  const focusOnRoute = React.useCallback(() => {
    if (!currentRoute || !currentRoute.bounds) return;

    const center = {
      lat: (currentRoute.bounds.north + currentRoute.bounds.south) / 2,
      lng: (currentRoute.bounds.east + currentRoute.bounds.west) / 2,
    };

    // Calculate appropriate zoom level based on bounds
    const latDiff = currentRoute.bounds.north - currentRoute.bounds.south;
    const lngDiff = currentRoute.bounds.east - currentRoute.bounds.west;
    const maxDiff = Math.max(latDiff, lngDiff);

    let zoom = 10;
    if (maxDiff < 0.01) zoom = 15;
    else if (maxDiff < 0.05) zoom = 13;
    else if (maxDiff < 0.1) zoom = 11;
    else if (maxDiff < 0.5) zoom = 9;
    else zoom = 7;

    panTo(center.lat, center.lng, zoom);
  }, [currentRoute, panTo]);

  return {
    focusOnRoute,
  };
}
