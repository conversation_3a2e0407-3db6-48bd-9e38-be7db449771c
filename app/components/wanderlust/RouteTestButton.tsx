/**
 * Simple test component to verify route planning integration
 */

import { Button } from '~/components/ui/button';
import { useWanderlustStore } from '~/stores/wanderlust';

export function RouteTestButton() {
  const { 
    itinerary, 
    addToItinerary, 
    calculateRouteFromItinerary, 
    routePlanning,
    currentRoute 
  } = useWanderlustStore();

  const handleAddTestPlaces = () => {
    // Add some test places to the itinerary
    const testPlaces = [
      {
        id: 'test-1',
        name: 'Test Place 1',
        description: { en: 'First test location', fr: 'Premier lieu de test', ar: 'الموقع التجريبي الأول' },
        category: 'landmark' as const,
        coordinates: { latitude: 40.7589, longitude: -73.9851 },
        city: 'New York',
        region: 'nyc',
        rating: 4,
        revisitPotential: 'Highly Recommend' as const,
        keyTakeaway: 'Great starting point',
        personalNotes: '',
        icon: '🗽'
      },
      {
        id: 'test-2',
        name: 'Test Place 2',
        description: { en: 'Second test location', fr: 'Deuxième lieu de test', ar: 'الموقع التجريبي الثاني' },
        category: 'food' as const,
        coordinates: { latitude: 40.7614, longitude: -73.9776 },
        city: 'New York',
        region: 'nyc',
        rating: 5,
        revisitPotential: 'Highly Recommend' as const,
        keyTakeaway: 'Amazing food',
        personalNotes: '',
        icon: '🍕'
      }
    ];

    testPlaces.forEach(place => addToItinerary(place));
  };

  const handleCalculateRoute = async () => {
    if (itinerary.length >= 2) {
      await calculateRouteFromItinerary();
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 space-y-2">
      <div className="bg-white p-4 rounded-lg shadow-lg border max-w-xs">
        <h3 className="font-semibold mb-2">Route Planning Test</h3>
        
        <div className="space-y-2 text-sm">
          <div>Itinerary: {itinerary.length} places</div>
          <div>Planning: {routePlanning.isPlanning ? 'Yes' : 'No'}</div>
          <div>Route: {currentRoute ? 'Calculated' : 'None'}</div>
          {routePlanning.planningError && (
            <div className="text-red-600">Error: {routePlanning.planningError}</div>
          )}
        </div>

        <div className="space-y-2 mt-3">
          <Button 
            onClick={handleAddTestPlaces} 
            size="sm" 
            className="w-full"
            disabled={itinerary.length >= 2}
          >
            Add Test Places
          </Button>
          
          <Button 
            onClick={handleCalculateRoute} 
            size="sm" 
            className="w-full"
            disabled={itinerary.length < 2 || routePlanning.isPlanning}
          >
            {routePlanning.isPlanning ? 'Calculating...' : 'Calculate Route'}
          </Button>
        </div>
      </div>
    </div>
  );
}
