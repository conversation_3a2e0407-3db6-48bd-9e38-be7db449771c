import { useState } from 'react';
import { X, Navigation, Clock, MapPin, Route } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { useWanderlustStore } from '~/stores/wanderlust';

export function DirectionsPanel() {
  const [isCalculating, setIsCalculating] = useState(false);

  const {
    showDirections,
    currentRoute,
    itinerary,
    setShowDirections,
    setCurrentRoute,
    clearItinerary,
  } = useWanderlustStore();

  const handleClose = () => {
    setShowDirections(false);
    setCurrentRoute(null);
  };

  const calculateRoute = async () => {
    if (itinerary.length < 2) return;

    setIsCalculating(true);

    try {
      // Simulate route calculation
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock route data
      const mockRoute = {
        id: 'route-1',
        name: 'My Travel Route',
        waypoints: itinerary.map(place => ({
          location: {
            lat: place.coordinates.latitude,
            lng: place.coordinates.longitude,
          },
          stopover: true,
        })),
        optimized: true,
        travelMode: 'DRIVING' as any,
        estimatedDuration: '2 hours 30 minutes',
        estimatedDistance: '45.2 km',
      };

      setCurrentRoute(mockRoute);
    } catch (error) {
      console.error('Error calculating route:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const optimizeRoute = () => {
    // This would implement route optimization logic
    console.log('Optimizing route...');
  };

  if (!showDirections) return null;

  return (
    <>
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black/50 z-40"
        onClick={handleClose}
      />

      {/* Panel */}
      <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-full max-w-md mx-4">
        <Card className="shadow-2xl">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center">
                <Route className="h-5 w-5 mr-2" />
                Directions & Route Planning
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Itinerary Summary */}
            <div>
              <h3 className="font-medium text-sm mb-2">Your Itinerary</h3>
              {itinerary.length === 0 ? (
                <p className="text-sm text-gray-500">
                  Add places to your itinerary to plan a route
                </p>
              ) : (
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {itinerary.map((place, index) => (
                    <div key={place.id} className="flex items-center text-sm">
                      <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-2">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{place.name}</div>
                        <div className="text-gray-500 text-xs">{place.city}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Route Actions */}
            {itinerary.length >= 2 && (
              <div className="space-y-2">
                <Button
                  onClick={calculateRoute}
                  disabled={isCalculating}
                  className="w-full"
                >
                  {isCalculating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Calculating Route...
                    </>
                  ) : (
                    <>
                      <Navigation className="h-4 w-4 mr-2" />
                      Calculate Route
                    </>
                  )}
                </Button>

                {currentRoute && (
                  <Button
                    variant="outline"
                    onClick={optimizeRoute}
                    className="w-full"
                  >
                    <Route className="h-4 w-4 mr-2" />
                    Optimize Route
                  </Button>
                )}
              </div>
            )}

            {/* Route Information */}
            {currentRoute && (
              <div className="bg-gray-50 rounded-lg p-3">
                <h4 className="font-medium text-sm mb-2">Route Details</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="flex items-center text-gray-600">
                      <Clock className="h-4 w-4 mr-1" />
                      Duration
                    </span>
                    <span className="font-medium">{currentRoute.estimatedDuration}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="flex items-center text-gray-600">
                      <MapPin className="h-4 w-4 mr-1" />
                      Distance
                    </span>
                    <span className="font-medium">{currentRoute.estimatedDistance}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Travel Mode</span>
                    <Badge variant="outline">
                      {currentRoute.travelMode === 'DRIVING' ? 'Driving' : 'Walking'}
                    </Badge>
                  </div>
                </div>
              </div>
            )}

            {/* Turn-by-turn directions would go here */}
            {currentRoute && (
              <div className="bg-blue-50 rounded-lg p-3">
                <h4 className="font-medium text-sm mb-2 text-blue-900">
                  Turn-by-turn Directions
                </h4>
                <div className="space-y-1 text-sm text-blue-800">
                  <div>1. Head north on Main St</div>
                  <div>2. Turn right onto Broadway</div>
                  <div>3. Continue for 2.5 km</div>
                  <div className="text-xs text-blue-600 mt-2">
                    Full directions will be displayed here
                  </div>
                </div>
              </div>
            )}

            {/* Clear Itinerary */}
            {itinerary.length > 0 && (
              <Button
                variant="destructive"
                onClick={clearItinerary}
                className="w-full"
                size="sm"
              >
                Clear Itinerary
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    </>
  );
}
