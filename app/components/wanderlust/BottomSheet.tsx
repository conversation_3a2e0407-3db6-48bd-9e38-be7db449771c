/**
 * BottomSheet Component for WL-004 Mobile Navigation Patterns
 * 
 * Gesture-driven bottom sheet with multiple snap points, backdrop blur,
 * and keyboard avoidance following mobile-first architecture standards.
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { X } from 'lucide-react';
import { cn } from '~/lib/utils';
import { useGestures } from '~/hooks/useGestures';
import type { BottomSheetProps } from '~/types/wanderlust';
import {
  clamp,
  findClosestSnapPoint,
  calculateSnapWithVelocity,
  animateValue,
  prefersReducedMotion,
} from '~/lib/gesture-utils';

const DEFAULT_SNAP_POINTS = [0.3, 0.6, 0.9]; // 30%, 60%, 90% of viewport height

export function BottomSheet({
  isOpen,
  onClose,
  children,
  snapPoints = DEFAULT_SNAP_POINTS,
  initialSnapPoint = 0.6,
  className,
  backdropClassName,
  onSnapPointChange,
}: BottomSheetProps) {
  const [currentHeight, setCurrentHeight] = useState(initialSnapPoint);
  const [isDragging, setIsDragging] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const sheetRef = useRef<HTMLDivElement>(null);
  const backdropRef = useRef<HTMLDivElement>(null);
  const cancelAnimationRef = useRef<(() => void) | null>(null);

  // Calculate actual pixel heights from viewport percentages
  const getPixelHeight = useCallback((percentage: number) => {
    return percentage * window.innerHeight;
  }, []);

  // Animate to a specific snap point
  const animateToSnapPoint = useCallback(
    (targetHeight: number, onComplete?: () => void) => {
      if (prefersReducedMotion()) {
        setCurrentHeight(targetHeight);
        onComplete?.();
        return;
      }

      // Cancel any existing animation
      if (cancelAnimationRef.current) {
        cancelAnimationRef.current();
      }

      setIsAnimating(true);

      cancelAnimationRef.current = animateValue(
        currentHeight,
        targetHeight,
        300, // 300ms following established animation standards
        (value) => {
          setCurrentHeight(value);
        },
        () => {
          setIsAnimating(false);
          onComplete?.();
          onSnapPointChange?.(targetHeight);
        }
      );
    },
    [currentHeight, onSnapPointChange]
  );

  // Handle drag gestures
  const { ref: gestureRef } = useGestures({
    onDragStart: () => {
      setIsDragging(true);
      // Cancel any ongoing animation
      if (cancelAnimationRef.current) {
        cancelAnimationRef.current();
        setIsAnimating(false);
      }
    },
    onDragMove: ({ deltaY }) => {
      if (!isDragging) return;

      // Convert pixel delta to percentage
      const deltaPercentage = -deltaY / window.innerHeight;
      const newHeight = clamp(currentHeight + deltaPercentage, 0, 1);
      setCurrentHeight(newHeight);
    },
    onDragEnd: ({ deltaY, velocity }) => {
      setIsDragging(false);

      // Convert velocity to percentage per second
      const velocityPercentage = -velocity / window.innerHeight;

      // Find the best snap point considering velocity
      const targetSnapPoint = calculateSnapWithVelocity(
        currentHeight,
        velocityPercentage,
        snapPoints,
        0.5 // velocity threshold
      );

      // If dragging down with high velocity and at lowest snap point, close
      if (
        velocityPercentage < -0.5 &&
        currentHeight <= Math.min(...snapPoints) + 0.1
      ) {
        onClose();
        return;
      }

      animateToSnapPoint(targetSnapPoint);
    },
    config: {
      swipeThreshold: 10, // Lower threshold for drag sensitivity
      velocityThreshold: 0.1,
    },
  });

  // Handle backdrop click
  const handleBackdropClick = useCallback(
    (event: React.MouseEvent) => {
      if (event.target === backdropRef.current) {
        onClose();
      }
    },
    [onClose]
  );

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when sheet is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  // Reset height when opening
  useEffect(() => {
    if (isOpen) {
      setCurrentHeight(initialSnapPoint);
    }
  }, [isOpen, initialSnapPoint]);

  // Cleanup animation on unmount
  useEffect(() => {
    return () => {
      if (cancelAnimationRef.current) {
        cancelAnimationRef.current();
      }
    };
  }, []);

  if (!isOpen) return null;

  const pixelHeight = getPixelHeight(currentHeight);
  const opacity = clamp(currentHeight / 0.3, 0, 1); // Fade in/out based on height

  return (
    <div className="fixed inset-0 z-50 md:hidden">
      {/* Backdrop */}
      <div
        ref={backdropRef}
        className={cn(
          'absolute inset-0 bg-black transition-opacity duration-300',
          backdropClassName
        )}
        style={{ opacity: opacity * 0.5 }}
        onClick={handleBackdropClick}
      />

      {/* Sheet */}
      <div
        ref={(node) => {
          sheetRef.current = node;
          gestureRef.current = node;
        }}
        className={cn(
          'absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-2xl',
          'border-t border-gray-200 overflow-hidden',
          'touch-manipulation', // Enable touch optimizations
          className
        )}
        style={{
          height: `${pixelHeight}px`,
          transform: isDragging ? 'none' : undefined,
          transition: isDragging || isAnimating ? 'none' : 'height 300ms ease-out',
        }}
      >
        {/* Drag Handle */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-12 h-1 bg-gray-300 rounded-full" />
        </div>

        {/* Header */}
        <div className="flex items-center justify-between px-4 pb-3 border-b border-gray-100">
          <div className="w-6" /> {/* Spacer for centering */}
          <h2 className="text-lg font-semibold text-gray-900">Place Details</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors min-w-[32px] min-h-[32px] flex items-center justify-center"
            aria-label="Close"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {children}
        </div>

        {/* Snap Point Indicators */}
        <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex flex-col gap-2">
          {snapPoints.map((point, index) => (
            <button
              key={index}
              onClick={() => animateToSnapPoint(point)}
              className={cn(
                'w-2 h-2 rounded-full transition-colors',
                Math.abs(currentHeight - point) < 0.1
                  ? 'bg-blue-500'
                  : 'bg-gray-300 hover:bg-gray-400'
              )}
              aria-label={`Snap to ${Math.round(point * 100)}% height`}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
