import { useEffect, useState } from 'react';

interface HydrationSafeWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * A wrapper component that prevents hydration mismatches caused by:
 * - Browser extensions adding attributes to DOM elements
 * - Client-side only features
 * - Dynamic content that differs between server and client
 */
export function HydrationSafeWrapper({ children, fallback }: HydrationSafeWrapperProps) {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // This effect only runs on the client side after hydration
    setIsHydrated(true);

    // Clean up any browser extension attributes that might cause hydration issues
    const cleanupExtensionAttributes = () => {
      const body = document.body;
      const html = document.documentElement;

      // List of known browser extension attributes that cause hydration issues
      const extensionAttributes = [
        'data-lt-installed',
        'data-qb-installed',
        'data-new-gr-c-s-check-loaded',
        'data-gr-ext-installed',
        'data-new-gr-c-s-loaded',
        'data-grammarly-shadow-root',
        'cz-shortcut-listen',
        'data-darkreader-mode',
        'data-darkreader-scheme',
      ];

      // Remove extension attributes from html and body
      [html, body].forEach(element => {
        if (element) {
          extensionAttributes.forEach(attr => {
            if (element.hasAttribute(attr)) {
              element.removeAttribute(attr);
            }
          });
        }
      });
    };

    // Clean up immediately and set up observer for future changes
    cleanupExtensionAttributes();

    // Set up a mutation observer to clean up extension attributes as they're added
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes') {
          const target = mutation.target as Element;
          const attributeName = mutation.attributeName;

          if (attributeName && (
            attributeName.startsWith('data-lt-') ||
            attributeName.startsWith('data-qb-') ||
            attributeName.startsWith('data-gr-') ||
            attributeName.startsWith('data-new-gr-') ||
            attributeName.includes('grammarly') ||
            attributeName.includes('darkreader')
          )) {
            target.removeAttribute(attributeName);
          }
        }
      });
    });

    // Observe changes to html and body elements
    [document.documentElement, document.body].forEach(element => {
      if (element) {
        observer.observe(element, {
          attributes: true,
          attributeFilter: [
            'data-lt-installed',
            'data-qb-installed',
            'data-new-gr-c-s-check-loaded',
            'data-gr-ext-installed',
            'data-new-gr-c-s-loaded',
            'data-grammarly-shadow-root',
            'cz-shortcut-listen',
            'data-darkreader-mode',
            'data-darkreader-scheme',
          ]
        });
      }
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  // Show fallback during hydration to prevent mismatches
  if (!isHydrated) {
    return (
      <div suppressHydrationWarning>
        {fallback || (
          <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-4 animate-pulse">🗺️</div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Loading Wanderlust Explorer
              </h2>
              <p className="text-gray-600 mb-6">
                Preparing your travel memories...
              </p>
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return <div suppressHydrationWarning>{children}</div>;
}

/**
 * Hook to safely check if we're on the client side
 */
export function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook to handle browser extension conflicts
 */
export function useBrowserExtensionHandler() {
  const [hasExtensionConflicts, setHasExtensionConflicts] = useState(false);

  useEffect(() => {
    // Check for known problematic browser extensions
    const checkForExtensions = () => {
      const body = document.body;
      const html = document.documentElement;

      const extensionIndicators = [
        'data-lt-installed', // LanguageTool
        'data-gr-ext-installed', // Grammarly
        'data-qb-installed', // QuillBot
        'cz-shortcut-listen', // ColorZilla
      ];

      const hasExtensions = extensionIndicators.some(indicator =>
        body?.hasAttribute(indicator) || html?.hasAttribute(indicator)
      );

      setHasExtensionConflicts(hasExtensions);
    };

    checkForExtensions();

    // Check periodically as extensions can load asynchronously
    const interval = setInterval(checkForExtensions, 1000);

    return () => clearInterval(interval);
  }, []);

  return { hasExtensionConflicts };
}
