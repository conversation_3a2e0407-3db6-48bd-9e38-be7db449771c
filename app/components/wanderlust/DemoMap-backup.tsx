import { useState } from 'react';
import { MapPin, Navigation, Satellite, Car, Zap } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { useWanderlustStore } from '~/stores/wanderlust';
import type { VisitedPlace } from '~/types/wanderlust';

interface DemoMapProps {
  onMapLoad: () => void;
  isLoaded: boolean;
}

// Category colors for markers
const categoryColors: Record<string, string> = {
  food: '#f39c12',
  landmark: '#9b59b6',
  museum: '#3498db',
  park: '#27ae60',
  accommodation: '#e74c3c',
  transport: '#34495e',
  entertainment: '#e91e63',
  shopping: '#ff9800',
};

export function DemoMap({ onMapLoad, isLoaded }: DemoMapProps) {
  const [selectedMarker, setSelectedMarker] = useState<VisitedPlace | null>(null);
  const [mapType, setMapType] = useState<'roadmap' | 'satellite'>('roadmap');
  const [showTraffic, setShowTraffic] = useState(false);

  const {
    filteredPlaces,
    mapCenter,
    mapZoom,
    selectedPlace,
    setSelectedPlace,
  } = useWanderlustStore();

  // Handle marker click
  const handleMarkerClick = (place: VisitedPlace) => {
    setSelectedMarker(place);
    setSelectedPlace(place);
  };

  // Handle map click (close info window)
  const handleMapClick = () => {
    setSelectedMarker(null);
  };

  // Get current location (demo)
  const getCurrentLocation = () => {
    // Demo: just center on NYC
    console.log('Demo: Getting current location...');
  };

  // Toggle satellite view
  const toggleSatellite = () => {
    setMapType(mapType === 'roadmap' ? 'satellite' : 'roadmap');
  };

  // Toggle traffic layer
  const toggleTraffic = () => {
    setShowTraffic(!showTraffic);
  };

  // Calculate marker position on the demo map
  const getMarkerPosition = (place: VisitedPlace) => {
    // Convert lat/lng to pixel position on our demo map
    const mapWidth = 800;
    const mapHeight = 600;

    // Simple projection (this is just for demo purposes)
    const lat = place.coordinates.latitude;
    const lng = place.coordinates.longitude;

    // Rough conversion for NYC area
    const x = ((lng + 74.2) / 0.6) * mapWidth;
    const y = ((40.9 - lat) / 0.4) * mapHeight;

    return { x: Math.max(0, Math.min(mapWidth, x)), y: Math.max(0, Math.min(mapHeight, y)) };
  };

  // Simulate map load
  if (!isLoaded) {
    setTimeout(() => onMapLoad(), 1000);
  }

  return (
    <div className="relative h-full bg-gray-100 rounded-lg overflow-hidden shadow-lg">
      {/* Demo Map Background */}
      <div
        className={`w-full h-full relative cursor-pointer transition-all duration-300 ${
          mapType === 'satellite' ? 'bg-gray-800' : 'bg-blue-50'
        }`}
        onClick={handleMapClick}
        style={{
          backgroundImage: mapType === 'roadmap'
            ? `linear-gradient(45deg, #e3f2fd 25%, transparent 25%),
               linear-gradient(-45deg, #e3f2fd 25%, transparent 25%),
               linear-gradient(45deg, transparent 75%, #e3f2fd 75%),
               linear-gradient(-45deg, transparent 75%, #e3f2fd 75%)`
            : `radial-gradient(circle at 30% 40%, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%)`,
          backgroundSize: mapType === 'roadmap' ? '20px 20px' : 'auto',
          backgroundPosition: mapType === 'roadmap' ? '0 0, 0 10px, 10px -10px, -10px 0px' : 'center',
        }}
      >
        {/* Demo Map Grid */}
        {mapType === 'roadmap' && (
          <div className="absolute inset-0 opacity-20">
            <svg width="100%" height="100%" className="text-blue-300">
              <defs>
                <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                  <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
          </div>
        )}

        {/* Demo Roads/Streets */}
        {mapType === 'roadmap' && (
          <div className="absolute inset-0">
            <svg width="100%" height="100%" className="text-gray-400">
              <line x1="0" y1="30%" x2="100%" y2="30%" stroke="currentColor" strokeWidth="3" opacity="0.6"/>
              <line x1="0" y1="60%" x2="100%" y2="60%" stroke="currentColor" strokeWidth="2" opacity="0.6"/>
              <line x1="20%" y1="0" x2="20%" y2="100%" stroke="currentColor" strokeWidth="3" opacity="0.6"/>
              <line x1="70%" y1="0" x2="70%" y2="100%" stroke="currentColor" strokeWidth="2" opacity="0.6"/>
            </svg>
          </div>
        )}

        {/* Traffic Layer Overlay */}
        {showTraffic && (
          <div className="absolute inset-0 bg-red-500 opacity-10 animate-pulse">
            <div className="absolute top-1/3 left-0 right-0 h-1 bg-red-500 opacity-60"></div>
            <div className="absolute top-2/3 left-0 right-0 h-1 bg-yellow-500 opacity-60"></div>
          </div>
        )}

        {/* Demo Markers */}
        {filteredPlaces.slice(0, 20).map((place) => {
          const position = getMarkerPosition(place);
          const color = categoryColors[place.category] || '#e74c3c';

          return (
            <div
              key={place.id}
              className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-200 hover:scale-110 ${
                selectedPlace?.id === place.id ? 'animate-bounce scale-125' : ''
              }`}
              style={{
                left: `${(position.x / 800) * 100}%`,
                top: `${(position.y / 600) * 100}%`,
              }}
              onClick={(e) => {
                e.stopPropagation();
                handleMarkerClick(place);
              }}
            >
              <div
                className="w-6 h-6 rounded-full border-2 border-white shadow-lg flex items-center justify-center text-white text-xs font-bold"
                style={{ backgroundColor: color }}
              >
                {place.icon || '📍'}
              </div>
            </div>
          );
        })}

        {/* Info Window */}
        {selectedMarker && (
          <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-4 max-w-xs z-10 border">
            <button
              onClick={() => setSelectedMarker(null)}
              className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
            <h3 className="font-semibold text-gray-900 mb-1 pr-6">
              {selectedMarker.name}
            </h3>
            <p className="text-sm text-gray-600 mb-2">
              {selectedMarker.description.en}
            </p>
            <div className="flex items-center justify-between">
              <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                {selectedMarker.category}
              </span>
              {selectedMarker.rating && (
                <div className="flex items-center">
                  <span className="text-yellow-500">★</span>
                  <span className="text-sm ml-1">{selectedMarker.rating}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Demo Watermark */}
        <div className="absolute bottom-4 left-4 bg-black/20 text-white px-3 py-1 rounded text-xs">
          🗺️ Demo Map - Google Maps integration available with API key
        </div>
      </div>

      {/* Map Controls - Mobile-friendly touch targets */}
      <div className="absolute top-4 right-4 flex flex-col gap-3">
        <Button
          size="lg"
          variant="secondary"
          onClick={getCurrentLocation}
          className="min-w-[48px] min-h-[48px] bg-white shadow-lg hover:bg-gray-50"
          title="My Location (Demo)"
        >
          <Navigation className="h-5 w-5" />
        </Button>

        <Button
          size="lg"
          variant="secondary"
          onClick={toggleSatellite}
          className={`min-w-[48px] min-h-[48px] bg-white shadow-lg hover:bg-gray-50 ${mapType === 'satellite' ? 'bg-blue-100' : ''}`}
          title="Toggle Satellite View"
        >
          <Satellite className="h-5 w-5" />
        </Button>

        <Button
          size="lg"
          variant="secondary"
          onClick={toggleTraffic}
          className={`min-w-[48px] min-h-[48px] bg-white shadow-lg hover:bg-gray-50 ${showTraffic ? 'bg-red-100' : ''}`}
          title="Toggle Traffic"
        >
          <Car className="h-5 w-5" />
        </Button>
      </div>

      {/* Legend */}
      <div className="absolute bottom-4 right-4 bg-white/95 backdrop-blur-sm rounded-lg p-3 shadow-md max-w-xs">
        <h4 className="text-sm font-semibold mb-2 flex items-center">
          <Zap className="h-4 w-4 mr-1" />
          Categories
        </h4>
        <div className="grid grid-cols-2 gap-1 text-xs">
          {Object.entries(categoryColors).slice(0, 6).map(([category, color]) => (
            <div key={category} className="flex items-center">
              <div
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: color }}
              />
              <span className="capitalize">{category}</span>
            </div>
          ))}
        </div>
        <div className="text-xs text-gray-500 mt-2">
          {filteredPlaces.length} places shown
        </div>
      </div>

      {/* Loading overlay */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Loading demo map...</p>
          </div>
        </div>
      )}
    </div>
  );
}
