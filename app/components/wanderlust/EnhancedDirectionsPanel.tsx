/**
 * Enhanced Directions Panel for Multi-Point Route Planning
 * 
 * Integrates with Google Maps Directions API for real route calculation,
 * optimization, and turn-by-turn directions with FIFA design system styling.
 */

import { useState, useEffect } from 'react';
import { 
  X, 
  Navigation, 
  Clock, 
  MapPin, 
  Route, 
  Settings, 
  Zap, 
  AlertTriangle,
  Car,
  Bike,
  Footprints,
  Bus,
  RotateCcw,
  Play
} from 'lucide-react';
import { But<PERSON> } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Switch } from '~/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { useWanderlustStore } from '~/stores/wanderlust';
import { cn } from '~/lib/utils';
import type { TravelRoute } from '~/types/wanderlust';

const TRAVEL_MODE_ICONS = {
  DRIVING: Car,
  WALKING: Footprints,
  BICYCLING: Bike,
  TRANSIT: Bus,
};

const TRAVEL_MODE_LABELS = {
  DRIVING: 'Driving',
  WALKING: 'Walking',
  BICYCLING: 'Cycling',
  TRANSIT: 'Transit',
};

export function EnhancedDirectionsPanel() {
  const [activeTab, setActiveTab] = useState('route');

  const {
    showDirections,
    currentRoute,
    itinerary,
    routePlanning,
    setShowDirections,
    setCurrentRoute,
    clearItinerary,
    calculateRouteFromItinerary,
    optimizeCurrentRoute,
    updateOptimizationOptions,
    setRoutePlanningError,
    stopRoutePlanning,
  } = useWanderlustStore();

  const handleClose = () => {
    setShowDirections(false);
    setCurrentRoute(null);
    stopRoutePlanning();
  };

  const handleCalculateRoute = async () => {
    if (itinerary.length < 2) {
      setRoutePlanningError('Add at least 2 places to your itinerary');
      return;
    }
    
    await calculateRouteFromItinerary();
  };

  const handleOptimizeRoute = async () => {
    if (!currentRoute) {
      setRoutePlanningError('No route to optimize');
      return;
    }
    
    await optimizeCurrentRoute();
  };

  const handleTravelModeChange = (mode: string) => {
    updateOptimizationOptions({
      ...routePlanning.optimizationOptions,
    });
    // Update route preferences
    // This would need to be added to the store
  };

  if (!showDirections) return null;

  return (
    <>
      {/* FIFA Overlay */}
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40"
        onClick={handleClose}
      />

      {/* Enhanced Panel with FIFA Styling */}
      <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden">
        <Card className="shadow-2xl border-2 border-[#FFD700]/30 bg-gradient-to-br from-black/90 to-black/70 backdrop-blur-xl">
          <CardHeader className="pb-3 bg-gradient-to-r from-[#FFD700]/20 to-[#DC2626]/20 border-b border-[#FFD700]/30">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center text-white">
                <Route className="h-5 w-5 mr-2 text-[#FFD700]" />
                Multi-Point Route Planning
                <Badge variant="outline" className="ml-2 text-xs border-[#FFD700] text-[#FFD700]">
                  FIFA 2025™
                </Badge>
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClose}
                className="h-8 w-8 p-0 text-white hover:bg-[#FFD700]/20"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-black/40 border-b border-[#FFD700]/20">
                <TabsTrigger 
                  value="route" 
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#FFD700] data-[state=active]:to-[#DC2626] data-[state=active]:text-black text-white"
                >
                  Route
                </TabsTrigger>
                <TabsTrigger 
                  value="directions"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#FFD700] data-[state=active]:to-[#DC2626] data-[state=active]:text-black text-white"
                >
                  Directions
                </TabsTrigger>
                <TabsTrigger 
                  value="settings"
                  className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-[#FFD700] data-[state=active]:to-[#DC2626] data-[state=active]:text-black text-white"
                >
                  Settings
                </TabsTrigger>
              </TabsList>

              {/* Route Tab */}
              <TabsContent value="route" className="p-4 space-y-4 max-h-[60vh] overflow-y-auto">
                {/* Itinerary Summary */}
                <div>
                  <h3 className="font-medium text-sm mb-2 text-white flex items-center">
                    <MapPin className="h-4 w-4 mr-1 text-[#FFD700]" />
                    Your Itinerary ({itinerary.length} places)
                  </h3>
                  
                  {itinerary.length === 0 ? (
                    <div className="text-center py-8 text-white/60">
                      <MapPin className="h-12 w-12 mx-auto mb-2 text-[#FFD700]/50" />
                      <p className="text-sm">Add places to your itinerary to plan a route</p>
                    </div>
                  ) : (
                    <div className="space-y-2 max-h-40 overflow-y-auto bg-black/20 rounded-lg p-3 border border-[#FFD700]/20">
                      {itinerary.map((place, index) => (
                        <div key={place.id} className="flex items-center text-sm">
                          <div className="w-6 h-6 bg-gradient-to-r from-[#FFD700] to-[#DC2626] text-black rounded-full flex items-center justify-center text-xs font-bold mr-3">
                            {index + 1}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-white">{place.name}</div>
                            <div className="text-white/60 text-xs">{place.city}</div>
                          </div>
                          {index < itinerary.length - 1 && (
                            <div className="text-[#FFD700]/60 text-xs">→</div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Route Actions */}
                {itinerary.length >= 2 && (
                  <div className="space-y-2">
                    <Button
                      onClick={handleCalculateRoute}
                      disabled={routePlanning.isPlanning}
                      className="w-full bg-gradient-to-r from-[#FFD700] to-[#DC2626] text-black hover:from-[#FFF700] hover:to-[#EF4444] font-bold"
                    >
                      {routePlanning.isPlanning ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2" />
                          Calculating Route...
                        </>
                      ) : (
                        <>
                          <Navigation className="h-4 w-4 mr-2" />
                          Calculate Route
                        </>
                      )}
                    </Button>

                    {currentRoute && (
                      <Button
                        variant="outline"
                        onClick={handleOptimizeRoute}
                        disabled={routePlanning.isOptimizing}
                        className="w-full border-[#FFD700] text-[#FFD700] hover:bg-[#FFD700]/10"
                      >
                        {routePlanning.isOptimizing ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#FFD700] mr-2" />
                            Optimizing...
                          </>
                        ) : (
                          <>
                            <Zap className="h-4 w-4 mr-2" />
                            Optimize Route
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                )}

                {/* Error Display */}
                {routePlanning.planningError && (
                  <div className="bg-[#DC2626]/20 border border-[#DC2626]/40 rounded-lg p-3">
                    <div className="flex items-center text-[#DC2626]">
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      <span className="text-sm font-medium">Error</span>
                    </div>
                    <p className="text-sm text-white/80 mt-1">{routePlanning.planningError}</p>
                  </div>
                )}

                {/* Route Information */}
                {currentRoute && (
                  <div className="bg-gradient-to-r from-[#FFD700]/10 to-[#DC2626]/10 rounded-lg p-4 border border-[#FFD700]/30">
                    <h4 className="font-medium text-sm mb-3 text-white flex items-center">
                      <Route className="h-4 w-4 mr-1 text-[#FFD700]" />
                      Route Details
                    </h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="flex items-center text-white/80">
                            <Clock className="h-4 w-4 mr-1" />
                            Duration
                          </span>
                          <span className="font-medium text-[#FFD700]">{currentRoute.estimatedDuration}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="flex items-center text-white/80">
                            <MapPin className="h-4 w-4 mr-1" />
                            Distance
                          </span>
                          <span className="font-medium text-[#FFD700]">{currentRoute.estimatedDistance}</span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-white/80">Travel Mode</span>
                          <Badge variant="outline" className="border-[#FFD700] text-[#FFD700]">
                            {TRAVEL_MODE_LABELS[currentRoute.travelMode]}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-white/80">Optimized</span>
                          <Badge variant={currentRoute.optimized ? "default" : "secondary"} className={
                            currentRoute.optimized 
                              ? "bg-gradient-to-r from-[#FFD700] to-[#DC2626] text-black" 
                              : "bg-white/20 text-white"
                          }>
                            {currentRoute.optimized ? 'Yes' : 'No'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Clear Itinerary */}
                {itinerary.length > 0 && (
                  <Button
                    variant="destructive"
                    onClick={clearItinerary}
                    className="w-full bg-[#DC2626]/80 hover:bg-[#DC2626] text-white"
                    size="sm"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Clear Itinerary
                  </Button>
                )}
              </TabsContent>

              {/* Directions Tab */}
              <TabsContent value="directions" className="p-4 space-y-4 max-h-[60vh] overflow-y-auto">
                {currentRoute && currentRoute.legs ? (
                  <div className="space-y-4">
                    <h4 className="font-medium text-sm text-white flex items-center">
                      <Navigation className="h-4 w-4 mr-1 text-[#FFD700]" />
                      Turn-by-turn Directions
                    </h4>
                    
                    {currentRoute.legs.map((leg, legIndex) => (
                      <div key={legIndex} className="space-y-2">
                        <div className="flex items-center justify-between p-2 bg-black/20 rounded border border-[#FFD700]/20">
                          <span className="text-sm font-medium text-white">
                            Leg {legIndex + 1}: {leg.start_address} → {leg.end_address}
                          </span>
                          <Badge variant="outline" className="border-[#FFD700] text-[#FFD700] text-xs">
                            {leg.duration.text}
                          </Badge>
                        </div>
                        
                        <div className="space-y-1 ml-4">
                          {leg.steps.slice(0, 3).map((step, stepIndex) => (
                            <div key={stepIndex} className="text-xs text-white/80 flex items-start">
                              <span className="w-4 h-4 bg-[#FFD700]/20 rounded-full flex items-center justify-center text-[10px] text-[#FFD700] mr-2 mt-0.5 flex-shrink-0">
                                {stepIndex + 1}
                              </span>
                              <div 
                                className="flex-1"
                                dangerouslySetInnerHTML={{ __html: step.html_instructions }}
                              />
                            </div>
                          ))}
                          {leg.steps.length > 3 && (
                            <div className="text-xs text-white/60 ml-6">
                              ... and {leg.steps.length - 3} more steps
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-white/60">
                    <Navigation className="h-12 w-12 mx-auto mb-2 text-[#FFD700]/50" />
                    <p className="text-sm">Calculate a route to see turn-by-turn directions</p>
                  </div>
                )}
              </TabsContent>

              {/* Settings Tab */}
              <TabsContent value="settings" className="p-4 space-y-4 max-h-[60vh] overflow-y-auto">
                <div className="space-y-4">
                  <h4 className="font-medium text-sm text-white flex items-center">
                    <Settings className="h-4 w-4 mr-1 text-[#FFD700]" />
                    Route Optimization Settings
                  </h4>

                  {/* Travel Mode Selection */}
                  <div className="space-y-2">
                    <label className="text-sm text-white/80">Travel Mode</label>
                    <Select value={routePlanning.routePreferences.preferredTravelMode} onValueChange={handleTravelModeChange}>
                      <SelectTrigger className="bg-black/20 border-[#FFD700]/30 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-black/90 border-[#FFD700]/30">
                        {Object.entries(TRAVEL_MODE_LABELS).map(([mode, label]) => {
                          const Icon = TRAVEL_MODE_ICONS[mode as keyof typeof TRAVEL_MODE_ICONS];
                          return (
                            <SelectItem key={mode} value={mode} className="text-white hover:bg-[#FFD700]/20">
                              <div className="flex items-center">
                                <Icon className="h-4 w-4 mr-2" />
                                {label}
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Optimization Options */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <label className="text-sm text-white/80">Optimize waypoint order</label>
                      <Switch
                        checked={routePlanning.optimizationOptions.optimizeWaypointOrder}
                        onCheckedChange={(checked) => updateOptimizationOptions({ optimizeWaypointOrder: checked })}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <label className="text-sm text-white/80">Avoid tolls</label>
                      <Switch
                        checked={routePlanning.optimizationOptions.avoidTolls}
                        onCheckedChange={(checked) => updateOptimizationOptions({ avoidTolls: checked })}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <label className="text-sm text-white/80">Avoid highways</label>
                      <Switch
                        checked={routePlanning.optimizationOptions.avoidHighways}
                        onCheckedChange={(checked) => updateOptimizationOptions({ avoidHighways: checked })}
                      />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <label className="text-sm text-white/80">Avoid ferries</label>
                      <Switch
                        checked={routePlanning.optimizationOptions.avoidFerries}
                        onCheckedChange={(checked) => updateOptimizationOptions({ avoidFerries: checked })}
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
