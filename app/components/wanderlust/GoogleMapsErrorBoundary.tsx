import React, { Component } from 'react';
import { AlertTriangle, RefreshCw, Map } from 'lucide-react';
import { Button } from '~/components/ui/button';

interface Props {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

export class GoogleMapsErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Google Maps Error Boundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="h-full bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
          <div className="text-center p-8 max-w-md">
            <div className="mb-4">
              <AlertTriangle className="h-12 w-12 text-amber-500 mx-auto mb-2" />
              <Map className="h-8 w-8 text-gray-400 mx-auto" />
            </div>

            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Map Loading Error
            </h3>

            <p className="text-sm text-gray-600 mb-4">
              There was an issue loading the Google Maps component. This might be due to:
            </p>

            <ul className="text-xs text-gray-500 text-left mb-6 space-y-1">
              <li>• Missing or invalid Google Maps API key</li>
              <li>• Network connectivity issues</li>
              <li>• Browser extension conflicts</li>
              <li>• API quota exceeded</li>
            </ul>

            <div className="space-y-3">
              <Button
                onClick={this.handleRetry}
                className="w-full"
                variant="default"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry Loading Map
              </Button>

              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="w-full"
              >
                Refresh Page
              </Button>
            </div>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4 text-left">
                <summary className="text-xs text-gray-500 cursor-pointer">
                  Error Details (Development)
                </summary>
                <pre className="text-xs text-red-600 mt-2 p-2 bg-red-50 rounded overflow-auto">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook for functional components to handle Google Maps errors
export function useGoogleMapsErrorHandler() {
  const [error, setError] = React.useState<string | null>(null);

  const handleError = React.useCallback((errorMessage: string) => {
    console.error('Google Maps Error:', errorMessage);
    setError(errorMessage);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  return { error, handleError, clearError };
}
