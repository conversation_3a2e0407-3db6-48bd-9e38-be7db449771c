import { useState, useEffect } from 'react';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { cn } from '~/lib/utils';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
}

interface NotificationSystemProps {
  className?: string;
}

// Global notification state
let notifications: Notification[] = [];
let listeners: ((notifications: Notification[]) => void)[] = [];

// Global notification functions
export const showNotification = (notification: Omit<Notification, 'id'>) => {
  const id = Math.random().toString(36).substr(2, 9);
  const newNotification: Notification = {
    id,
    duration: 5000,
    ...notification,
  };

  notifications = [...notifications, newNotification];
  listeners.forEach(listener => listener(notifications));

  // Auto-remove after duration (unless persistent)
  if (!newNotification.persistent && newNotification.duration) {
    setTimeout(() => {
      removeNotification(id);
    }, newNotification.duration);
  }

  return id;
};

export const removeNotification = (id: string) => {
  notifications = notifications.filter(n => n.id !== id);
  listeners.forEach(listener => listener(notifications));
};

export const clearAllNotifications = () => {
  notifications = [];
  listeners.forEach(listener => listener(notifications));
};

// Convenience functions
export const showSuccess = (title: string, message?: string) =>
  showNotification({ type: 'success', title, message });

export const showError = (title: string, message?: string) =>
  showNotification({ type: 'error', title, message, persistent: true });

export const showInfo = (title: string, message?: string) =>
  showNotification({ type: 'info', title, message });

export const showWarning = (title: string, message?: string) =>
  showNotification({ type: 'warning', title, message });

export function NotificationSystem({ className }: NotificationSystemProps) {
  const [currentNotifications, setCurrentNotifications] = useState<Notification[]>([]);

  useEffect(() => {
    const listener = (newNotifications: Notification[]) => {
      setCurrentNotifications(newNotifications);
    };

    listeners.push(listener);

    return () => {
      listeners = listeners.filter(l => l !== listener);
    };
  }, []);

  if (currentNotifications.length === 0) return null;

  return (
    <div className={cn(
      'fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full',
      className
    )}>
      {currentNotifications.map((notification) => (
        <NotificationCard
          key={notification.id}
          notification={notification}
          onClose={() => removeNotification(notification.id)}
        />
      ))}
    </div>
  );
}

interface NotificationCardProps {
  notification: Notification;
  onClose: () => void;
}

function NotificationCard({ notification, onClose }: NotificationCardProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(onClose, 300); // Wait for exit animation
  };

  const getIcon = () => {
    switch (notification.type) {
      case 'success':
        return <CheckCircle className="h-5 w-5" />;
      case 'error':
        return <AlertCircle className="h-5 w-5" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5" />;
      case 'info':
      default:
        return <Info className="h-5 w-5" />;
    }
  };

  const getStyles = () => {
    switch (notification.type) {
      case 'success':
        return {
          bg: 'bg-green-50 border-green-200',
          icon: 'text-green-600',
          title: 'text-green-900',
          message: 'text-green-700',
        };
      case 'error':
        return {
          bg: 'bg-red-50 border-red-200',
          icon: 'text-red-600',
          title: 'text-red-900',
          message: 'text-red-700',
        };
      case 'warning':
        return {
          bg: 'bg-yellow-50 border-yellow-200',
          icon: 'text-yellow-600',
          title: 'text-yellow-900',
          message: 'text-yellow-700',
        };
      case 'info':
      default:
        return {
          bg: 'bg-blue-50 border-blue-200',
          icon: 'text-blue-600',
          title: 'text-blue-900',
          message: 'text-blue-700',
        };
    }
  };

  const styles = getStyles();

  return (
    <div
      className={cn(
        'border rounded-lg shadow-lg backdrop-blur-sm transition-all duration-300 transform',
        styles.bg,
        isVisible && !isLeaving
          ? 'translate-x-0 opacity-100'
          : 'translate-x-full opacity-0',
        isLeaving && 'translate-x-full opacity-0'
      )}
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className={cn('flex-shrink-0', styles.icon)}>
            {getIcon()}
          </div>

          <div className="ml-3 flex-1">
            <h3 className={cn('text-sm font-medium', styles.title)}>
              {notification.title}
            </h3>
            {notification.message && (
              <p className={cn('mt-1 text-sm', styles.message)}>
                {notification.message}
              </p>
            )}
          </div>

          <div className="ml-4 flex-shrink-0">
            <button
              onClick={handleClose}
              className={cn(
                'inline-flex rounded-md p-1.5 transition-colors',
                'hover:bg-black/5 focus:outline-none focus:ring-2 focus:ring-offset-2',
                styles.icon
              )}
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Progress bar for timed notifications */}
      {!notification.persistent && notification.duration && (
        <div className="h-1 bg-black/10 rounded-b-lg overflow-hidden">
          <div
            className="h-full bg-current opacity-50 transition-all ease-linear"
            style={{
              animation: `shrink ${notification.duration}ms linear`,
            }}
          />
        </div>
      )}
    </div>
  );
}

// CSS for progress bar animation (would typically be in a CSS file)
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes shrink {
      from { width: 100%; }
      to { width: 0%; }
    }
  `;
  document.head.appendChild(style);
}
