import { useState } from 'react';
import { Search, Filter, SortAsc, SortDesc, MapPin, Plus, X } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Badge } from '~/components/ui/badge';
import { useWanderlustStore } from '~/stores/wanderlust';
import { PlaceCard } from './PlaceCard';
import { BottomSheet } from './BottomSheet';

const categories = [
  { id: 'food', label: 'Food & Drink', icon: '🍕' },
  { id: 'landmark', label: 'Landmarks', icon: '🏛️' },
  { id: 'museum', label: 'Museums', icon: '🎨' },
  { id: 'park', label: 'Parks', icon: '🌳' },
  { id: 'accommodation', label: 'Hotels', icon: '🏨' },
  { id: 'transport', label: 'Transport', icon: '🚇' },
  { id: 'entertainment', label: 'Entertainment', icon: '🎭' },
  { id: 'shopping', label: 'Shopping', icon: '🛍️' },
];

export function ExplorerLog() {
  const [showFilters, setShowFilters] = useState(false);
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const [bottomSheetPlace, setBottomSheetPlace] = useState<any>(null);

  const {
    filteredPlaces,
    filterOptions,
    selectedPlace,
    setFilterOptions,
    setSelectedPlace,
    addToItinerary,
    itinerary,
  } = useWanderlustStore();

  const handleSearchChange = (value: string) => {
    setFilterOptions({ searchQuery: value });
  };

  const handleCategoryToggle = (categoryId: string) => {
    const currentCategories = filterOptions.categories;
    const newCategories = currentCategories.includes(categoryId)
      ? currentCategories.filter(id => id !== categoryId)
      : [...currentCategories, categoryId];

    setFilterOptions({ categories: newCategories });
  };

  const handleSortChange = (sortBy: typeof filterOptions.sortBy) => {
    const newSortOrder = filterOptions.sortBy === sortBy && filterOptions.sortOrder === 'asc'
      ? 'desc'
      : 'asc';

    setFilterOptions({ sortBy, sortOrder: newSortOrder });
  };

  const clearFilters = () => {
    setFilterOptions({
      categories: [],
      searchQuery: '',
      sortBy: 'name',
      sortOrder: 'asc',
    });
  };

  // Handle place selection - mobile uses bottom sheet, desktop uses existing behavior
  const handlePlaceSelect = (place: any) => {
    setSelectedPlace(place);

    // On mobile (< 768px), open bottom sheet
    if (window.innerWidth < 768) {
      setBottomSheetPlace(place);
      setIsBottomSheetOpen(true);
    }
  };

  const handleBottomSheetClose = () => {
    setIsBottomSheetOpen(false);
    setBottomSheetPlace(null);
  };

  return (
    <div className="h-full bg-gray-50 flex flex-col">
      {/* Header - Mobile Compact */}
      <div className="p-3 lg:p-4 bg-white border-b border-gray-200">
        <div className="flex items-center justify-between mb-3 lg:mb-4">
          <h2 className="text-base lg:text-lg font-semibold text-gray-900">
            Explorer Log
          </h2>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="min-h-[44px] px-3"
          >
            <Filter className="h-4 w-4 mr-1" />
            <span className="hidden sm:inline">Filters</span>
          </Button>
        </div>

        {/* Search Bar - Mobile Optimized */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="🔍 Search places..."
            value={filterOptions.searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10 pr-12 h-11 text-base text-gray-900 placeholder:text-gray-500"
          />
          {/* Clear All Filters Button */}
          {(filterOptions.categories.length > 0 || filterOptions.searchQuery) && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-red-50"
              title="Clear All Filters"
            >
              <X className="h-4 w-4 text-red-500 hover:text-red-600" />
            </Button>
          )}
        </div>

        {/* Filter Panel - Mobile Optimized */}
        {showFilters && (
          <div className="mt-3 lg:mt-4 p-3 lg:p-4 bg-gray-50 rounded-lg">
            {/* Category Filters */}
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Categories</h3>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={filterOptions.categories.includes(category.id) ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleCategoryToggle(category.id)}
                    className="text-xs min-h-[40px] px-3"
                  >
                    <span className="mr-1">{category.icon}</span>
                    <span className="hidden sm:inline">{category.label}</span>
                    <span className="sm:hidden">{category.label.split(' ')[0]}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Sort Options */}
            <div className="mb-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Sort By</h3>
              <div className="flex gap-2 flex-wrap">
                {[
                  { key: 'name', label: 'Name' },
                  { key: 'rating', label: 'Rating' },
                  { key: 'category', label: 'Category' },
                ].map((sort) => (
                  <Button
                    key={sort.key}
                    variant={filterOptions.sortBy === sort.key ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleSortChange(sort.key as typeof filterOptions.sortBy)}
                    className="text-xs min-h-[40px] px-3"
                  >
                    {sort.label}
                    {filterOptions.sortBy === sort.key && (
                      filterOptions.sortOrder === 'asc' ?
                        <SortAsc className="h-3 w-3 ml-1" /> :
                        <SortDesc className="h-3 w-3 ml-1" />
                    )}
                  </Button>
                ))}
              </div>
            </div>


          </div>
        )}

        {/* Active Filters */}
        {(filterOptions.categories.length > 0 || filterOptions.searchQuery) && (
          <div className="mt-3 flex flex-wrap gap-1">
            {filterOptions.categories.map((categoryId) => {
              const category = categories.find(c => c.id === categoryId);
              return category ? (
                <Badge key={categoryId} variant="secondary" className="text-xs">
                  {category.icon} {category.label}
                </Badge>
              ) : null;
            })}
            {filterOptions.searchQuery && (
              <Badge variant="secondary" className="text-xs">
                Search: "{filterOptions.searchQuery}"
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Places List - Mobile Optimized */}
      <div className="flex-1 overflow-y-auto p-3 lg:p-4 space-y-2 lg:space-y-3">
        {filteredPlaces.length === 0 ? (
          <div className="text-center py-6 lg:py-8">
            <MapPin className="h-10 w-10 lg:h-12 lg:w-12 text-gray-400 mx-auto mb-3" />
            <h3 className="text-base lg:text-lg font-medium text-gray-900 mb-1">
              No places found
            </h3>
            <p className="text-sm text-gray-500">
              Try adjusting your filters or search terms
            </p>
          </div>
        ) : (
          filteredPlaces.map((place) => (
            <PlaceCard
              key={place.id}
              place={place}
              isSelected={selectedPlace?.id === place.id}
              onSelect={() => handlePlaceSelect(place)}
              onAddToItinerary={() => addToItinerary(place)}
              isInItinerary={itinerary.some(p => p.id === place.id)}
            />
          ))
        )}
      </div>

      {/* Results Count - Mobile Compact */}
      <div className="p-3 lg:p-4 bg-white border-t border-gray-200">
        <p className="text-xs lg:text-sm text-gray-600 text-center">
          Showing {filteredPlaces.length} places
        </p>
      </div>

      {/* Mobile Bottom Sheet for Place Details */}
      <BottomSheet
        isOpen={isBottomSheetOpen}
        onClose={handleBottomSheetClose}
        snapPoints={[0.4, 0.7, 0.9]}
        initialSnapPoint={0.7}
      >
        {bottomSheetPlace && (
          <div className="space-y-4">
            {/* Place Header */}
            <div className="flex items-start gap-4">
              <div className="text-3xl flex-shrink-0">
                {bottomSheetPlace.icon || '📍'}
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-bold text-gray-900 mb-1">
                  {bottomSheetPlace.name}
                </h3>
                <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                  <span>{bottomSheetPlace.city}</span>
                  {bottomSheetPlace.rating && (
                    <>
                      <span>•</span>
                      <div className="flex items-center">
                        <span className="text-yellow-500">★</span>
                        <span className="ml-1">{bottomSheetPlace.rating}</span>
                      </div>
                    </>
                  )}
                </div>
                <Badge variant="outline" className="text-xs">
                  {categories.find(c => c.id === bottomSheetPlace.category)?.icon}{' '}
                  {categories.find(c => c.id === bottomSheetPlace.category)?.label || bottomSheetPlace.category}
                </Badge>
              </div>
            </div>

            {/* Description */}
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">About</h4>
              <p className="text-gray-600 leading-relaxed">
                {bottomSheetPlace.description.en}
              </p>
            </div>

            {/* Key Takeaway */}
            {bottomSheetPlace.keyTakeaway && (
              <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r">
                <h4 className="font-semibold text-blue-900 mb-1">💡 Key Takeaway</h4>
                <p className="text-blue-700 text-sm">
                  {bottomSheetPlace.keyTakeaway}
                </p>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-3 pt-4">
              <Button
                onClick={() => addToItinerary(bottomSheetPlace)}
                className="flex-1"
                disabled={itinerary.some(p => p.id === bottomSheetPlace.id)}
              >
                <Plus className="h-4 w-4 mr-2" />
                {itinerary.some(p => p.id === bottomSheetPlace.id) ? 'Added to Itinerary' : 'Add to Itinerary'}
              </Button>
              <Button
                variant="outline"
                onClick={handleBottomSheetClose}
                className="px-6"
              >
                Close
              </Button>
            </div>
          </div>
        )}
      </BottomSheet>
    </div>
  );
}