import { useState, useCallback } from 'react';
import { Star, Plus, ChevronDown, ChevronUp, Heart, MapPin, MoreHorizontal } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Card, CardContent, CardHeader } from '~/components/ui/card';
import { cn } from '~/lib/utils';
import { useGestures } from '~/hooks/useGestures';
import type { VisitedPlace } from '~/types/wanderlust';

const categories = [
  { id: 'food', label: 'Food & Drink', icon: '🍕' },
  { id: 'landmark', label: 'Landmarks', icon: '🏛️' },
  { id: 'museum', label: 'Museums', icon: '🎨' },
  { id: 'park', label: 'Parks', icon: '🌳' },
  { id: 'accommodation', label: 'Hotels', icon: '🏨' },
  { id: 'transport', label: 'Transport', icon: '🚇' },
  { id: 'entertainment', label: 'Entertainment', icon: '🎭' },
  { id: 'shopping', label: 'Shopping', icon: '🛍️' },
];

interface PlaceCardProps {
  place: VisitedPlace;
  isSelected: boolean;
  onSelect: () => void;
  onAddToItinerary: () => void;
  isInItinerary: boolean;
}

export function PlaceCard({ place, isSelected, onSelect, onAddToItinerary, isInItinerary }: PlaceCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [swipeOffset, setSwipeOffset] = useState(0);
  const [isSwipeActionTriggered, setIsSwipeActionTriggered] = useState(false);
  const categoryInfo = categories.find(c => c.id === place.category);

  const handleCardClick = () => {
    onSelect();
    setIsExpanded(!isExpanded);
  };

  const handleExpandToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  const handleLongPress = useCallback(() => {
    setShowQuickActions(true);
    // Haptic feedback if available
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  }, []);

  const handleSwipeAction = useCallback((direction: 'left' | 'right') => {
    if (isSwipeActionTriggered) return;

    setIsSwipeActionTriggered(true);

    if (direction === 'right') {
      // Swipe right = add to itinerary
      onAddToItinerary();
    }
    // Swipe left could be for removal or other actions in the future

    // Reset after animation
    setTimeout(() => {
      setSwipeOffset(0);
      setIsSwipeActionTriggered(false);
    }, 300);
  }, [onAddToItinerary, isSwipeActionTriggered]);

  // Gesture handling
  const { ref: gestureRef } = useGestures({
    onLongPress: handleLongPress,
    onSwipeLeft: () => handleSwipeAction('left'),
    onSwipeRight: () => handleSwipeAction('right'),
    onDragMove: ({ deltaX }) => {
      // Visual feedback during swipe
      const maxOffset = 80;
      const clampedOffset = Math.max(-maxOffset, Math.min(maxOffset, deltaX));
      setSwipeOffset(clampedOffset);
    },
    onDragEnd: ({ deltaX }) => {
      const threshold = 60;
      if (Math.abs(deltaX) > threshold) {
        handleSwipeAction(deltaX > 0 ? 'right' : 'left');
      } else {
        setSwipeOffset(0);
      }
    },
    config: {
      swipeThreshold: 44,
      longPressDelay: 500,
    },
  });

  return (
    <div className="relative overflow-hidden">
      {/* Swipe Action Background */}
      <div
        className={cn(
          'absolute inset-0 flex items-center justify-end pr-4 bg-green-500 text-white transition-opacity duration-200',
          swipeOffset > 30 ? 'opacity-100' : 'opacity-0'
        )}
      >
        <div className="flex items-center gap-2">
          <Plus className="h-5 w-5" />
          <span className="font-medium">Add to Itinerary</span>
        </div>
      </div>

      <Card
        ref={gestureRef as React.RefObject<HTMLDivElement>}
        className={cn(
          'cursor-pointer transition-all duration-300 hover:shadow-md active:scale-[0.98] touch-manipulation',
          isSelected ? 'ring-2 ring-blue-500 shadow-md' : '',
          isExpanded ? 'shadow-lg' : '',
          'relative z-10'
        )}
        style={{
          transform: `translateX(${swipeOffset}px)`,
          transition: swipeOffset === 0 ? 'transform 300ms ease-out' : 'none',
        }}
        onClick={handleCardClick}
      >
      {/* Collapsed State - Always Visible */}
      <CardHeader className="pb-2 p-3 lg:p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 text-sm lg:text-base mb-1 truncate">
              {place.name}
            </h3>

            {/* Compact info row */}
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <Badge variant="outline" className="text-xs px-1 py-0">
                {categoryInfo?.icon} {categoryInfo?.label || place.category}
              </Badge>

              {place.rating && (
                <div className="flex items-center">
                  <Star className="h-3 w-3 text-yellow-500 fill-current" />
                  <span className="ml-1">{place.rating}</span>
                </div>
              )}

              <span className="truncate">{place.city}</span>
            </div>
          </div>

          <div className="flex items-center ml-2 gap-2">
            <div className="text-xl lg:text-2xl flex-shrink-0">
              {place.icon || categoryInfo?.icon || '📍'}
            </div>

            {/* Expand/Collapse Button */}
            <button
              onClick={handleExpandToggle}
              className="p-1 hover:bg-gray-100 rounded-full transition-colors min-w-[32px] min-h-[32px] flex items-center justify-center"
              aria-label={isExpanded ? 'Collapse details' : 'Expand details'}
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4 text-gray-400" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-400" />
              )}
            </button>
          </div>
        </div>
      </CardHeader>

      {/* Expanded State - Progressive Disclosure */}
      <div
        className={cn(
          'overflow-hidden transition-all duration-300 ease-in-out',
          isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        )}
      >
        <CardContent className="pt-0 p-3 lg:p-4">
          {/* Full Description */}
          <p className="text-xs lg:text-sm text-gray-600 mb-3 leading-relaxed">
            {place.description.en}
          </p>

          {/* Key Takeaway */}
          {place.keyTakeaway && (
            <div className="bg-blue-50 border-l-4 border-blue-400 p-3 mb-3 rounded-r">
              <p className="text-xs lg:text-sm text-blue-700">
                💡 <strong>Key Takeaway:</strong> {place.keyTakeaway}
              </p>
            </div>
          )}

          {/* Action Button */}
          <div className="flex justify-end">
            <Button
              size="sm"
              variant={isInItinerary ? "secondary" : "default"}
              onClick={(e) => {
                e.stopPropagation();
                onAddToItinerary();
              }}
              className="min-h-[36px] px-4"
            >
              <Plus className="h-3 w-3 mr-1" />
              {isInItinerary ? 'Added to Itinerary' : 'Add to Itinerary'}
            </Button>
          </div>
        </CardContent>
      </div>

      {/* Quick Actions Menu - Long Press */}
      {showQuickActions && (
        <div className="absolute top-2 right-2 bg-white rounded-lg shadow-lg border border-gray-200 p-2 z-20">
          <div className="flex gap-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onAddToItinerary();
                setShowQuickActions(false);
              }}
              className="p-2 hover:bg-gray-100 rounded-lg min-w-[40px] min-h-[40px] flex items-center justify-center"
              aria-label="Add to itinerary"
            >
              <Plus className="h-4 w-4 text-green-600" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onSelect();
                setShowQuickActions(false);
              }}
              className="p-2 hover:bg-gray-100 rounded-lg min-w-[40px] min-h-[40px] flex items-center justify-center"
              aria-label="View on map"
            >
              <MapPin className="h-4 w-4 text-blue-600" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowQuickActions(false);
              }}
              className="p-2 hover:bg-gray-100 rounded-lg min-w-[40px] min-h-[40px] flex items-center justify-center"
              aria-label="More options"
            >
              <MoreHorizontal className="h-4 w-4 text-gray-600" />
            </button>
          </div>
        </div>
      )}

      {/* Backdrop for quick actions */}
      {showQuickActions && (
        <div
          className="fixed inset-0 z-10"
          onClick={() => setShowQuickActions(false)}
        />
      )}
    </Card>

    {/* Gesture Instructions - Mobile Only */}
    <div className="md:hidden text-xs text-gray-500 text-center mt-1">
      <span className="sr-only">
        Swipe right to add to itinerary, long press for quick actions
      </span>
    </div>
  </div>
  );
}
