import { useEffect, useRef, useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { cn } from '~/lib/utils';
import { useWanderlustStore } from '~/stores/wanderlust';
import { SwipeableRegionCards } from './SwipeableRegionCards';
import type { CityRegion } from '~/types/wanderlust';

interface CityHubProps {
  regions: CityRegion[];
}

export function CityHub({ regions }: CityHubProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { currentRegion, setCurrentRegion } = useWanderlustStore();

  // Auto-scroll to active card when region changes
  useEffect(() => {
    if (currentRegion && scrollContainerRef.current) {
      const activeCard = scrollContainerRef.current.querySelector(
        `[data-region="${currentRegion}"]`
      ) as HTMLElement;

      if (activeCard) {
        activeCard.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'center',
        });
      }
    }
  }, [currentRegion]);

  const handleRegionSelect = (regionId: string) => {
    setCurrentRegion(regionId);
    setIsDropdownOpen(false); // Close dropdown on mobile after selection
  };

  const currentRegionData = regions.find(r => r.id === currentRegion);

  return (
    <div className="relative bg-gradient-to-r from-[#000000] to-[#DC2626] border-b border-[#FFD700]/30 py-3 sm:py-4 lg:py-6 fifa-city-hub">
      {/* FIFA Background Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#FFD700]/5 to-[#DC2626]/5 backdrop-blur-sm"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-3 sm:px-4 lg:px-8">
        <div className="text-center mb-3 sm:mb-4 lg:mb-6">
          <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-white mb-1 sm:mb-2 fifa-hub-title">
            Select a Region to Explore
          </h2>
          <p className="text-xs sm:text-sm text-white/80 fifa-hub-subtitle">
            FIFA Club World Cup 2025™ - Choose your destination
          </p>
        </div>

        {/* Mobile Swipeable Cards (< 768px) - WL-004 Enhancement */}
        <div className="md:hidden">
          <SwipeableRegionCards
            regions={regions}
            currentRegion={currentRegion || ''}
            onRegionChange={handleRegionSelect}
          />

          {/* Accessibility Fallback - Dropdown for screen readers */}
          <div className="sr-only">
            <label htmlFor="region-select" className="block text-sm font-medium text-gray-700 mb-2">
              Select Region
            </label>
            <select
              id="region-select"
              value={currentRegion || ''}
              onChange={(e) => handleRegionSelect(e.target.value)}
              className="w-full bg-white border border-gray-300 rounded-lg px-4 py-3"
            >
              {regions.map((region) => (
                <option key={region.id} value={region.id}>
                  {region.name} - {region.places?.length || 0} places
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Desktop Cards (≥ 768px) */}
        <div
          ref={scrollContainerRef}
          className="hidden md:flex gap-4 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#9CA3AF #F3F4F6',
          }}
        >
          {regions.map((region) => (
            <CityCard
              key={region.id}
              region={region}
              isActive={currentRegion === region.id}
              onClick={() => handleRegionSelect(region.id)}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

interface CityCardProps {
  region: CityRegion;
  isActive: boolean;
  onClick: () => void;
}

function CityCard({ region, isActive, onClick }: CityCardProps) {
  return (
    <div
      data-region={region.id}
      onClick={onClick}
      className={cn(
        'relative min-w-[200px] sm:min-w-[240px] p-4 sm:p-6 bg-black/40 backdrop-blur-sm rounded-xl shadow-md cursor-pointer transition-all duration-300 border-2 overflow-hidden group fifa-city-card',
        isActive
          ? 'border-[#FFD700] bg-gradient-to-br from-[#FFD700]/20 to-[#DC2626]/20 text-white transform -translate-y-1 shadow-lg'
          : 'border-white/20 hover:border-[#FFD700]/50 hover:-translate-y-1 hover:shadow-lg'
      )}
    >
      {/* FIFA Animated top border */}
      <div
        className={cn(
          'absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#FFD700] to-[#DC2626] transform transition-transform duration-300',
          isActive ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100'
        )}
      />

      {/* FIFA Background pattern for active state */}
      {isActive && (
        <div className="absolute inset-0 opacity-20">
          <svg
            className="w-full h-full"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <circle cx="20" cy="20" r="2" fill="#FFD700" />
            <circle cx="80" cy="40" r="3" fill="#DC2626" />
            <circle cx="40" cy="80" r="1" fill="#FFD700" />
          </svg>
        </div>
      )}

      <div className="relative z-10">
        {/* Icon and title */}
        <div className="flex items-center mb-2 sm:mb-3">
          <span className="text-xl sm:text-2xl mr-2 sm:mr-3 fifa-region-icon">{region.icon}</span>
          <h3 className={cn(
            'text-base sm:text-lg font-semibold leading-tight',
            isActive ? 'text-[#FFD700]' : 'text-white'
          )}>
            {region.name}
          </h3>
        </div>

        {/* Description */}
        <p className={cn(
          'text-xs sm:text-sm mb-3 sm:mb-4 leading-relaxed',
          isActive ? 'text-white/90' : 'text-white/70'
        )}>
          {region.description}
        </p>

        {/* Stats */}
        <div className="flex items-center justify-between text-xs">
          <div className={cn(
            'flex items-center space-x-1',
            isActive ? 'text-white/80' : 'text-white/60'
          )}>
            <span>📍</span>
            <span className="text-xs">{region.places?.length ?? 0} places</span>
          </div>

          <div className={cn(
            'px-2 py-1 rounded-full text-xs font-medium min-w-[60px] text-center',
            isActive
              ? 'bg-gradient-to-r from-[#FFD700] to-[#DC2626] text-black'
              : 'bg-white/20 text-white'
          )}>
            {isActive ? 'Active' : 'Explore'}
          </div>
        </div>
      </div>

      {/* FIFA Hover effect overlay */}
      <div className={cn(
        'absolute inset-0 bg-gradient-to-br from-[#FFD700]/5 to-[#DC2626]/5 opacity-0 transition-opacity duration-300',
        !isActive && 'group-hover:opacity-100'
      )} />

      {/* FIFA Design System Styles */}
      <style jsx>{`
        .fifa-city-hub {
          position: relative;
        }

        .fifa-hub-title {
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .fifa-hub-subtitle {
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .fifa-city-card {
          /* Mobile-first touch optimization */
          touch-action: manipulation;
          -webkit-tap-highlight-color: transparent;
        }

        .fifa-city-card:hover {
          transform: translateY(-2px) scale(1.01);
          filter: drop-shadow(0 4px 12px rgba(255, 215, 0, 0.2));
        }

        /* Enhanced mobile hover/active states */
        @media (max-width: 768px) {
          .fifa-city-card:hover {
            transform: translateY(-1px) scale(1.005);
            filter: drop-shadow(0 2px 8px rgba(255, 215, 0, 0.15));
          }

          .fifa-city-card:active {
            transform: translateY(0) scale(0.98);
            transition: transform 0.1s ease;
          }
        }

        /* Desktop hover effects */
        @media (min-width: 769px) {
          .fifa-city-card:hover {
            transform: translateY(-4px) scale(1.02);
            filter: drop-shadow(0 8px 16px rgba(255, 215, 0, 0.2));
          }
        }

        .fifa-city-card.active {
          filter: drop-shadow(0 8px 16px rgba(255, 215, 0, 0.4));
        }

        .fifa-region-icon {
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        /* Mobile text optimization */
        @media (max-width: 640px) {
          .fifa-hub-title {
            line-height: 1.2;
          }

          .fifa-hub-subtitle {
            line-height: 1.3;
          }
        }
      `}</style>
    </div>
  );
}
