/**
 * Enhanced DemoMap Component for WL-004B Performance Optimization
 *
 * Features:
 * - Mobile gesture system integration from WL-004
 * - Performance monitoring and metrics collection
 * - Touch-optimized controls (44px+ targets)
 * - Enhanced loading states and accessibility
 * - Collapsible legend with progressive disclosure
 */

import { useState, useCallback, useEffect } from 'react';
import { MapPin, Navigation, Satellite, Car, Zap, Plus, Minus, ChevronUp, ChevronDown } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { useWanderlustStore } from '~/stores/wanderlust';
import { useGestures } from '~/hooks/useGestures';
// Removed useMapPerformance - simplified architecture
import type { VisitedPlace } from '~/types/wanderlust';
import { cn } from '~/lib/utils';

interface DemoMapProps {
  onMapLoad: () => void;
  isLoaded: boolean;
}

// Category colors for markers
const categoryColors: Record<string, string> = {
  food: '#f39c12',
  landmark: '#9b59b6',
  museum: '#3498db',
  park: '#27ae60',
  accommodation: '#e74c3c',
  transport: '#34495e',
  entertainment: '#e91e63',
  shopping: '#ff9800',
};

export function DemoMap({ onMapLoad, isLoaded }: DemoMapProps) {
  const [selectedMarker, setSelectedMarker] = useState<VisitedPlace | null>(null);
  const [mapType, setMapType] = useState<'roadmap' | 'satellite'>('roadmap');
  const [showTraffic, setShowTraffic] = useState(false);
  const [isMapInteractionActive, setIsMapInteractionActive] = useState(false);
  const [isLegendCollapsed, setIsLegendCollapsed] = useState(true); // Collapsed by default on mobile
  const [demoZoom, setDemoZoom] = useState(10);

  // Simplified - no performance monitoring needed
  const measureLoadTime = () => () => {}; // No-op function
  const measureInteractionLatency = () => () => {}; // No-op function

  const {
    filteredPlaces,
    mapCenter,
    mapZoom,
    selectedPlace,
    setSelectedPlace,
  } = useWanderlustStore();

  // Integrate with WL-004 gesture system
  const { ref: gestureRef } = useGestures({
    onLongPress: () => {
      console.log('Long press detected on demo map');
      // Could implement pin dropping functionality here
    },
    onDragStart: () => {
      setIsMapInteractionActive(true);
      if (typeof document !== 'undefined') {
        document.body.style.touchAction = 'none';
      }
    },
    onDragEnd: () => {
      setIsMapInteractionActive(false);
      if (typeof document !== 'undefined') {
        document.body.style.touchAction = 'auto';
      }
    },
    config: {
      longPressDelay: 500,
      swipeThreshold: 44, // 44px minimum for accessibility
    },
  });

  // Handle marker click with performance measurement
  const handleMarkerClick = useCallback((place: VisitedPlace) => {
    const endMeasurement = measureInteractionLatency();
    setSelectedMarker(place);
    setSelectedPlace(place);
    endMeasurement();
  }, [measureInteractionLatency, setSelectedPlace]);

  // Handle map click (close info window)
  const handleMapClick = useCallback(() => {
    setSelectedMarker(null);
  }, []);

  // Get current location (demo) with performance measurement
  const getCurrentLocation = useCallback(() => {
    const endMeasurement = measureInteractionLatency();
    console.log('Demo: Getting current location...');
    // Demo: just center on NYC
    endMeasurement();
  }, [measureInteractionLatency]);

  // Toggle satellite view with performance measurement
  const toggleSatellite = useCallback(() => {
    const endMeasurement = measureInteractionLatency();
    setMapType(mapType === 'roadmap' ? 'satellite' : 'roadmap');
    endMeasurement();
  }, [mapType, measureInteractionLatency]);

  // Toggle traffic layer with performance measurement
  const toggleTraffic = useCallback(() => {
    const endMeasurement = measureInteractionLatency();
    setShowTraffic(!showTraffic);
    endMeasurement();
  }, [showTraffic, measureInteractionLatency]);

  // Demo zoom controls with performance measurement
  const handleZoomIn = useCallback(() => {
    const endMeasurement = measureInteractionLatency();
    setDemoZoom(prev => Math.min(prev + 1, 18));
    endMeasurement();
  }, [measureInteractionLatency]);

  const handleZoomOut = useCallback(() => {
    const endMeasurement = measureInteractionLatency();
    setDemoZoom(prev => Math.max(prev - 1, 1));
    endMeasurement();
  }, [measureInteractionLatency]);

  // Toggle legend collapse
  const toggleLegend = useCallback(() => {
    setIsLegendCollapsed(!isLegendCollapsed);
  }, [isLegendCollapsed]);

  // Calculate marker position on the demo map
  const getMarkerPosition = useCallback((place: VisitedPlace) => {
    // Convert lat/lng to pixel position on our demo map
    const mapWidth = 800;
    const mapHeight = 600;

    // Simple projection (this is just for demo purposes)
    const lat = place.coordinates.latitude;
    const lng = place.coordinates.longitude;

    // Rough conversion for NYC area with zoom factor
    const zoomFactor = Math.pow(2, (demoZoom - 10) / 3);
    const x = ((lng + 74.2) / 0.6) * mapWidth * zoomFactor;
    const y = ((40.9 - lat) / 0.4) * mapHeight * zoomFactor;

    return { x: Math.max(0, Math.min(mapWidth, x)), y: Math.max(0, Math.min(mapHeight, y)) };
  }, [demoZoom]);

  // Simulate map load with performance monitoring
  useEffect(() => {
    if (!isLoaded) {
      const timer = setTimeout(() => {
        onMapLoad();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isLoaded, onMapLoad]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typeof document !== 'undefined') {
        document.body.style.touchAction = 'auto';
      }
    };
  }, []);

  return (
    <div className="relative h-full bg-gray-100 rounded-lg overflow-hidden shadow-lg">
      {/* Demo Map Background with gesture integration */}
      <div ref={gestureRef as React.RefObject<HTMLDivElement>} className="h-full">
        <div
          className={`w-full h-full relative cursor-pointer transition-all duration-300 ${
            mapType === 'satellite' ? 'bg-gray-800' : 'bg-blue-50'
          }`}
          onClick={handleMapClick}
          style={{
            backgroundImage: mapType === 'roadmap'
              ? `linear-gradient(45deg, #e3f2fd 25%, transparent 25%),
                 linear-gradient(-45deg, #e3f2fd 25%, transparent 25%),
                 linear-gradient(45deg, transparent 75%, #e3f2fd 75%),
                 linear-gradient(-45deg, transparent 75%, #e3f2fd 75%)`
              : `radial-gradient(circle at 30% 40%, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%)`,
            backgroundSize: mapType === 'roadmap' ? '20px 20px' : 'auto',
            backgroundPosition: mapType === 'roadmap' ? '0 0, 0 10px, 10px -10px, -10px 0px' : 'center',
            transform: `scale(${1 + (demoZoom - 10) * 0.1})`, // Simple zoom effect
          }}
        >
          {/* Demo Map Grid */}
          {mapType === 'roadmap' && (
            <div className="absolute inset-0 opacity-20">
              <svg width="100%" height="100%" className="text-blue-300">
                <defs>
                  <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
              </svg>
            </div>
          )}

          {/* Demo Roads/Streets */}
          {mapType === 'roadmap' && (
            <div className="absolute inset-0">
              <svg width="100%" height="100%" className="text-gray-400">
                <line x1="0" y1="30%" x2="100%" y2="30%" stroke="currentColor" strokeWidth="3" opacity="0.6"/>
                <line x1="0" y1="60%" x2="100%" y2="60%" stroke="currentColor" strokeWidth="2" opacity="0.6"/>
                <line x1="20%" y1="0" x2="20%" y2="100%" stroke="currentColor" strokeWidth="3" opacity="0.6"/>
                <line x1="70%" y1="0" x2="70%" y2="100%" stroke="currentColor" strokeWidth="2" opacity="0.6"/>
              </svg>
            </div>
          )}

          {/* Traffic Layer Overlay */}
          {showTraffic && (
            <div className="absolute inset-0 bg-red-500 opacity-10 animate-pulse">
              <div className="absolute top-1/3 left-0 right-0 h-1 bg-red-500 opacity-60"></div>
              <div className="absolute top-2/3 left-0 right-0 h-1 bg-yellow-500 opacity-60"></div>
            </div>
          )}

          {/* Demo Markers */}
          {filteredPlaces.slice(0, 20).map((place) => {
            const position = getMarkerPosition(place);
            const color = categoryColors[place.category] || '#e74c3c';

            return (
              <div
                key={place.id}
                className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-200 hover:scale-110 ${
                  selectedPlace?.id === place.id ? 'animate-bounce scale-125' : ''
                }`}
                style={{
                  left: `${(position.x / 800) * 100}%`,
                  top: `${(position.y / 600) * 100}%`,
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleMarkerClick(place);
                }}
              >
                <div
                  className="w-6 h-6 rounded-full border-2 border-white shadow-lg flex items-center justify-center text-white text-xs font-bold hover:shadow-xl transition-shadow"
                  style={{ backgroundColor: color }}
                >
                  {place.icon || '📍'}
                </div>
              </div>
            );
          })}

          {/* Enhanced Info Window */}
          {selectedMarker && (
            <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-4 max-w-xs z-10 border">
              <button
                onClick={() => setSelectedMarker(null)}
                className="absolute top-2 right-2 text-gray-400 hover:text-gray-600 w-6 h-6 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
                aria-label="Close"
              >
                ×
              </button>
              <h3 className="font-semibold text-gray-900 mb-2 pr-6">
                {selectedMarker.name}
              </h3>
              <p className="text-sm text-gray-600 mb-3">
                {selectedMarker.description.en}
              </p>
              <div className="flex items-center justify-between mb-3">
                <span className="text-xs bg-gray-100 px-2 py-1 rounded-full">
                  {selectedMarker.category}
                </span>
                {selectedMarker.rating && (
                  <div className="flex items-center">
                    <span className="text-yellow-500">★</span>
                    <span className="text-sm ml-1">{selectedMarker.rating}</span>
                  </div>
                )}
              </div>
              {/* Action buttons */}
              <div className="flex gap-2">
                <button className="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors">
                  Directions (Demo)
                </button>
                <button className="text-xs bg-gray-600 text-white px-3 py-1 rounded hover:bg-gray-700 transition-colors">
                  Details
                </button>
              </div>
            </div>
          )}

          {/* Demo Watermark */}
          <div className="absolute bottom-4 left-4 bg-black/20 text-white px-3 py-1 rounded text-xs backdrop-blur-sm">
            🗺️ Demo Map - Google Maps integration available with API key
          </div>
        </div>
      </div>

      {/* Enhanced Map Controls - Modern FAB Design with 44px+ touch targets */}
      <div className="absolute top-4 right-4 flex flex-col gap-2 z-10">
        {/* Zoom Controls */}
        <div className="bg-white shadow-lg rounded-lg overflow-hidden">
          <button
            onClick={handleZoomIn}
            className="w-12 h-12 flex items-center justify-center hover:bg-gray-50 active:scale-95 transition-all duration-150 border-b border-gray-200"
            aria-label="Zoom in"
            title="Zoom in (Demo)"
          >
            <Plus className="h-5 w-5 text-gray-700" />
          </button>
          <button
            onClick={handleZoomOut}
            className="w-12 h-12 flex items-center justify-center hover:bg-gray-50 active:scale-95 transition-all duration-150"
            aria-label="Zoom out"
            title="Zoom out (Demo)"
          >
            <Minus className="h-5 w-5 text-gray-700" />
          </button>
        </div>

        {/* Current Location - Enhanced FAB */}
        <button
          onClick={getCurrentLocation}
          className="bg-white shadow-lg rounded-lg p-3 min-w-[48px] min-h-[48px] flex items-center justify-center hover:bg-gray-50 active:scale-95 transition-all duration-150 hover:shadow-xl"
          aria-label="Current location"
          title="My Location (Demo)"
        >
          <Navigation className="h-5 w-5 text-blue-600" />
        </button>

        {/* Satellite Toggle - Enhanced FAB */}
        <button
          onClick={toggleSatellite}
          className={cn(
            "bg-white shadow-lg rounded-lg p-3 min-w-[48px] min-h-[48px] flex items-center justify-center hover:bg-gray-50 active:scale-95 transition-all duration-150 hover:shadow-xl",
            mapType === 'satellite' && "bg-blue-100 text-blue-600"
          )}
          aria-label="Toggle satellite view"
          title="Toggle Satellite View"
        >
          <Satellite className="h-5 w-5" />
        </button>

        {/* Traffic Toggle - Enhanced FAB */}
        <button
          onClick={toggleTraffic}
          className={cn(
            "bg-white shadow-lg rounded-lg p-3 min-w-[48px] min-h-[48px] flex items-center justify-center hover:bg-gray-50 active:scale-95 transition-all duration-150 hover:shadow-xl",
            showTraffic && "bg-red-100 text-red-600"
          )}
          aria-label="Toggle traffic"
          title="Toggle Traffic (Demo)"
        >
          <Car className="h-5 w-5" />
        </button>
      </div>

      {/* Collapsible Legend with Progressive Disclosure */}
      <div className="absolute bottom-4 right-4 z-10">
        <div className="bg-white/95 backdrop-blur-sm rounded-lg shadow-md overflow-hidden max-w-xs">
          {/* Legend Header - Always Visible */}
          <button
            onClick={toggleLegend}
            className="w-full flex items-center justify-between p-3 hover:bg-gray-50 transition-colors"
            aria-label={isLegendCollapsed ? "Expand legend" : "Collapse legend"}
          >
            <div className="flex items-center">
              <Zap className="h-4 w-4 mr-2 text-blue-600" />
              <h4 className="text-sm font-semibold">Categories</h4>
            </div>
            {isLegendCollapsed ? (
              <ChevronUp className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </button>

          {/* Legend Content - Collapsible */}
          <div className={cn(
            "transition-all duration-300 ease-in-out overflow-hidden",
            isLegendCollapsed ? "max-h-0" : "max-h-96"
          )}>
            <div className="px-3 pb-3 border-t border-gray-200">
              <div className="grid grid-cols-2 gap-1 text-xs mb-2">
                {Object.entries(categoryColors).slice(0, 6).map(([category, color]) => (
                  <div key={category} className="flex items-center py-1">
                    <div
                      className="w-3 h-3 rounded-full mr-2 shadow-sm"
                      style={{ backgroundColor: color }}
                    />
                    <span className="capitalize text-gray-700">{category}</span>
                  </div>
                ))}
              </div>
              <div className="text-xs text-gray-500 pt-2 border-t border-gray-100">
                {filteredPlaces.length} places shown • Zoom: {demoZoom}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Loading overlay with skeleton UI */}
      {!isLoaded && (
        <div className="absolute inset-0 bg-gray-100 rounded-lg overflow-hidden">
          {/* Skeleton map container */}
          <div className="relative h-full bg-gradient-to-br from-gray-200 to-gray-300 animate-pulse">
            {/* Skeleton controls */}
            <div className="absolute top-4 right-4 flex flex-col gap-2">
              {[1, 2, 3, 4, 5].map((i) => (
                <div
                  key={i}
                  className="w-12 h-12 bg-white/80 rounded-lg shadow-lg animate-pulse"
                />
              ))}
            </div>

            {/* Skeleton legend */}
            <div className="absolute bottom-4 right-4 bg-white/80 rounded-lg p-3 shadow-md">
              <div className="h-4 bg-gray-300 rounded mb-2 w-20 animate-pulse" />
              <div className="grid grid-cols-2 gap-1">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="flex items-center">
                    <div className="w-3 h-3 bg-gray-300 rounded-full mr-2 animate-pulse" />
                    <div className="h-3 bg-gray-300 rounded w-12 animate-pulse" />
                  </div>
                ))}
              </div>
            </div>

            {/* Loading indicator */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center bg-white/90 backdrop-blur-sm rounded-lg p-6 shadow-lg">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                <p className="text-sm text-gray-600">Loading demo map...</p>
                <p className="text-xs text-gray-500 mt-1">
                  Optimizing for mobile performance...
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Simplified demo - no performance monitoring */}
    </div>
  );
}
