import { Settings, Share2, Download } from 'lucide-react';
import { Button } from '~/components/ui/button';

export function WanderlustHeader() {
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Wanderlust Explorer',
          text: 'Check out my travel memories and recommendations!',
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      // You could show a notification here
    }
  };

  const handleExport = () => {
    // This would export the travel data as JSON or PDF
    console.log('Export functionality to be implemented');
  };

  return (
    <header className="relative bg-gradient-to-r from-[#000000] to-[#DC2626] text-white overflow-hidden fifa-wanderlust-header">
      {/* FIFA Brand Background with Glassmorphism */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#FFD700]/10 to-[#DC2626]/10 backdrop-blur-sm"></div>

      {/* FIFA Animated background pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-0 left-0 w-full h-full">
          <svg
            className="w-full h-full"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <circle cx="20" cy="20" r="2" fill="#FFD700" className="fifa-floating-particle">
              <animate
                attributeName="opacity"
                values="0.3;1;0.3"
                dur="3s"
                repeatCount="indefinite"
              />
            </circle>
            <circle cx="80" cy="40" r="3" fill="#DC2626" className="fifa-floating-particle">
              <animate
                attributeName="opacity"
                values="0.5;1;0.5"
                dur="4s"
                repeatCount="indefinite"
              />
            </circle>
            <circle cx="40" cy="80" r="1" fill="#FFD700" className="fifa-floating-particle">
              <animate
                attributeName="opacity"
                values="0.2;1;0.2"
                dur="2s"
                repeatCount="indefinite"
              />
            </circle>
            <circle cx="60" cy="30" r="2" fill="#DC2626" className="fifa-floating-particle">
              <animate
                attributeName="opacity"
                values="0.4;1;0.4"
                dur="3.5s"
                repeatCount="indefinite"
              />
            </circle>
          </svg>
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between py-6">
          {/* Left side - Travel Context */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-white/80">
              <span className="text-2xl">🗺️</span>
              <span className="hidden sm:inline text-sm">Travel Experience</span>
            </div>
          </div>

          {/* Center - FIFA Title */}
          <div className="text-center">
            <h2 className="text-2xl sm:text-3xl font-bold mb-1 fifa-title-gradient">
              Wanderlust Explorer
            </h2>
            <p className="text-white/90 text-xs sm:text-sm fifa-subtitle">
              FIFA Club World Cup 2025™ Travel Experience
            </p>
          </div>

          {/* Right side - FIFA Actions */}
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleShare}
              className="text-white/80 hover:text-[#FFD700] hover:bg-[#FFD700]/10 transition-all duration-300 fifa-action-btn"
            >
              <Share2 className="h-4 w-4" />
              <span className="hidden sm:ml-2 sm:inline">Share</span>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleExport}
              className="text-white/80 hover:text-[#FFD700] hover:bg-[#FFD700]/10 transition-all duration-300 fifa-action-btn"
            >
              <Download className="h-4 w-4" />
              <span className="hidden sm:ml-2 sm:inline">Export</span>
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="text-white/80 hover:text-[#FFD700] hover:bg-[#FFD700]/10 transition-all duration-300 fifa-action-btn"
            >
              <Settings className="h-4 w-4" />
              <span className="hidden sm:ml-2 sm:inline">Settings</span>
            </Button>
          </div>
        </div>

        {/* FIFA Stats bar */}
        <div className="border-t border-[#FFD700]/30 pt-4 pb-2">
          <div className="flex justify-center space-x-8 text-sm">
            <div className="text-center fifa-stat-item">
              <div className="text-2xl font-bold text-[#FFD700] fifa-stat-number">150+</div>
              <div className="text-white/80">Places Visited</div>
            </div>
            <div className="text-center fifa-stat-item">
              <div className="text-2xl font-bold text-[#FFD700] fifa-stat-number">8</div>
              <div className="text-white/80">Cities Explored</div>
            </div>
            <div className="text-center fifa-stat-item">
              <div className="text-2xl font-bold text-[#FFD700] fifa-stat-number">3</div>
              <div className="text-white/80">Countries</div>
            </div>
          </div>
        </div>
      </div>

      {/* FIFA Gradient overlay for depth */}
      <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-[#FFD700]/10 to-transparent"></div>

      {/* FIFA Design System Styles */}
      <style jsx>{`
        .fifa-wanderlust-header {
          position: relative;
          overflow: hidden;
        }

        .fifa-floating-particle {
          filter: drop-shadow(0 0 4px currentColor);
        }

        .fifa-title-gradient {
          background: linear-gradient(135deg, #FFD700 0%, #DC2626 100%);
          background-size: 200% 200%;
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          color: transparent;
          animation: fifa-gradient-shift 4s ease infinite;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        @supports not (-webkit-background-clip: text) {
          .fifa-title-gradient {
            background: none;
            color: #FFD700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
          }
        }

        .fifa-subtitle {
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .fifa-nav-link:hover {
          transform: scale(1.05);
          filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.4));
        }

        .fifa-action-btn:hover {
          transform: scale(1.05);
          filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.3));
        }

        .fifa-stat-item:hover .fifa-stat-number {
          transform: scale(1.1);
          filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
          transition: all 0.3s ease;
        }

        @keyframes fifa-gradient-shift {
          0%, 100% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
        }
      `}</style>
    </header>
  );
}
