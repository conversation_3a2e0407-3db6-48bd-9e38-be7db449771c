/**
 * Enhanced Search Interface Component
 *
 * Unified search UI that adapts to different search modes:
 * - route-planning: Focus on waypoint addition and route optimization
 * - venue-discovery: Focus on exploration and venue details
 * - general: Basic search functionality
 */

import { useState, useCallback, useMemo } from 'react';
import { Search, Filter, History, X, MapPin, Star, ChevronDown, ChevronUp } from 'lucide-react';
import { Input } from '~/components/ui/input';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import { UnifiedPlaceCard, type PlaceCardAction } from './UnifiedPlaceCard';
import { useEnhancedPlaceSearch } from '~/hooks/useEnhancedPlaceSearch';
import { cn } from '~/lib/utils';
import type { VisitedPlace, CityRegion } from '~/types/wanderlust';
import { AdvancedFilteringUI } from './filters/AdvancedFilteringUI';
import { useTranslation } from 'react-i18next';

export type SearchMode = 'route-planning' | 'venue-discovery' | 'general';

export interface EnhancedSearchInterfaceProps {
  searchMode: SearchMode;
  regions: CityRegion[];
  onPlaceSelect: (place: VisitedPlace) => void;
  enableFilters?: boolean;
  enableHistory?: boolean;
  enableCategories?: boolean;
  enableAdvancedFilters?: boolean;
  placeholder?: string;
  maxResults?: number;
  className?: string;
}

// Category filters with FIFA design system colors (Labels will be translated)
const categoryFilters = [
  { id: 'all', labelKey: 'category.all', icon: '🌍', color: '#FFD700' },
  { id: 'food', labelKey: 'category.food', icon: '🍽️', color: '#FFD700' },
  { id: 'landmark', labelKey: 'category.landmark', icon: '🏛️', color: '#DC2626' },
  { id: 'museum', labelKey: 'category.museum', icon: '🏛️', color: '#1E40AF' },
  { id: 'park', labelKey: 'category.park', icon: '🌳', color: '#059669' },
  { id: 'accommodation', labelKey: 'category.accommodation', icon: '🏨', color: '#7C2D12' },
  { id: 'transport', labelKey: 'category.transport', icon: '🚇', color: '#374151' },
  { id: 'entertainment', labelKey: 'category.entertainment', icon: '🎭', color: '#BE185D' },
  { id: 'shopping', labelKey: 'category.shopping', icon: '🛍️', color: '#EA580C' },
];

export function EnhancedSearchInterface({
  searchMode,
  regions,
  onPlaceSelect,
  enableFilters = true,
  enableHistory = true,
  enableCategories = true,
  enableAdvancedFilters = true,
  placeholder,
  maxResults = 20,
  className,
}: EnhancedSearchInterfaceProps) {
  const [activeTab, setActiveTab] = useState<'search' | 'history' | 'categories'>('search');

  // Enhanced search hook
  const { searchState, searchActions } = useEnhancedPlaceSearch(regions, {
    searchMode: 'hybrid',
    enableCategoryFilter: enableCategories,
    enableLocationBias: true,
    cacheResults: true,
    maxResults,
  });

  const { t } = useTranslation('search'); // Use the translation hook

  // Mode-specific configuration
  const modeConfig = useMemo(() => {
    switch (searchMode) {
      case 'route-planning':
        return {
          placeholder: placeholder || t('placeholder.routePlanning'),
          primaryAction: t('action.addToRoute'),
          primaryIcon: MapPin,
          showDistance: true,
          showEstimatedTime: true,
          cardVariant: 'search-result' as const,
          searchHint: t('hint.routePlanning'),
        };
      case 'venue-discovery':
        return {
          placeholder: placeholder || t('placeholder.venueDiscovery'),
          primaryAction: t('action.exploreVenue'),
          primaryIcon: Star,
          showDistance: true,
          showEstimatedTime: false,
          cardVariant: 'venue-detail' as const,
          searchHint: t('hint.venueDiscovery'),
        };
      case 'general':
      default:
        return {
          placeholder: placeholder || t('placeholder.general'),
          primaryAction: t('action.select'),
          primaryIcon: MapPin,
          showDistance: false,
          showEstimatedTime: false,
          cardVariant: 'search-result' as const,
          searchHint: t('hint.general'),
        };
    }
  }, [searchMode, placeholder, t]);

  // Handle search input
  const handleSearchChange = useCallback((value: string) => {
    searchActions.setQuery(value);
    if (value.trim()) {
      searchActions.search(value);
    } else {
      searchActions.clearResults();
    }
  }, [searchActions]);

  // Handle category filter
  const handleCategoryFilter = useCallback((categoryId: string) => {
    searchActions.setCategory(categoryId);
    if (searchState.query) {
      searchActions.search(searchState.query);
    }
  }, [searchActions, searchState.query]);

  // Handle place selection
  const handlePlaceSelect = useCallback((place: VisitedPlace) => {
    onPlaceSelect(place);
    searchActions.addToHistory(place.name);
  }, [onPlaceSelect, searchActions]);

  // Create place card actions based on mode
  const createPlaceActions = useCallback((place: VisitedPlace): PlaceCardAction[] => {
    const actions: PlaceCardAction[] = [
      {
        type: 'add',
        label: modeConfig.primaryAction,
        icon: modeConfig.primaryIcon,
        onClick: handlePlaceSelect,
        variant: 'default',
      },
    ];

    if (searchMode === 'venue-discovery') {
      actions.push({
        type: 'favorite',
        label: t('action.favorite'), // Translate "Favorite"
        icon: Star,
        onClick: (place) => {
          console.log('Add to favorites:', place.name);
          // TODO: Implement favorites functionality
        },
        variant: 'outline',
      });
    }

    return actions;
  }, [modeConfig, searchMode, handlePlaceSelect]);

  // Render search results
  const renderSearchResults = () => {
    if (searchState.isSearching) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#FFD700]"></div>
          <span className="ml-2 text-white/70">Searching...</span>
        </div>
      );
    }

    if (!searchState.query) {
      return (
        <div className="text-center py-8 text-white/70">
          <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">{modeConfig.searchHint}</p>
        </div>
      );
    }

    if (searchState.results.length === 0) {
      return (
        <div className="text-center py-8 text-white/70">
          <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No results found for "{searchState.query}"</p>
          <p className="text-xs mt-1">Try different keywords or check spelling</p>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {searchState.results.slice(0, maxResults).map((result, index) => (
          <UnifiedPlaceCard
            key={`${result.place.id}-${index}`}
            place={result.place}
            variant={modeConfig.cardVariant}
            actions={createPlaceActions(result.place)}
            distance={result.distance}
            estimatedTime={modeConfig.showEstimatedTime ? '15 min' : undefined}
            onSelect={handlePlaceSelect}
            enableSwipeActions={true}
          />
        ))}
      </div>
    );
  };

  // Render search history
  const renderSearchHistory = () => {
    if (searchState.searchHistory.length === 0) {
      return (
        <div className="text-center py-8 text-white/70">
          <History className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No search history yet</p>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {searchState.searchHistory.map((query, index) => (
          <div
            key={index}
            className="flex items-center justify-between p-3 bg-white/5 rounded-lg cursor-pointer hover:bg-white/10 transition-colors"
            onClick={() => handleSearchChange(query)}
          >
            <div className="flex items-center">
              <History className="h-4 w-4 text-white/50 mr-2" />
              <span className="text-white text-sm">{query}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                searchActions.removeFromHistory(query); // Implement removal from history
              }}
              className="h-6 w-6 p-0 text-white/50 hover:text-white"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ))}
      </div>
    );
  };

  // Render category filters
  const renderCategoryFilters = () => {
    return (
      <div className="grid grid-cols-2 gap-2">
        {categoryFilters.map((category) => (
          <Button
            key={category.id}
            variant={searchState.selectedCategory === category.id ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleCategoryFilter(category.id)}
            className={cn(
              "justify-start text-left h-auto p-3",
              searchState.selectedCategory === category.id
                ? "bg-[#FFD700] text-black hover:bg-[#FFD700]/90"
                : "border-white/20 text-white hover:bg-white/10"
            )}
          >
            <span className="text-base mr-2">{category.icon}</span>
            <span className="text-sm">{t(category.labelKey)}</span> {/* Use translated label */}
          </Button>
        ))}
      </div>
    );
  };

  // Handle advanced filter changes
  const handlePriceRangeChange = useCallback((range: [number, number]) => {
    searchActions.setPriceRange(range);
    if (searchState.query) {
      searchActions.search(searchState.query);
    }
  }, [searchActions, searchState.query]);

  const handleRatingChange = useCallback((rating: number) => {
    searchActions.setMinRating(rating);
    if (searchState.query) {
      searchActions.search(searchState.query);
    }
  }, [searchActions, searchState.query]);

  const handleDistanceRadiusChange = useCallback((radius: number) => {
    searchActions.setDistanceRadius(radius);
    if (searchState.query) {
      searchActions.search(searchState.query);
    }
  }, [searchActions, searchState.query]);

  return (
    <div className={cn("bg-white/5 backdrop-blur-sm rounded-lg border border-white/10", className)}>
      {/* Search Header */}
      <div className="p-4 border-b border-white/10">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/60" />
          <Input
            placeholder={modeConfig.placeholder}
            value={searchState.query}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10 bg-white/5 border-white/20 text-white placeholder-white/50 focus:border-[#FFD700]"
          />
          {searchState.query && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                searchActions.clearResults();
                searchActions.setQuery('');
              }}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 text-white/50 hover:text-white"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Filter Toggle */}
        {enableFilters && (
          <div className="flex items-center justify-between mt-3">
            <div className="flex items-center gap-2">
              {searchState.selectedCategory !== 'all' && (
                <Badge variant="outline" className="border-[#FFD700]/30 text-[#FFD700]">
                  {t(categoryFilters.find(c => c.id === searchState.selectedCategory)?.labelKey || '')}
                </Badge>
              )}
              {searchState.results.length > 0 && (
                <span className="text-xs text-white/70">
                  {searchState.results.length} result{searchState.results.length !== 1 ? 's' : ''}
                </span>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => searchActions.toggleAdvancedFilters()}
              className="text-white/70 hover:text-white"
            >
              <Filter className="h-4 w-4 mr-1" />
              {t('filters.label')} {/* Translate "Filters" */}
              {searchState.showAdvancedFilters ? (
                <ChevronUp className="ml-1 h-3 w-3" />
              ) : (
                <ChevronDown className="ml-1 h-3 w-3" />
              )}
            </Button>
          </div>
        )}

        {/* Advanced Filters */}
        {enableAdvancedFilters && searchState.showAdvancedFilters && (
          <div className="mt-3">
            <AdvancedFilteringUI
              priceRange={searchState.priceRange}
              minRating={searchState.minRating}
              distanceRadius={searchState.distanceRadius}
              onPriceRangeChange={handlePriceRangeChange}
              onRatingChange={handleRatingChange}
              onDistanceRadiusChange={handleDistanceRadiusChange}
              onResetAllFilters={searchActions.resetAllFilters}
              defaultExpanded={true}
            />
          </div>
        )}
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-white/5 border-b border-white/10 rounded-none">
          <TabsTrigger value="search" className="data-[state=active]:bg-[#FFD700] data-[state=active]:text-black">
            {t('tabs.search')} {/* Translate "Search" */}
          </TabsTrigger>
          {enableHistory && (
            <TabsTrigger value="history" className="data-[state=active]:bg-[#FFD700] data-[state=active]:text-black">
              {t('tabs.history')} {/* Translate "History" */}
            </TabsTrigger>
          )}
          {enableCategories && (
            <TabsTrigger value="categories" className="data-[state=active]:bg-[#FFD700] data-[state=active]:text-black">
              {t('tabs.categories')} {/* Translate "Categories" */}
            </TabsTrigger>
          )}
        </TabsList>

        {/* Content */}
        <div className="p-4">
          <TabsContent value="search" className="mt-0">
            <ScrollArea className="h-96">
              {renderSearchResults()}
            </ScrollArea>
          </TabsContent>

          {enableHistory && (
            <TabsContent value="history" className="mt-0">
              <ScrollArea className="h-96">
                {renderSearchHistory()}
              </ScrollArea>
            </TabsContent>
          )}

          {enableCategories && (
            <TabsContent value="categories" className="mt-0">
              <ScrollArea className="h-96">
                {renderCategoryFilters()}
              </ScrollArea>
            </TabsContent>
          )}
        </div>
      </Tabs>
    </div>
  );
}
