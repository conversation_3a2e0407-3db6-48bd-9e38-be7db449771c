/**
 * Place Management Panel Component
 *
 * Configurable place list management with different modes:
 * - waypoints: Drag-and-drop reordering, route optimization
 * - favorites: Grouping by category, bulk operations
 * - search-results: Relevance sorting, quick add actions
 */

import React, { useState, useCallback, useMemo } from 'react';
import { DragDropContext, Droppable, Draggable, type DropResult } from '@hello-pangea/dnd';
import { MoreVertical, Trash2, Star, Navigation, MapPin, Clock, Route, Target } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { ScrollArea } from '~/components/ui/scroll-area';
import { Separator } from '~/components/ui/separator';
import { UnifiedPlaceCard, type PlaceCardAction } from './UnifiedPlaceCard';
import { cn } from '~/lib/utils';
import type { VisitedPlace } from '~/types/wanderlust';
import { useTranslation } from 'react-i18next';

export type PlaceManagementMode = 'waypoints' | 'favorites' | 'search-results';

export interface PlaceAction {
  type: 'add' | 'remove' | 'reorder' | 'select' | 'favorite' | 'navigate' | 'clear' | 'optimize';
  payload?: any;
}

export interface PlaceManagementPanelProps {
  mode: PlaceManagementMode;
  places: VisitedPlace[];
  enableReordering?: boolean;
  enableGrouping?: boolean;
  enableBulkActions?: boolean;
  maxHeight?: string;
  title?: string;
  emptyMessage?: string;
  onPlaceAction: (action: PlaceAction, place: VisitedPlace, index?: number) => void;
  onBulkAction?: (action: PlaceAction, places: VisitedPlace[]) => void;
  className?: string;
}

// Group places by category
const groupPlacesByCategory = (places: VisitedPlace[]) => {
  const groups = places.reduce((acc, place) => {
    const category = place.category || 'other';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(place);
    return acc;
  }, {} as Record<string, VisitedPlace[]>);

  return Object.entries(groups).map(([category, places]) => ({
    category,
    places,
    count: places.length,
  }));
};

export function PlaceManagementPanel({
  mode,
  places,
  enableReordering = false,
  enableGrouping = false,
  enableBulkActions = false,
  maxHeight = 'h-96',
  title,
  emptyMessage,
  onPlaceAction,
  onBulkAction,
  className,
}: PlaceManagementPanelProps) {
  const [selectedPlaces, setSelectedPlaces] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  const { t } = useTranslation('placeManagement'); // Use the translation hook

  // Mode-specific configuration
  const modeConfig = useMemo(() => {
    switch (mode) {
      case 'waypoints':
        return {
          title: title || t('placeManagement.waypoints.title'),
          emptyMessage: emptyMessage || t('placeManagement.waypoints.emptyMessage'),
          cardVariant: 'waypoint' as const,
          enableReordering: true,
          showIndex: true,
          showEstimatedTime: true,
          primaryAction: t('placeManagement.action.remove'),
          primaryIcon: Trash2,
          bulkActions: ['clear', 'optimize'],
          bulkActionLabels: {
            clear: t('placeManagement.bulkAction.clear'),
            optimize: t('placeManagement.bulkAction.optimize'),
          },
        };
      case 'favorites':
        return {
          title: title || t('placeManagement.favorites.title'),
          emptyMessage: emptyMessage || t('placeManagement.favorites.emptyMessage'),
          cardVariant: 'venue-detail' as const,
          enableReordering: false,
          showIndex: false,
          showEstimatedTime: false,
          primaryAction: t('placeManagement.action.remove'),
          primaryIcon: Star,
          bulkActions: ['remove', 'export'],
          bulkActionLabels: {
            remove: t('placeManagement.bulkAction.remove'),
            export: t('placeManagement.bulkAction.export'),
          },
        };
      case 'search-results':
        return {
          title: title || t('placeManagement.searchResults.title'),
          emptyMessage: emptyMessage || t('placeManagement.searchResults.emptyMessage'),
          cardVariant: 'search-result' as const,
          enableReordering: false,
          showIndex: false,
          showEstimatedTime: false,
          primaryAction: t('placeManagement.action.add'),
          primaryIcon: MapPin,
          bulkActions: ['add'],
          bulkActionLabels: {
            add: t('placeManagement.bulkAction.add'),
          },
        };
      default:
        return {
          title: title || t('placeManagement.general.title'),
          emptyMessage: emptyMessage || t('placeManagement.general.emptyMessage'),
          cardVariant: 'search-result' as const,
          enableReordering: false,
          showIndex: false,
          showEstimatedTime: false,
          primaryAction: t('placeManagement.action.select'),
          primaryIcon: MapPin,
          bulkActions: [],
          bulkActionLabels: {},
        };
    }
  }, [mode, title, emptyMessage, t]);

  // Handle drag and drop reordering
  const handleDragEnd = useCallback((result: DropResult) => {
    if (!result.destination || !enableReordering) return;

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    if (sourceIndex === destinationIndex) return;

    const place = places[sourceIndex];
    onPlaceAction(
      { type: 'reorder', payload: { from: sourceIndex, to: destinationIndex } },
      place,
      sourceIndex
    );
  }, [places, enableReordering, onPlaceAction]);

  // Handle place selection for bulk actions
  const handlePlaceSelection = useCallback((place: VisitedPlace, selected: boolean) => {
    setSelectedPlaces(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(place.id);
      } else {
        newSet.delete(place.id);
      }
      return newSet;
    });
  }, []);

  // Create place card actions based on mode
  const createPlaceActions = useCallback((place: VisitedPlace, index: number): PlaceCardAction[] => {
    const actions: PlaceCardAction[] = [];

    // Primary action
    actions.push({
      type: mode === 'search-results' ? 'add' : 'remove',
      label: modeConfig.primaryAction,
      icon: modeConfig.primaryIcon,
      onClick: (place) => onPlaceAction({ type: mode === 'search-results' ? 'add' : 'remove' }, place, index),
      variant: mode === 'search-results' ? 'default' : 'destructive',
    });

    // Mode-specific secondary actions
    if (mode === 'waypoints') {
      actions.push({
        type: 'navigate',
        label: 'Navigate',
        icon: Navigation,
        onClick: (place) => onPlaceAction({ type: 'navigate' }, place, index),
        variant: 'outline',
      });
    } else if (mode === 'favorites') {
      actions.push({
        type: 'navigate',
        label: 'Navigate',
        icon: Navigation,
        onClick: (place) => onPlaceAction({ type: 'navigate' }, place, index),
        variant: 'outline',
      });
    }

    return actions;
  }, [mode, modeConfig, onPlaceAction]);

  // Handle bulk actions
  const handleBulkAction = useCallback((actionType: string) => {
    const selectedPlacesList = places.filter(place => selectedPlaces.has(place.id));
    if (selectedPlacesList.length === 0) return;

    onBulkAction?.({ type: actionType as any }, selectedPlacesList);
    setSelectedPlaces(new Set());
    setIsSelectionMode(false);
  }, [places, selectedPlaces, onBulkAction]);

  // Render bulk action buttons
  const renderBulkActions = () => {
    if (!enableBulkActions || !isSelectionMode || selectedPlaces.size === 0) return null;

    return (
      <div className="flex items-center gap-2 p-3 bg-white/5 border-b border-white/10">
        <span className="text-sm text-white/70">
          {t('placeManagement.bulkAction.selectedCount', { count: selectedPlaces.size })} {/* Translate selected count */}
        </span>
        <div className="flex gap-2 ml-auto">
          {modeConfig.bulkActions.map((action) => (
            <Button
              key={action}
              size="sm"
              variant={action === 'clear' || action === 'remove' ? 'destructive' : 'outline'}
              onClick={() => handleBulkAction(action)}
              className="h-8 px-3 text-xs"
            >
              {action === 'clear' && <Trash2 className="h-3 w-3 mr-1" />}
              {action === 'remove' && <Trash2 className="h-3 w-3 mr-1" />}
              {action === 'optimize' && <Route className="h-3 w-3 mr-1" />}
              {action === 'add' && <MapPin className="h-3 w-3 mr-1" />}
              {modeConfig.bulkActionLabels[action as keyof typeof modeConfig.bulkActionLabels]} {/* Use translated bulk action label */}
            </Button>
          ))}
          <Button
            size="sm"
            variant="ghost"
            onClick={() => {
              setIsSelectionMode(false);
              setSelectedPlaces(new Set());
            }}
            className="h-8 px-3 text-xs text-white/70"
          >
            {t('placeManagement.bulkAction.cancel')} {/* Translate "Cancel" */}
          </Button>
        </div>
      </div>
    );
  };

  // Render header
  const renderHeader = () => (
    <div className="flex items-center justify-between p-4 border-b border-white/10">
      <div className="flex items-center gap-2">
        <h3 className="font-semibold text-white">{modeConfig.title}</h3>
        {places.length > 0 && (
          <Badge variant="outline" className="border-[#FFD700]/30 text-[#FFD700]">
            {places.length}
          </Badge>
        )}
      </div>

      {enableBulkActions && places.length > 0 && (
        <Button
          size="sm"
          variant="ghost"
          onClick={() => setIsSelectionMode(!isSelectionMode)}
          className="text-white/70 hover:text-white"
        >
          <MoreVertical className="h-4 w-4" />
        </Button>
      )}
    </div>
  );

  // Render empty state
  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="text-4xl mb-4 opacity-50">
        {mode === 'waypoints' && '🗺️'}
        {mode === 'favorites' && '⭐'}
        {mode === 'search-results' && '🔍'}
      </div>
      <p className="text-white/70 text-sm max-w-xs">{modeConfig.emptyMessage}</p>
    </div>
  );

  // Render place list
  const renderPlaceList = () => {
    if (places.length === 0) {
      return renderEmptyState();
    }

    const content = (
      <div className="space-y-2 p-4">
        {places.map((place, index) => (
          <UnifiedPlaceCard
            key={place.id}
            place={place}
            variant={modeConfig.cardVariant}
            actions={createPlaceActions(place, index)}
            index={modeConfig.showIndex ? index : undefined}
            estimatedTime={modeConfig.showEstimatedTime ? `${(index + 1) * 5} min` : undefined}
            isSelected={selectedPlaces.has(place.id)}
            onSelect={isSelectionMode ?
              (place) => handlePlaceSelection(place, !selectedPlaces.has(place.id)) :
              (place) => onPlaceAction({ type: 'select' }, place, index)
            }
            enableReordering={enableReordering && modeConfig.enableReordering}
            enableSwipeActions={true}
          />
        ))}
      </div>
    );

    if (enableReordering && modeConfig.enableReordering) {
      return (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="places-list">
            {(provided) => (
              <div ref={provided.innerRef} {...provided.droppableProps}>
                {places.map((place, index) => (
                  <Draggable key={place.id} draggableId={place.id} index={index}>
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        className={cn(
                          "mb-2 mx-4",
                          snapshot.isDragging && "opacity-75 rotate-2"
                        )}
                      >
                        <UnifiedPlaceCard
                          place={place}
                          variant={modeConfig.cardVariant}
                          actions={createPlaceActions(place, index)}
                          index={modeConfig.showIndex ? index : undefined}
                          estimatedTime={modeConfig.showEstimatedTime ? `${(index + 1) * 5} min` : undefined}
                          enableReordering={true}
                          onSelect={(place) => onPlaceAction({ type: 'select' }, place, index)}
                        />
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      );
    }

    return content;
  };

  return (
    <div className={cn("bg-white/5 backdrop-blur-sm rounded-lg border border-white/10", className)}>
      {renderHeader()}
      {renderBulkActions()}

      <ScrollArea className={maxHeight}>
        {renderPlaceList()}
      </ScrollArea>
    </div>
  );
}
