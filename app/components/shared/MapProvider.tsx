import React, { createContext, useContext, useState } from 'react';
import type { MapContextState } from '~/types/maps';
import type { VisitedPlace } from '~/types/wanderlust';
import type { RouteWaypoint } from '~/types/route-planning';

interface MapContextValue extends MapContextState {
  waypoints: RouteWaypoint[];
  setWaypoints: (waypoints: RouteWaypoint[]) => void;
  addWaypoint: (waypoint: RouteWaypoint) => void;
  removeWaypoint: (id: string) => void;
  reorderWaypoints: (sourceIndex: number, targetIndex: number) => void;
  places: VisitedPlace[];
  setPlaces: (places: VisitedPlace[]) => void;
}

const MapContext = createContext<MapContextValue | null>(null);

export function MapProvider({ children }: { children: React.ReactNode }) {
const [state, setState] = useState<MapContextState>({
  center: { lat: 0, lng: 0 },
  zoom: 12,
  mapInstance: null,
  isLoading: true,
  error: null
});

const [places, setPlaces] = useState<VisitedPlace[]>([]);

  const [waypoints, setWaypoints] = useState<RouteWaypoint[]>([]);

  const addWaypoint = (waypoint: RouteWaypoint) => {
    setWaypoints(prev => [...prev, waypoint]);
  };

  const removeWaypoint = (id: string) => {
    setWaypoints(prev => prev.filter(wp => wp.id !== id));
  };

  const reorderWaypoints = (sourceIndex: number, targetIndex: number) => {
    setWaypoints(prev => {
      const newWaypoints = [...prev];
      const [movedWaypoint] = newWaypoints.splice(sourceIndex, 1);
      newWaypoints.splice(targetIndex, 0, movedWaypoint);
      return newWaypoints;
    });
  };

return (
  <MapContext.Provider
    value={{
      ...state,
      waypoints,
      setWaypoints,
      addWaypoint,
      removeWaypoint,
      reorderWaypoints,
      places,
      setPlaces
    }}
  >
    {children}
  </MapContext.Provider>
);
}

export function useMapContext() {
  const context = useContext(MapContext);
  if (!context) {
    throw new Error('useMapContext must be used within a MapProvider');
  }
  return context;
}
