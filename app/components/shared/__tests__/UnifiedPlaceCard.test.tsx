import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import { UnifiedPlaceCard } from '../UnifiedPlaceCard';
import type { PlaceCardAction } from '../UnifiedPlaceCard'; // Type-only import
import type { VisitedPlace } from '~/types/wanderlust';
import { describe, it, expect, vi } from 'vitest'; // Explicitly import Vitest globals

expect.extend(toHaveNoViolations);

const mockPlace: VisitedPlace = {
  id: 'test-place-1',
  name: 'Test Venue',
  category: 'venue',
  city: 'Test City',
  region: 'Test Region', // Added region property
  description: { en: 'A test venue description', fr: 'Description de lieu de test', ar: 'وصف مكان الاختبار' }, // Added fr and ar
  coordinates: { latitude: 123, longitude: 456 }, // Changed to latitude/longitude
  rating: 4.5,
};

const mockActions: PlaceCardAction[] = [
  {
    type: 'add',
    label: 'Add',
    icon: () => <svg data-testid="plus-icon" />, // Mock icon component
    onClick: vi.fn(),
  },
];

describe('UnifiedPlaceCard Accessibility', () => {
  it('should not have any accessibility violations in search-result variant', async () => {
    const { container } = render(
      <UnifiedPlaceCard
        place={mockPlace}
        variant="search-result"
        actions={mockActions}
      />
    );

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  // Add more test cases for other variants and states as needed
});