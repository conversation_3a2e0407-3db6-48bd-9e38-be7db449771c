import React, { useEffect, lazy, Suspense } from 'react';
import { useLoaderData } from 'react-router';
import type { LoaderFunctionArgs } from 'react-router';
import { MapProvider } from '~/components/maps/MapProvider';
import { BaseMapComponent } from '~/components/maps/BaseMapComponent';
import type { CityRegion } from '~/types/wanderlust';
import { loadData } from '~/lib/map-explorer-loader';
import SkeletonLoader from '~/components/SkeletonLoader';
import { useWanderlustStore } from '~/stores/wanderlust';

const VenueExplorerMap = lazy(() => import('~/components/venue-discovery/VenueExplorerMap').then(module => ({ default: module.VenueExplorerMap })));
const VenueDiscoveryPanel = lazy(() => import('~/components/venue-discovery/VenueDiscoveryPanel').then(module => ({ default: module.VenueDiscoveryPanel })));
const WanderlustHeader = lazy(() => import('~/components/wanderlust/WanderlustHeader').then(module => ({ default: module.WanderlustHeader })));
const CityHub = lazy(() => import('~/components/wanderlust/CityHub').then(module => ({ default: module.CityHub })));
const ExplorerLog = lazy(() => import('~/components/wanderlust/ExplorerLog').then(module => ({ default: module.ExplorerLog })));
const SmartRouteCard = lazy(() => import('~/components/route-planner/SmartRouteCard').then(module => ({ default: module.SmartRouteCard })));
const RouteMap = lazy(() => import('~/components/route-planner/RouteMap').then(module => ({ default: module.RouteMap })));
const TurnByTurnDirections = lazy(() => import('~/components/route-planner/TurnByTurnDirections').then(module => ({ default: module.TurnByTurnDirections })));

interface MapExplorerProps {
  mode: 'venue-discovery' | 'travel-history' | 'route-planning';
  regions: any[]; // Add regions prop
}

export function MapExplorerComponent({ mode, regions }: MapExplorerProps) {
  // Remove useLoaderData as data is now passed as a prop
  // const { regions, success, error } = useLoaderData<typeof loadData>();

  const {
    loadRegionData,
    currentRegion,
    isLoading,
    error: storeError,
    setError,
    setLoading,
  } = useWanderlustStore();

  // Load data into store on component mount
  useEffect(() => {
    // Check if regions prop is available and not empty
    if (regions && regions.length > 0) {
      setLoading(true);
      try {
        loadRegionData(regions);
        setError(null);
      } catch (err) {
        setError('Failed to initialize travel data');
        console.error('Error loading region data:', err);
      } finally {
        setLoading(false);
      }
    } else {
      // Handle case where regions prop is empty or null
      setError('No region data available');
    }
  }, [regions, loadRegionData, setError, setLoading]); // Depend on regions prop

  return (
    <MapProvider>
      <BaseMapComponent>
        {/* Map Content based on mode */}
        {mode === 'venue-discovery' && (
          <Suspense fallback={<SkeletonLoader />}>
            <VenueExplorerMap />
            <VenueDiscoveryPanel />
          </Suspense>
        )}
        {mode === 'travel-history' && (
          <Suspense fallback={<SkeletonLoader />}>
            <WanderlustHeader />
            <CityHub regions={regions} />
            <ExplorerLog />
          </Suspense>
        )}
        {mode === 'route-planning' && (
          <Suspense fallback={<SkeletonLoader />}>
            <SmartRouteCard regions={regions} />
            <RouteMap />
            <TurnByTurnDirections />
          </Suspense>
        )}
      </BaseMapComponent>
    </MapProvider>
  );
}