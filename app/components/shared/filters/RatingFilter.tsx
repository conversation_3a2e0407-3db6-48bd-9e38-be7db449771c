"use client";

import * as React from "react";
import { Label } from "~/components/ui/label";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";

interface RatingFilterProps {
  onChange?: (rating: number) => void;
  onReset?: () => void;
  className?: string;
  defaultRating?: number;
  maxRating?: number;
}

export function RatingFilter({
  onChange,
  onReset,
  className,
  defaultRating = 0,
  maxRating = 5
}: RatingFilterProps) {
  const [rating, setRating] = React.useState<number>(defaultRating);
  const [hoverRating, setHoverRating] = React.useState<number>(0);

  const handleRatingChange = (newRating: number) => {
    // Toggle off if clicking the same rating
    const updatedRating = newRating === rating ? 0 : newRating;
    setRating(updatedRating);
    if (onChange) {
      onChange(updatedRating);
    }
  };

  const handleReset = () => {
    setRating(0);
    if (onReset) {
      onReset();
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Minimum Rating</Label>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleReset}
          className="h-8 px-2 text-xs"
        >
          Reset
        </Button>
      </div>
      
      <div className="flex items-center space-x-1">
        {Array.from({ length: maxRating }).map((_, index) => {
          const starValue = index + 1;
          const isFilled = starValue <= (hoverRating || rating);
          
          return (
            <button
              key={index}
              type="button"
              className="focus:outline-none"
              onClick={() => handleRatingChange(starValue)}
              onMouseEnter={() => setHoverRating(starValue)}
              onMouseLeave={() => setHoverRating(0)}
            >
              <StarIcon 
                className={cn(
                  "h-6 w-6",
                  isFilled ? "text-yellow-500 fill-yellow-500" : "text-gray-300 fill-gray-300"
                )}
              />
            </button>
          );
        })}
        
        {rating > 0 && (
          <span className="ml-2 text-sm text-muted-foreground">
            {rating} star{rating !== 1 ? "s" : ""} & up
          </span>
        )}
      </div>
    </div>
  );
}

function StarIcon({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
    </svg>
  );
}