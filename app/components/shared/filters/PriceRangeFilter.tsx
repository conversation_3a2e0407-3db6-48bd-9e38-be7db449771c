"use client";

import * as React from "react";
import { Slider } from "~/components/ui/slider";
import { Label } from "~/components/ui/label";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";

interface PriceRangeFilterProps {
  minPrice?: number;
  maxPrice?: number;
  defaultMinPrice?: number;
  defaultMaxPrice?: number;
  step?: number;
  onChange?: (values: [number, number]) => void;
  onReset?: () => void;
  className?: string;
  currency?: string;
}

export function PriceRangeFilter({
  minPrice = 0,
  maxPrice = 1000,
  defaultMinPrice = minPrice,
  defaultMaxPrice = maxPrice,
  step = 10,
  onChange,
  onReset,
  className,
  currency = "$"
}: PriceRangeFilterProps) {
  const [values, setValues] = React.useState<[number, number]>([
    defaultMinPrice,
    defaultMaxPrice
  ]);

  const handleValueChange = (newValues: number[]) => {
    const typedValues = newValues as [number, number];
    setValues(typedValues);
    if (onChange) {
      onChange(typedValues);
    }
  };

  const handleReset = () => {
    setValues([minPrice, maxPrice]);
    if (onReset) {
      onReset();
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Price Range</Label>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleReset}
          className="h-8 px-2 text-xs"
        >
          Reset
        </Button>
      </div>
      
      <Slider
        defaultValue={[values[0], values[1]]}
        min={minPrice}
        max={maxPrice}
        step={step}
        value={[values[0], values[1]]}
        onValueChange={handleValueChange}
        className="py-4"
      />
      
      <div className="flex items-center justify-between text-sm">
        <div className="rounded-md border border-input px-2 py-1">
          <span className="text-muted-foreground">{currency}</span>
          <span>{values[0]}</span>
        </div>
        <div className="rounded-md border border-input px-2 py-1">
          <span className="text-muted-foreground">{currency}</span>
          <span>{values[1]}</span>
        </div>
      </div>
    </div>
  );
}