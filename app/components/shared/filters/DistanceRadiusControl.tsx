"use client";

import * as React from "react";
import { Slider } from "~/components/ui/slider";
import { Label } from "~/components/ui/label";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";

interface DistanceRadiusControlProps {
  minRadius?: number;
  maxRadius?: number;
  defaultRadius?: number;
  step?: number;
  onChange?: (radius: number) => void;
  onReset?: () => void;
  className?: string;
  unit?: string;
}

export function DistanceRadiusControl({
  minRadius = 1,
  maxRadius = 50,
  defaultRadius = 10,
  step = 1,
  onChange,
  onReset,
  className,
  unit = "km"
}: DistanceRadiusControlProps) {
  const [radius, setRadius] = React.useState<number>(defaultRadius);

  const handleValueChange = (newValues: number[]) => {
    const newRadius = newValues[0];
    setRadius(newRadius);
    if (onChange) {
      onChange(newRadius);
    }
  };

  const handleReset = () => {
    setRadius(defaultRadius);
    if (onReset) {
      onReset();
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Distance Radius</Label>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleReset}
          className="h-8 px-2 text-xs"
        >
          Reset
        </Button>
      </div>
      
      <Slider
        defaultValue={[radius]}
        min={minRadius}
        max={maxRadius}
        step={step}
        value={[radius]}
        onValueChange={handleValueChange}
        className="py-4"
      />
      
      <div className="flex items-center justify-between text-sm">
        <div className="rounded-md border border-input px-2 py-1">
          <span>{radius}</span>
          <span className="ml-1 text-muted-foreground">{unit}</span>
        </div>
        <div className="text-sm text-muted-foreground">
          {radius === maxRadius ? "Max" : radius < maxRadius / 3 ? "Nearby" : "Medium"} distance
        </div>
      </div>
    </div>
  );
}