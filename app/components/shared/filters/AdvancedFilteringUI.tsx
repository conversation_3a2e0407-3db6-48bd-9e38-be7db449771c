"use client";

import * as React from "react";
import { Button } from "~/components/ui/button";
import { PriceRangeFilter } from "./PriceRangeFilter";
import { RatingFilter } from "./RatingFilter";
import { DistanceRadiusControl } from "./DistanceRadiusControl";
import { cn } from "~/lib/utils";
import { ChevronDown, ChevronUp, X } from "lucide-react";

interface AdvancedFilteringUIProps {
  priceRange?: [number, number];
  minRating?: number;
  distanceRadius?: number;
  onPriceRangeChange?: (values: [number, number]) => void;
  onRatingChange?: (rating: number) => void;
  onDistanceRadiusChange?: (radius: number) => void;
  onResetAllFilters?: () => void;
  className?: string;
  defaultExpanded?: boolean;
}

export function AdvancedFilteringUI({
  priceRange = [0, 1000],
  minRating = 0,
  distanceRadius = 10,
  onPriceRangeChange,
  onRatingChange,
  onDistanceRadiusChange,
  onResetAllFilters,
  className,
  defaultExpanded = false
}: AdvancedFilteringUIProps) {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);
  
  const handleResetAll = () => {
    if (onResetAllFilters) {
      onResetAllFilters();
    }
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const hasActiveFilters = (
    priceRange[0] > 0 || 
    priceRange[1] < 1000 || 
    minRating > 0 || 
    distanceRadius !== 10
  );

  return (
    <div className={cn("rounded-lg border border-border p-4", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-sm font-medium">Advanced Filters</h3>
          {hasActiveFilters && (
            <span className="rounded-full bg-primary px-2 py-0.5 text-xs text-primary-foreground">
              Active
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {hasActiveFilters && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleResetAll}
              className="h-8 px-2 text-xs"
            >
              <X className="mr-1 h-3 w-3" />
              Clear All
            </Button>
          )}
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={toggleExpanded}
            className="h-8 w-8 p-0"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {isExpanded && (
        <div className="mt-4 space-y-6">
          <PriceRangeFilter 
            defaultMinPrice={priceRange[0]}
            defaultMaxPrice={priceRange[1]}
            onChange={onPriceRangeChange}
            onReset={() => onPriceRangeChange?.([0, 1000])}
          />
          
          <RatingFilter 
            defaultRating={minRating}
            onChange={onRatingChange}
            onReset={() => onRatingChange?.(0)}
          />
          
          <DistanceRadiusControl 
            defaultRadius={distanceRadius}
            onChange={onDistanceRadiusChange}
            onReset={() => onDistanceRadiusChange?.(10)}
          />
        </div>
      )}
    </div>
  );
}