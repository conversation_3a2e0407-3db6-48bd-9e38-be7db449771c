/**
 * useGooglePlaces Hook for Task 12: Search and Autocomplete
 *
 * Provides Google Places API integration with search functionality,
 * autocomplete suggestions, and performance monitoring.
 */

import { useCallback, useEffect, useState, useRef } from 'react';
import { showError, showWarning } from '~/components/wanderlust/NotificationSystem';

export interface PlaceSearchResult {
  place_id: string;
  name: string;
  formatted_address: string;
  geometry: {
    location: google.maps.LatLng;
    viewport: google.maps.LatLngBounds;
  };
  types: string[];
  rating?: number;
  price_level?: number;
  photos?: google.maps.places.PlacePhoto[];
}

export interface AutocompleteResult {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
  types: string[];
}

export function useGooglePlaces(map?: google.maps.Map) {
  const [autocompleteService, setAutocompleteService] = useState<google.maps.places.AutocompleteService | null>(null);
  const [placesService, setPlacesService] = useState<google.maps.places.PlacesService | null>(null);
  const [searchResults, setSearchResults] = useState<PlaceSearchResult[]>([]);
  const [autocompleteResults, setAutocompleteResults] = useState<AutocompleteResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);

  // Debounce timer for autocomplete
  const debounceTimerRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // Request cache to prevent duplicate API calls
  const cacheRef = useRef<Map<string, PlaceSearchResult[]>>(new Map());

  // Initialize services when Google Maps is loaded
  useEffect(() => {
    if (window.google?.maps) {
      try {
        setAutocompleteService(new google.maps.places.AutocompleteService());
        if (map) {
          setPlacesService(new google.maps.places.PlacesService(map));
        }
      } catch (error) {
        console.error('Error initializing Google Places services:', error);
        showError('Places Service Error', 'Unable to initialize search functionality');
      }
    }
  }, [map]);

  // Load search history from localStorage
  useEffect(() => {
    try {
      const saved = localStorage.getItem('wanderlust-search-history');
      if (saved) {
        setSearchHistory(JSON.parse(saved));
      }
    } catch (error) {
      console.warn('Failed to load search history:', error);
    }
  }, []);

  // Save search history to localStorage
  const saveSearchHistory = useCallback((query: string) => {
    try {
      const updated = [query, ...searchHistory.filter(h => h !== query)].slice(0, 10);
      const sanitizedQuery = query.replace(/\d{4,}/g, '****'); // Replace sequences of 4 or more digits with asterisks
      setSearchHistory(updated);
      localStorage.setItem('wanderlust-search-history', JSON.stringify(updated));
    } catch (error) {
      console.warn('Failed to save search history:', error);
    }
  }, [searchHistory]);

  // Debounced autocomplete search
  const getAutocompleteSuggestions = useCallback(async (query: string): Promise<AutocompleteResult[]> => {
    if (!autocompleteService || !query.trim()) {
      return [];
    }

    return new Promise((resolve) => {
      // Clear previous debounce timer
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      // Debounce the request
      debounceTimerRef.current = setTimeout(() => {
        autocompleteService.getPlacePredictions(
          {
            input: query,
            types: ['establishment', 'geocode'],
            componentRestrictions: { country: 'us' }, // Adjust as needed
          },
          (predictions, status) => {
            if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
              const results: AutocompleteResult[] = predictions.map(prediction => ({
                place_id: prediction.place_id,
                description: prediction.description,
                structured_formatting: prediction.structured_formatting,
                types: prediction.types,
              }));

              setAutocompleteResults(results);
              resolve(results);
            } else {
              setAutocompleteResults([]);
              resolve([]);

              if (status === google.maps.places.PlacesServiceStatus.OVER_QUERY_LIMIT) {
                showWarning('Search Limit', 'Search quota exceeded. Please try again later.');
              }
            }
          }
        );
      }, 300); // 300ms debounce
    });
  }, [autocompleteService]);

  // Search places by query
  const searchPlaces = useCallback(async (query: string): Promise<PlaceSearchResult[]> => {
    if (!placesService || !query.trim()) {
      return [];
    }

    // Check cache first
    const cacheKey = query.toLowerCase().trim();
    if (cacheRef.current.has(cacheKey)) {
      const cached = cacheRef.current.get(cacheKey)!;
      setSearchResults(cached);
      return cached;
    }

    setIsSearching(true);

    return new Promise((resolve) => {
      const request: google.maps.places.TextSearchRequest = {
        query,
        type: 'point_of_interest',
      };

      placesService.textSearch(request, (results, status) => {
        setIsSearching(false);

        if (status === google.maps.places.PlacesServiceStatus.OK && results) {
          const searchResults: PlaceSearchResult[] = results
            .filter(result => result.geometry?.location && result.place_id)
            .map(result => ({
              place_id: result.place_id!,
              name: result.name || 'Unknown Place',
              formatted_address: result.formatted_address || '',
              geometry: {
                location: result.geometry!.location!,
                viewport: result.geometry!.viewport!,
              },
              types: result.types || [],
              rating: result.rating,
              price_level: result.price_level,
              photos: result.photos,
            }));

          // Cache the results
          cacheRef.current.set(cacheKey, searchResults);

          // Limit cache size
          if (cacheRef.current.size > 50) {
            const firstKey = cacheRef.current.keys().next().value;
            if (firstKey) {
              cacheRef.current.delete(firstKey);
            }
          }

          setSearchResults(searchResults);
          saveSearchHistory(query);
          resolve(searchResults);
        } else {
          setSearchResults([]);
          resolve([]);

          if (status === google.maps.places.PlacesServiceStatus.ZERO_RESULTS) {
            showWarning('No Results', `No places found for "${query}"`);
          } else if (status === google.maps.places.PlacesServiceStatus.OVER_QUERY_LIMIT) {
            showError('Search Limit Exceeded', 'Please try again later or refine your search');
          } else {
            showError('Search Failed', 'Unable to search places. Please try again.');
          }
        }
      });
    });
  }, [placesService, saveSearchHistory]);

  // Get place details by place_id
  const getPlaceDetails = useCallback(async (placeId: string): Promise<google.maps.places.PlaceResult | null> => {
    if (!placesService || !placeId) {
      return null;
    }

    return new Promise((resolve) => {
      const request: google.maps.places.PlaceDetailsRequest = {
        placeId,
        fields: [
          'place_id',
          'name',
          'formatted_address',
          'geometry',
          'rating',
          'price_level',
          'photos',
          'reviews',
          'website',
          'formatted_phone_number',
          'opening_hours',
          'types'
        ],
      };

      placesService.getDetails(request, (place, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && place) {
          resolve(place);
        } else {
          resolve(null);

          if (status === google.maps.places.PlacesServiceStatus.NOT_FOUND) {
            showWarning('Place Not Found', 'The selected place could not be found');
          } else {
            showError('Details Failed', 'Unable to load place details');
          }
        }
      });
    });
  }, [placesService]);

  // Clear search results
  const clearSearchResults = useCallback(() => {
    setSearchResults([]);
    setAutocompleteResults([]);
  }, []);

  // Clear search history
  const clearSearchHistory = useCallback(() => {
    setSearchHistory([]);
    try {
      localStorage.removeItem('wanderlust-search-history');
    } catch (error) {
      console.warn('Failed to clear search history:', error);
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return {
    // Services
    autocompleteService,
    placesService,

    // State
    searchResults,
    autocompleteResults,
    isSearching,
    searchHistory,

    // Methods
    searchPlaces,
    getAutocompleteSuggestions,
    getPlaceDetails,
    clearSearchResults,
    clearSearchHistory,

    // Utilities
    isReady: !!autocompleteService && !!placesService,
  };
}
