/**
 * useGestures Hook for WL-004 Mobile Navigation Patterns
 *
 * Provides reusable gesture detection logic with performance optimization,
 * following the established mobile-first architecture and touch accessibility standards.
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import type { GestureState, GestureConfig } from '~/types/wanderlust';
import {
  normalizeTouchEvent,
  calculateSwipeGesture,
  getEventNames,
  throttle,
  prefersReducedMotion,
  isTouchDevice,
} from '~/lib/gesture-utils';

interface UseGesturesOptions {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  onLongPress?: () => void;
  onDragStart?: (event: { x: number; y: number }) => void;
  onDragMove?: (event: { deltaX: number; deltaY: number; x: number; y: number }) => void;
  onDragEnd?: (event: { deltaX: number; deltaY: number; velocity: number }) => void;
  config?: Partial<GestureConfig>;
  disabled?: boolean;
}

const defaultConfig: GestureConfig = {
  swipeThreshold: 44, // Minimum 44px for touch accessibility
  longPressDelay: 500, // 500ms for long press
  velocityThreshold: 0.5, // Minimum velocity for swipe detection
  snapAnimationDuration: 300, // 300ms following established animation standards
};

export function useGestures(options: UseGesturesOptions = {}) {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onLongPress,
    onDragStart,
    onDragMove,
    onDragEnd,
    config = {},
    disabled = false,
  } = options;

  const mergedConfig = { ...defaultConfig, ...config };
  const [gestureState, setGestureState] = useState<GestureState>({
    isActive: false,
    isDragging: false,
    isLongPressing: false,
    lastTapTime: 0,
    startPoint: null,
    currentPoint: null,
    startTime: null,
  });

  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null);
  const elementRef = useRef<HTMLElement | null>(null);
  const isDraggingRef = useRef(false);
  const hasMovedRef = useRef(false);

  // Throttled drag move handler for performance
  const throttledDragMove = useCallback(
    throttle((event: { deltaX: number; deltaY: number; x: number; y: number }) => {
      onDragMove?.(event);
    }, 16), // ~60fps
    [onDragMove]
  );

  const clearLongPressTimer = useCallback(() => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current);
      longPressTimerRef.current = null;
    }
  }, []);

  const handleStart = useCallback(
    (event: Event) => {
      if (disabled || prefersReducedMotion()) return;

      const touchEvent = event as TouchEvent | MouseEvent;

      // Prevent default to avoid scrolling during gestures
      if ('touches' in touchEvent && touchEvent.touches.length === 1) {
        touchEvent.preventDefault();
      }

      const point = normalizeTouchEvent(touchEvent);
      const startTime = Date.now();

      setGestureState({
        isActive: true,
        isDragging: false,
        isLongPressing: false,
        lastTapTime: 0,
        startPoint: { x: point.x, y: point.y },
        currentPoint: { x: point.x, y: point.y },
        startTime,
      });

      isDraggingRef.current = false;
      hasMovedRef.current = false;

      // Start long press timer
      if (onLongPress) {
        longPressTimerRef.current = setTimeout(() => {
          if (!hasMovedRef.current) {
            onLongPress();
          }
        }, mergedConfig.longPressDelay);
      }

      // Call drag start
      onDragStart?.({ x: point.x, y: point.y });
    },
    [disabled, onLongPress, onDragStart, mergedConfig.longPressDelay]
  );

  const handleMove = useCallback(
    (event: Event) => {
      if (disabled || !gestureState.isActive || !gestureState.startPoint) return;

      const touchEvent = event as TouchEvent | MouseEvent;
      const point = normalizeTouchEvent(touchEvent);
      const deltaX = point.x - gestureState.startPoint.x;
      const deltaY = point.y - gestureState.startPoint.y;
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

      // Mark as moved if we've exceeded the threshold
      if (distance > 10) {
        hasMovedRef.current = true;
        clearLongPressTimer();
      }

      // Start dragging if we've moved enough
      if (!isDraggingRef.current && distance > mergedConfig.swipeThreshold / 2) {
        isDraggingRef.current = true;
      }

      setGestureState(prev => ({
        ...prev,
        currentPoint: { x: point.x, y: point.y },
      }));

      // Call drag move if dragging
      if (isDraggingRef.current) {
        throttledDragMove({
          deltaX,
          deltaY,
          x: point.x,
          y: point.y,
        });
      }
    },
    [disabled, gestureState.isActive, gestureState.startPoint, mergedConfig.swipeThreshold, clearLongPressTimer, throttledDragMove]
  );

  const handleEnd = useCallback(
    (event: Event) => {
      if (disabled || !gestureState.isActive || !gestureState.startPoint || !gestureState.startTime) return;

      clearLongPressTimer();

      const touchEvent = event as TouchEvent | MouseEvent;
      const point = normalizeTouchEvent(touchEvent);
      const endTime = Date.now();

      const startPoint = {
        x: gestureState.startPoint.x,
        y: gestureState.startPoint.y,
        timestamp: gestureState.startTime,
      };

      const endPoint = {
        x: point.x,
        y: point.y,
        timestamp: endTime,
      };

      // Calculate swipe gesture
      const swipeGesture = calculateSwipeGesture(startPoint, endPoint);

      if (swipeGesture && swipeGesture.velocity >= mergedConfig.velocityThreshold) {
        // Handle swipe gestures
        switch (swipeGesture.direction) {
          case 'left':
            onSwipeLeft?.();
            break;
          case 'right':
            onSwipeRight?.();
            break;
          case 'up':
            onSwipeUp?.();
            break;
          case 'down':
            onSwipeDown?.();
            break;
        }
      }

      // Handle drag end
      if (isDraggingRef.current) {
        const deltaX = point.x - gestureState.startPoint.x;
        const deltaY = point.y - gestureState.startPoint.y;
        const duration = endTime - gestureState.startTime;
        const velocity = duration > 0 ? Math.sqrt(deltaX * deltaX + deltaY * deltaY) / duration : 0;

        onDragEnd?.({ deltaX, deltaY, velocity });
      }

      // Reset state
      setGestureState({
        isActive: false,
        isDragging: false,
        isLongPressing: false,
        lastTapTime: gestureState.lastTapTime ?? 0,
        startPoint: null,
        currentPoint: null,
        startTime: null,
      });

      isDraggingRef.current = false;
      hasMovedRef.current = false;
    },
    [
      disabled,
      gestureState.isActive,
      gestureState.startPoint,
      gestureState.startTime,
      clearLongPressTimer,
      mergedConfig.velocityThreshold,
      onSwipeLeft,
      onSwipeRight,
      onSwipeUp,
      onSwipeDown,
      onDragEnd,
    ]
  );

  const handleCancel = useCallback(() => {
    clearLongPressTimer();
    setGestureState({
      isActive: false,
      isDragging: false,
      isLongPressing: false,
      lastTapTime: gestureState.lastTapTime ?? 0,
      startPoint: null,
      currentPoint: null,
      startTime: null,
    });
    isDraggingRef.current = false;
    hasMovedRef.current = false;
  }, [clearLongPressTimer]);

  // Set up event listeners
  useEffect(() => {
    const element = elementRef.current;
    if (!element || disabled) return;

    const eventNames = getEventNames();
    const isTouch = isTouchDevice();

    // Add event listeners with proper options
    const options = { passive: false };

    element.addEventListener(eventNames.start, handleStart, options);
    document.addEventListener(eventNames.move, handleMove, options);
    document.addEventListener(eventNames.end, handleEnd, options);

    if (isTouch) {
      element.addEventListener(eventNames.cancel, handleCancel, options);
    }

    // Cleanup function
    return () => {
      element.removeEventListener(eventNames.start, handleStart);
      document.removeEventListener(eventNames.move, handleMove);
      document.removeEventListener(eventNames.end, handleEnd);

      if (isTouch) {
        element.removeEventListener(eventNames.cancel, handleCancel);
      }

      clearLongPressTimer();
    };
  }, [disabled, handleStart, handleMove, handleEnd, handleCancel, clearLongPressTimer]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearLongPressTimer();
    };
  }, [clearLongPressTimer]);

  return {
    ref: elementRef,
    gestureState,
    isDragging: isDraggingRef.current,
    hasMoved: hasMovedRef.current,
  };
}
