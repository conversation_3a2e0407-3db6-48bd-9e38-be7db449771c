/**
 * Performance Monitoring Hook
 *
 * Provides comprehensive performance monitoring for React components and map operations.
 * Integrates with React DevTools Profiler and provides real-time metrics.
 *
 * Features:
 * - Component render time tracking
 * - Map operation performance monitoring
 * - Memory usage tracking
 * - Bundle size analysis
 * - Real-time performance dashboard
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import type { ProfilerOnRenderCallback } from 'react';

// Performance metrics types
export interface PerformanceMetrics {
  componentRenderTime: number;
  mapLoadTime: number;
  searchResponseTime: number;
  memoryUsage: number;
  bundleSize: number;
  coreWebVitals: CoreWebVitals;
}

export interface CoreWebVitals {
  fcp: number | null; // First Contentful Paint
  lcp: number | null; // Largest Contentful Paint
  fid: number | null; // First Input Delay
  cls: number | null; // Cumulative Layout Shift
  ttfb: number | null; // Time to First Byte
}

export interface ComponentPerformance {
  id: string;
  phase: 'mount' | 'update';
  actualDuration: number;
  baseDuration: number;
  startTime: number;
  commitTime: number;
  interactions: Set<any>;
}

export interface MapOperationMetrics {
  operation: string;
  duration: number;
  timestamp: number;
  success: boolean;
  error?: string;
}

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  componentRender: 16, // 60fps = 16.67ms per frame
  mapLoad: 2000, // 2 seconds
  searchResponse: 500, // 500ms
  memoryUsage: 50 * 1024 * 1024, // 50MB
  fcp: 1800, // 1.8 seconds
  lcp: 2500, // 2.5 seconds
  fid: 100, // 100ms
  cls: 0.1, // 0.1 score
};

// Global performance store
class PerformanceStore {
  private metrics: ComponentPerformance[] = [];
  private mapMetrics: MapOperationMetrics[] = [];
  private subscribers: Set<(metrics: PerformanceMetrics) => void> = new Set();

  addComponentMetric(metric: ComponentPerformance) {
    this.metrics.push(metric);
    // Keep only last 100 metrics
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }
    this.notifySubscribers();
  }

  addMapMetric(metric: MapOperationMetrics) {
    this.mapMetrics.push(metric);
    // Keep only last 50 map metrics
    if (this.mapMetrics.length > 50) {
      this.mapMetrics = this.mapMetrics.slice(-50);
    }
    this.notifySubscribers();
  }

  subscribe(callback: (metrics: PerformanceMetrics) => void) {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  private notifySubscribers() {
    const aggregatedMetrics = this.getAggregatedMetrics();
    this.subscribers.forEach(callback => callback(aggregatedMetrics));
  }

  private getAggregatedMetrics(): PerformanceMetrics {
    const recentComponents = this.metrics.slice(-10);
    const recentMapOps = this.mapMetrics.slice(-10);

    return {
      componentRenderTime: recentComponents.length > 0
        ? recentComponents.reduce((sum, m) => sum + m.actualDuration, 0) / recentComponents.length
        : 0,
      mapLoadTime: recentMapOps.find(m => m.operation === 'mapLoad')?.duration || 0,
      searchResponseTime: recentMapOps.find(m => m.operation === 'search')?.duration || 0,
      memoryUsage: this.getMemoryUsage(),
      bundleSize: this.getBundleSize(),
      coreWebVitals: this.getCoreWebVitals(),
    };
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory?.usedJSHeapSize || 0;
    }
    return 0;
  }

  private getBundleSize(): number {
    // Estimate bundle size based on loaded resources
    if (typeof window !== 'undefined' && 'performance' in window) {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      return resources
        .filter(r => r.name.includes('.js') || r.name.includes('.css'))
        .reduce((sum, r) => sum + (r.transferSize || 0), 0);
    }
    return 0;
  }

  private getCoreWebVitals(): CoreWebVitals {
    const vitals: CoreWebVitals = {
      fcp: null,
      lcp: null,
      fid: null,
      cls: null,
      ttfb: null,
    };

    if (typeof window !== 'undefined' && 'performance' in window) {
      // Get navigation timing for TTFB
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        vitals.ttfb = navigation.responseStart - navigation.requestStart;
      }

      // Get paint timing for FCP
      const paintEntries = performance.getEntriesByType('paint');
      const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        vitals.fcp = fcpEntry.startTime;
      }
    }

    return vitals;
  }

  getPerformanceReport() {
    return {
      componentMetrics: this.metrics,
      mapMetrics: this.mapMetrics,
      aggregated: this.getAggregatedMetrics(),
      thresholds: PERFORMANCE_THRESHOLDS,
    };
  }
}

const performanceStore = new PerformanceStore();

// Main performance monitoring hook
export function usePerformanceMonitoring(componentId?: string) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const operationTimers = useRef<Map<string, number>>(new Map());

  // Subscribe to performance updates
  useEffect(() => {
    const unsubscribe = performanceStore.subscribe(setMetrics);
    return () => {
      unsubscribe();
    };
  }, []);

  // Component profiler callback
  const onRender = useCallback((
    id: any,
    phase: any,
    actualDuration: any,
    baseDuration: any,
    startTime: any,
    commitTime: any,
    interactions: any
  ) => {
    performanceStore.addComponentMetric({
      id,
      phase,
      actualDuration,
      baseDuration,
      startTime,
      commitTime,
      interactions,
    });
  }, []);

  // Map operation timing
  const startMapOperation = useCallback((operation: string) => {
    operationTimers.current.set(operation, performance.now());
    setIsMonitoring(true);
  }, []);

  const endMapOperation = useCallback((operation: string, success: boolean = true, error?: string) => {
    const startTime = operationTimers.current.get(operation);
    if (startTime) {
      const duration = performance.now() - startTime;
      performanceStore.addMapMetric({
        operation,
        duration,
        timestamp: Date.now(),
        success,
        error,
      });
      operationTimers.current.delete(operation);
    }
    setIsMonitoring(false);
  }, []);

  // Performance measurement utilities
  const measureAsync = useCallback(async <T>(
    operation: string,
    asyncFn: () => Promise<T>
  ): Promise<T> => {
    startMapOperation(operation);
    try {
      const result = await asyncFn();
      endMapOperation(operation, true);
      return result;
    } catch (error) {
      endMapOperation(operation, false, error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }, [startMapOperation, endMapOperation]);

  const measureSync = useCallback(<T>(
    operation: string,
    syncFn: () => T
  ): T => {
    startMapOperation(operation);
    try {
      const result = syncFn();
      endMapOperation(operation, true);
      return result;
    } catch (error) {
      endMapOperation(operation, false, error instanceof Error ? error.message : 'Unknown error');
      throw error;
    }
  }, [startMapOperation, endMapOperation]);

  // Performance analysis
  const getPerformanceAnalysis = useCallback(() => {
    if (!metrics) return null;

    const analysis = {
      overall: 'good' as 'good' | 'needs-improvement' | 'poor',
      issues: [] as string[],
      recommendations: [] as string[],
    };

    // Analyze component render performance
    if (metrics.componentRenderTime > PERFORMANCE_THRESHOLDS.componentRender) {
      analysis.overall = 'needs-improvement';
      analysis.issues.push('Slow component rendering detected');
      analysis.recommendations.push('Consider memoizing expensive components');
    }

    // Analyze map load performance
    if (metrics.mapLoadTime > PERFORMANCE_THRESHOLDS.mapLoad) {
      analysis.overall = 'needs-improvement';
      analysis.issues.push('Slow map loading');
      analysis.recommendations.push('Implement map preloading or reduce initial features');
    }

    // Analyze search performance
    if (metrics.searchResponseTime > PERFORMANCE_THRESHOLDS.searchResponse) {
      analysis.overall = 'needs-improvement';
      analysis.issues.push('Slow search responses');
      analysis.recommendations.push('Implement search debouncing and result caching');
    }

    // Analyze memory usage
    if (metrics.memoryUsage > PERFORMANCE_THRESHOLDS.memoryUsage) {
      analysis.overall = 'poor';
      analysis.issues.push('High memory usage detected');
      analysis.recommendations.push('Check for memory leaks and optimize data structures');
    }

    // Analyze Core Web Vitals
    const { coreWebVitals } = metrics;
    if (coreWebVitals.fcp && coreWebVitals.fcp > PERFORMANCE_THRESHOLDS.fcp) {
      analysis.overall = 'needs-improvement';
      analysis.issues.push('Slow First Contentful Paint');
      analysis.recommendations.push('Optimize critical rendering path');
    }

    if (coreWebVitals.lcp && coreWebVitals.lcp > PERFORMANCE_THRESHOLDS.lcp) {
      analysis.overall = 'needs-improvement';
      analysis.issues.push('Slow Largest Contentful Paint');
      analysis.recommendations.push('Optimize largest content element loading');
    }

    return analysis;
  }, [metrics]);

  // Performance report generation
  const generateReport = useCallback(() => {
    return performanceStore.getPerformanceReport();
  }, []);

  return {
    metrics,
    isMonitoring,
    onRender,
    startMapOperation,
    endMapOperation,
    measureAsync,
    measureSync,
    getPerformanceAnalysis,
    generateReport,
  };
}

// Specialized hooks for different use cases
export function useMapPerformanceMonitoring() {
  const {
    measureAsync,
    measureSync,
    startMapOperation,
    endMapOperation,
    metrics,
  } = usePerformanceMonitoring('map');

  const measureMapLoad = useCallback((mapLoadFn: () => Promise<void>) => {
    return measureAsync('mapLoad', mapLoadFn);
  }, [measureAsync]);

  const measureSearch = useCallback((searchFn: () => Promise<any>) => {
    return measureAsync('search', searchFn);
  }, [measureAsync]);

  const measureRouteCalculation = useCallback((routeFn: () => Promise<any>) => {
    return measureAsync('routeCalculation', routeFn);
  }, [measureAsync]);

  return {
    measureMapLoad,
    measureSearch,
    measureRouteCalculation,
    startMapOperation,
    endMapOperation,
    mapMetrics: metrics,
  };
}

export function useComponentPerformanceMonitoring(componentName: string) {
  const { onRender, metrics } = usePerformanceMonitoring(componentName);

  return {
    componentMetrics: metrics,
    onRender,
    componentName,
  };
}

// Performance monitoring utilities
export const PerformanceUtils = {
  // Mark performance milestones
  mark: (name: string) => {
    if (typeof performance !== 'undefined' && performance.mark) {
      performance.mark(name);
    }
  },

  // Measure between marks
  measure: (name: string, startMark: string, endMark?: string) => {
    if (typeof performance !== 'undefined' && performance.measure) {
      performance.measure(name, startMark, endMark);
    }
  },

  // Get performance entries
  getEntries: (type?: string) => {
    if (typeof performance !== 'undefined') {
      return type ? performance.getEntriesByType(type) : performance.getEntries();
    }
    return [];
  },

  // Clear performance data
  clear: () => {
    if (typeof performance !== 'undefined' && performance.clearMarks) {
      performance.clearMarks();
      performance.clearMeasures();
    }
  },
};
