/**
 * Offline Status Hook
 * 
 * Provides comprehensive offline status information and service worker integration.
 * Features:
 * - Real-time online/offline detection
 * - Service worker cache statistics
 * - Background sync status monitoring
 * - Cache management operations
 * - Integration with OfflineIndicator component
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useOfflineSync } from './useOfflineSync';
import type { OfflineStatus } from '~/components/offline/OfflineIndicator';

interface ServiceWorkerCacheStats {
  version: string;
  caches: Record<string, { count: number; size: number }>;
  totalSize: number;
  lastCleanup: number;
}

interface ServiceWorkerMessage {
  type: string;
  payload?: any;
}

export function useOfflineStatus() {
  const { isOnline, stats: syncStats, forceSync, clearQueue } = useOfflineSync();
  const [cacheStats, setCacheStats] = useState<ServiceWorkerCacheStats | null>(null);
  const [isServiceWorkerReady, setIsServiceWorkerReady] = useState(false);
  const [lastCacheUpdate, setLastCacheUpdate] = useState<Date | null>(null);
  
  const serviceWorkerRef = useRef<ServiceWorker | null>(null);
  const messageChannelRef = useRef<MessageChannel | null>(null);

  // Initialize service worker communication
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.ready.then((registration) => {
        serviceWorkerRef.current = registration.active;
        setIsServiceWorkerReady(true);
        
        // Set up message channel for communication
        messageChannelRef.current = new MessageChannel();
        
        // Listen for messages from service worker
        messageChannelRef.current.port1.onmessage = (event) => {
          const { type, payload } = event.data as ServiceWorkerMessage;
          
          switch (type) {
            case 'CACHE_STATS':
              setCacheStats(payload);
              setLastCacheUpdate(new Date());
              break;
            case 'CACHE_UPDATED':
              // Refresh cache stats when cache is updated
              getCacheStats();
              break;
          }
        };
        
        // Initial cache stats fetch
        getCacheStats();
      }).catch((error) => {
        console.error('Service Worker not ready:', error);
      });
    }
  }, []);

  // Send message to service worker
  const sendMessageToServiceWorker = useCallback((message: ServiceWorkerMessage): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (!serviceWorkerRef.current || !messageChannelRef.current) {
        reject(new Error('Service Worker not ready'));
        return;
      }

      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        resolve(event.data);
      };

      serviceWorkerRef.current.postMessage(message, [messageChannel.port2]);
    });
  }, []);

  // Get cache statistics from service worker
  const getCacheStats = useCallback(async () => {
    if (!isServiceWorkerReady) return;

    try {
      const stats = await sendMessageToServiceWorker({ type: 'GET_CACHE_STATS' });
      setCacheStats(stats);
      setLastCacheUpdate(new Date());
    } catch (error) {
      console.error('Failed to get cache stats:', error);
    }
  }, [isServiceWorkerReady, sendMessageToServiceWorker]);

  // Clear specific cache
  const clearCache = useCallback(async (cacheName?: string) => {
    if (!isServiceWorkerReady) return;

    try {
      await sendMessageToServiceWorker({ 
        type: 'CLEAR_CACHE', 
        payload: { cacheName } 
      });
      
      // Refresh stats after clearing
      setTimeout(() => getCacheStats(), 500);
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }, [isServiceWorkerReady, sendMessageToServiceWorker, getCacheStats]);

  // Force background sync
  const forceBgSync = useCallback(async () => {
    if (!isServiceWorkerReady) return;

    try {
      await sendMessageToServiceWorker({ type: 'FORCE_SYNC' });
      
      // Also trigger local sync
      forceSync();
    } catch (error) {
      console.error('Failed to force background sync:', error);
    }
  }, [isServiceWorkerReady, sendMessageToServiceWorker, forceSync]);

  // Calculate total cached places and routes
  const getCachedCounts = useCallback(() => {
    if (!cacheStats) {
      return { places: 0, routes: 0 };
    }

    const placesCount = cacheStats.caches['PLACES_API']?.count || 0;
    const routesCount = cacheStats.caches['ROUTES_API']?.count || 0;

    return { places: placesCount, routes: routesCount };
  }, [cacheStats]);

  // Format cache size for display
  const formatCacheSize = useCallback((bytes: number): string => {
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
  }, []);

  // Get comprehensive offline status
  const getOfflineStatus = useCallback((): OfflineStatus => {
    const { places, routes } = getCachedCounts();
    
    return {
      isOnline,
      isSyncing: syncStats.isProcessing,
      cachedPlacesCount: places,
      cachedRoutesCount: routes,
      lastSyncTime: syncStats.lastSuccessfulSync,
      pendingActionsCount: syncStats.pendingActions,
      cacheSize: cacheStats?.totalSize || 0,
    };
  }, [isOnline, syncStats, getCachedCounts, cacheStats]);

  // Auto-refresh cache stats periodically
  useEffect(() => {
    if (!isServiceWorkerReady) return;

    const interval = setInterval(() => {
      getCacheStats();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [isServiceWorkerReady, getCacheStats]);

  // Listen for online/offline events to refresh stats
  useEffect(() => {
    const handleOnline = () => {
      // Refresh stats when coming back online
      setTimeout(() => getCacheStats(), 1000);
    };

    window.addEventListener('online', handleOnline);
    return () => window.removeEventListener('online', handleOnline);
  }, [getCacheStats]);

  return {
    // Status
    offlineStatus: getOfflineStatus(),
    isServiceWorkerReady,
    cacheStats,
    lastCacheUpdate,
    
    // Actions
    getCacheStats,
    clearCache,
    forceBgSync,
    clearQueue,
    
    // Utilities
    formatCacheSize,
    getCachedCounts,
    
    // Raw data
    syncStats,
    isOnline,
  };
}

// Hook for components that only need basic offline status
export function useBasicOfflineStatus() {
  const { offlineStatus } = useOfflineStatus();
  return offlineStatus;
}

// Hook for service worker registration and management
export function useServiceWorkerRegistration() {
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [isRegistering, setIsRegistering] = useState(false);
  const [registrationError, setRegistrationError] = useState<string | null>(null);
  const [syncRegistered, setSyncRegistered] = useState<Record<string, boolean>>({
    'place-search-sync': false,
    'route-calculation-sync': false,
    'venue-discovery-sync': false,
    'itinerary-update-sync': false,
    'periodic-sync-all': false
  });

  const registerServiceWorker = useCallback(async () => {
    if (!('serviceWorker' in navigator)) {
      setRegistrationError('Service Workers not supported');
      return;
    }

    setIsRegistering(true);
    setRegistrationError(null);

    try {
      const reg = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
      });

      setRegistration(reg);
      console.log('🏆 FIFA Service Worker registered successfully');

      // Listen for updates
      reg.addEventListener('updatefound', () => {
        console.log('🔄 Service Worker update found');
      });

      // Register background sync
      await registerBackgroundSync(reg);

    } catch (error) {
      console.error('Service Worker registration failed:', error);
      setRegistrationError(error instanceof Error ? error.message : 'Registration failed');
    } finally {
      setIsRegistering(false);
    }
  }, []);

  const registerBackgroundSync = useCallback(async (reg: ServiceWorkerRegistration) => {
    // Inline type definitions for SyncManager and PeriodicSyncManager
    type SyncManager = {
      register: (tag: string) => Promise<void>;
    };
    type PeriodicSyncManager = {
      register: (tag: string, options: { minInterval: number }) => Promise<void>;
    };

    if ('sync' in reg) {
      const syncManager = (reg as any).sync as SyncManager;
      try {
        // Register one-time sync for each action type
        const syncTags = [
          'place-search-sync',
          'route-calculation-sync',
          'venue-discovery-sync',
          'itinerary-update-sync'
        ];

        for (const tag of syncTags) {
          try {
            await syncManager.register(tag);
            setSyncRegistered(prev => ({ ...prev, [tag]: true }));
            console.log(`Background sync registered for ${tag}`);
          } catch (error) {
            console.error(`Failed to register background sync for ${tag}:`, error);
          }
        }

        // Register periodic sync if supported
        if ('periodicSync' in reg) {
          const periodicSyncManager = (reg as any).periodicSync as PeriodicSyncManager;
          try {
            // Check permission
            const status = await navigator.permissions.query({
              name: 'periodic-background-sync' as any
            });

            if (status.state === 'granted') {
              await periodicSyncManager.register('periodic-sync-all', {
                minInterval: 60 * 60 * 1000 // 1 hour
              });
              setSyncRegistered(prev => ({ ...prev, 'periodic-sync-all': true }));
              console.log('Periodic background sync registered');
            } else {
              console.log('Periodic background sync permission not granted');
            }
          } catch (error) {
            console.error('Failed to register periodic background sync:', error);
          }
        }
      } catch (error) {
        console.error('Background sync registration failed:', error);
      }
    } else {
      console.log('Background sync not supported');
    }
  }, []);

  const unregisterServiceWorker = useCallback(async () => {
    if (registration) {
      try {
        // Unregister background sync if possible
        if ('sync' in registration) {
          const syncTags = [
            'place-search-sync',
            'route-calculation-sync',
            'venue-discovery-sync',
            'itinerary-update-sync'
          ];

          // We can't actually unregister sync events, but we can update our state
          setSyncRegistered({
            'place-search-sync': false,
            'route-calculation-sync': false,
            'venue-discovery-sync': false,
            'itinerary-update-sync': false,
            'periodic-sync-all': false
          });
        }

        await registration.unregister();
        setRegistration(null);
        console.log('Service Worker unregistered');
      } catch (error) {
        console.error('Failed to unregister Service Worker:', error);
      }
    }
  }, [registration]);

  // Auto-register on mount
  useEffect(() => {
    registerServiceWorker();
  }, [registerServiceWorker]);

  return {
    registration,
    isRegistering,
    registrationError,
    registerServiceWorker,
    unregisterServiceWorker,
    isSupported: 'serviceWorker' in navigator,
    syncRegistered
  };
}
