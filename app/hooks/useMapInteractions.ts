/**
 * Enhanced Map Interactions Hook with Framer Motion Animations
 *
 * Standardized map event handling with mobile gesture support, context-aware interactions,
 * and comprehensive animation system for micro-interactions and visual feedback.
 *
 * Features:
 * - Gesture-based animations with 60fps performance
 * - Marker animations (bounce, fade, scale) for place selection
 * - Panel transitions (slide, expand, collapse) for UI elements
 * - Route polyline drawing animations with FIFA colors
 * - Mobile-first responsive animation patterns
 */

import { useCallback, useMemo, useRef, useState, useEffect } from 'react';
import { useGestures } from './useGestures';
import { useOfflineSync } from './useOfflineSync';
import { useEnhancedMapContext } from '~/components/maps/EnhancedMapProvider';
import { showInfo, showSuccess, showWarning } from '~/components/wanderlust/NotificationSystem';
import { useMapPerformanceMonitoring } from './usePerformanceMonitoring';
import type { VisitedPlace } from '~/types/wanderlust';
import type { MapMode } from '~/components/maps/BaseMapComponent';
import type { AnimationControls, Variants } from 'framer-motion';

export interface MapInteractionConfig {
  mode: MapMode;
  enableGestures: boolean;
  enableSelection: boolean;
  enableContextMenu: boolean;
  enableLongPress: boolean;
  enableAnimations?: boolean;
  animationDuration?: number;
  onPlaceSelect?: (place: VisitedPlace) => void;
  onMapClick?: (event: google.maps.MapMouseEvent) => void;
  onLongPress?: (event: google.maps.MapMouseEvent) => void;
  onDoubleClick?: (event: google.maps.MapMouseEvent) => void;
  onAnimationStart?: (type: AnimationType) => void;
  onAnimationComplete?: (type: AnimationType) => void;
}

export interface MapEventHandlers {
  onMapClick: (event: google.maps.MapMouseEvent) => void;
  onMapDrag: () => void;
  onMapDragEnd: () => void;
  onZoomChanged: () => void;
  onMapIdle: () => void;
  onPlaceSelect: (place: VisitedPlace) => void;
}

export interface GestureState {
  isActive: boolean;
  isDragging: boolean;
  isLongPressing: boolean;
  lastTapTime: number;
}

export type AnimationType =
  | 'marker-bounce'
  | 'marker-fade'
  | 'marker-scale'
  | 'panel-slide'
  | 'panel-expand'
  | 'panel-collapse'
  | 'route-draw'
  | 'gesture-feedback'
  | 'selection-highlight';

export interface AnimationState {
  isAnimating: boolean;
  currentAnimation: AnimationType | null;
  animationProgress: number;
  queuedAnimations: AnimationType[];
}

export interface MarkerAnimationConfig {
  bounce: {
    duration: number;
    height: number;
    ease: string;
  };
  fade: {
    duration: number;
    opacity: [number, number];
    ease: string;
  };
  scale: {
    duration: number;
    scale: [number, number];
    ease: string;
  };
}

export interface PanelAnimationConfig {
  slide: {
    duration: number;
    distance: number;
    ease: string;
  };
  expand: {
    duration: number;
    scale: [number, number];
    ease: string;
  };
  collapse: {
    duration: number;
    height: [string, string];
    ease: string;
  };
}

// FIFA Club World Cup 2025™ Animation Colors
const FIFA_COLORS = {
  primary: '#000000',      // Black
  secondary: '#FFD700',    // Gold
  accent: '#DC143C',       // Crimson Red
  gradient: 'linear-gradient(135deg, #000000 0%, #DC143C 100%)',
} as const;

// Animation configurations optimized for 60fps performance
const MARKER_ANIMATIONS: MarkerAnimationConfig = {
  bounce: {
    duration: 0.6,
    height: 20,
    ease: 'easeOut',
  },
  fade: {
    duration: 0.3,
    opacity: [0, 1],
    ease: 'easeInOut',
  },
  scale: {
    duration: 0.4,
    scale: [0.8, 1.2],
    ease: 'easeOut',
  },
};

const PANEL_ANIMATIONS: PanelAnimationConfig = {
  slide: {
    duration: 0.5,
    distance: 300,
    ease: 'easeInOut',
  },
  expand: {
    duration: 0.4,
    scale: [0.95, 1],
    ease: 'easeOut',
  },
  collapse: {
    duration: 0.3,
    height: ['auto', '0px'],
    ease: 'easeIn',
  },
};

// Framer Motion variants for different animation types
const ANIMATION_VARIANTS: Record<AnimationType, Variants> = {
  'marker-bounce': {
    initial: { y: 0, scale: 1 },
    animate: {
      y: [-MARKER_ANIMATIONS.bounce.height, 0],
      scale: [1, 1.1, 1],
      transition: {
        duration: MARKER_ANIMATIONS.bounce.duration,
        ease: MARKER_ANIMATIONS.bounce.ease,
      }
    },
  },
  'marker-fade': {
    initial: { opacity: 0, scale: 0.8 },
    animate: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: MARKER_ANIMATIONS.fade.duration,
        ease: MARKER_ANIMATIONS.fade.ease,
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      transition: { duration: 0.2 }
    },
  },
  'marker-scale': {
    initial: { scale: 0.8 },
    animate: {
      scale: MARKER_ANIMATIONS.scale.scale,
      transition: {
        duration: MARKER_ANIMATIONS.scale.duration,
        ease: MARKER_ANIMATIONS.scale.ease,
      }
    },
  },
  'panel-slide': {
    initial: { x: PANEL_ANIMATIONS.slide.distance, opacity: 0 },
    animate: {
      x: 0,
      opacity: 1,
      transition: {
        duration: PANEL_ANIMATIONS.slide.duration,
        ease: PANEL_ANIMATIONS.slide.ease,
      }
    },
    exit: {
      x: -PANEL_ANIMATIONS.slide.distance,
      opacity: 0,
      transition: { duration: 0.3 }
    },
  },
  'panel-expand': {
    initial: { scale: 0.95, opacity: 0 },
    animate: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: PANEL_ANIMATIONS.expand.duration,
        ease: PANEL_ANIMATIONS.expand.ease,
      }
    },
  },
  'panel-collapse': {
    initial: { height: 'auto', opacity: 1 },
    animate: {
      height: 0,
      opacity: 0,
      transition: {
        duration: PANEL_ANIMATIONS.collapse.duration,
        ease: PANEL_ANIMATIONS.collapse.ease,
      }
    },
  },
  'route-draw': {
    initial: { pathLength: 0, opacity: 0 },
    animate: {
      pathLength: 1,
      opacity: 1,
      transition: {
        duration: 1.5,
        ease: 'easeInOut',
      }
    },
  },
  'gesture-feedback': {
    initial: { scale: 1, opacity: 0.7 },
    animate: {
      scale: [1, 1.2, 1],
      opacity: [0.7, 1, 0.7],
      transition: {
        duration: 0.6,
        ease: 'easeInOut',
        repeat: 1,
      }
    },
  },
  'selection-highlight': {
    initial: {
      boxShadow: '0 0 0 0px rgba(220, 20, 60, 0.4)',
      borderColor: 'transparent',
    },
    animate: {
      boxShadow: '0 0 0 8px rgba(220, 20, 60, 0.1)',
      borderColor: FIFA_COLORS.accent,
      transition: {
        duration: 0.4,
        ease: 'easeOut',
      }
    },
  },
};

// Default configuration
const DEFAULT_CONFIG: Partial<MapInteractionConfig> = {
  enableGestures: true,
  enableSelection: true,
  enableContextMenu: false,
  enableLongPress: true,
  enableAnimations: true,
  animationDuration: 0.4,
};

export function useMapInteractions(config: Partial<MapInteractionConfig> = {}) {
  const {
    mode = 'general',
    enableGestures = true,
    enableSelection = true,
    enableContextMenu = false,
    enableLongPress = true,
    enableAnimations = true,
    animationDuration = 0.4,
    onPlaceSelect,
    onMapClick,
    onLongPress,
    onDoubleClick,
    onAnimationStart,
    onAnimationComplete,
  } = { ...DEFAULT_CONFIG, ...config };

  // Get enhanced map context and offline sync
  const {
    setSelectedPlace,
    setInteracting,
    isMapReady,
    getMap,
  } = useEnhancedMapContext();

  const { isOnline } = useOfflineSync();
  const { measureSearch } = useMapPerformanceMonitoring();

  // Simple async wrapper for performance monitoring
  const measureAsync = useCallback(async <T>(operation: string, asyncFn: () => Promise<T>): Promise<T> => {
    const startTime = performance.now();
    try {
      const result = await asyncFn();
      const endTime = performance.now();
      if (process.env.NODE_ENV === 'development') {
        console.log(`${operation} completed in ${(endTime - startTime).toFixed(2)}ms`);
      }
      return result;
    } catch (error) {
      const endTime = performance.now();
      if (process.env.NODE_ENV === 'development') {
        console.error(`${operation} failed after ${(endTime - startTime).toFixed(2)}ms:`, error);
      }
      throw error;
    }
  }, []);

  // Animation state management
  const [animationState, setAnimationState] = useState<AnimationState>({
    isAnimating: false,
    currentAnimation: null,
    animationProgress: 0,
    queuedAnimations: [],
  });

  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Animation control functions
  const triggerAnimation = useCallback(async (type: AnimationType, duration?: number) => {
    if (!enableAnimations) return;

    const animDuration = duration || animationDuration;

    // Performance monitoring for animations
    return measureAsync(`animation-${type}`, async () => {
      // Clear any existing animation timeout
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }

      // Update animation state
      setAnimationState(prev => ({
        ...prev,
        isAnimating: true,
        currentAnimation: type,
        animationProgress: 0,
      }));

      // Trigger animation start callback
      onAnimationStart?.(type);

      // Simulate animation progress (in real implementation, this would be driven by Framer Motion)
      return new Promise<void>((resolve) => {
        animationTimeoutRef.current = setTimeout(() => {
          setAnimationState(prev => ({
            ...prev,
            isAnimating: false,
            currentAnimation: null,
            animationProgress: 1,
          }));

          // Trigger animation complete callback
          onAnimationComplete?.(type);
          resolve();
        }, animDuration * 1000);
      });
    });
  }, [enableAnimations, animationDuration, measureAsync, onAnimationStart, onAnimationComplete]);

  // Specific animation triggers
  const animateMarkerBounce = useCallback((markerId?: string) => {
    return triggerAnimation('marker-bounce', MARKER_ANIMATIONS.bounce.duration);
  }, [triggerAnimation]);

  const animateMarkerFade = useCallback((markerId?: string, fadeIn: boolean = true) => {
    return triggerAnimation('marker-fade', MARKER_ANIMATIONS.fade.duration);
  }, [triggerAnimation]);

  const animateMarkerScale = useCallback((markerId?: string) => {
    return triggerAnimation('marker-scale', MARKER_ANIMATIONS.scale.duration);
  }, [triggerAnimation]);

  const animatePanelSlide = useCallback((direction: 'in' | 'out' = 'in') => {
    return triggerAnimation('panel-slide', PANEL_ANIMATIONS.slide.duration);
  }, [triggerAnimation]);

  const animatePanelExpand = useCallback(() => {
    return triggerAnimation('panel-expand', PANEL_ANIMATIONS.expand.duration);
  }, [triggerAnimation]);

  const animatePanelCollapse = useCallback(() => {
    return triggerAnimation('panel-collapse', PANEL_ANIMATIONS.collapse.duration);
  }, [triggerAnimation]);

  const animateRouteDraw = useCallback((routeId?: string) => {
    return triggerAnimation('route-draw', 1.5);
  }, [triggerAnimation]);

  const animateGestureFeedback = useCallback((gestureType: string) => {
    return triggerAnimation('gesture-feedback', 0.6);
  }, [triggerAnimation]);

  const animateSelectionHighlight = useCallback((elementId?: string) => {
    return triggerAnimation('selection-highlight', 0.4);
  }, [triggerAnimation]);

  // Cleanup animation timeout on unmount
  useEffect(() => {
    return () => {
      if (animationTimeoutRef.current) {
        clearTimeout(animationTimeoutRef.current);
      }
    };
  }, []);

  // Mode-specific interaction behaviors
  const getModeConfig = useCallback((interactionMode: MapMode) => {
    switch (interactionMode) {
      case 'route-planning':
        return {
          longPressAction: 'Add waypoint at this location',
          doubleClickAction: 'Zoom to location',
          selectionFeedback: 'Waypoint selected',
          gestureConfig: {
            longPressDelay: 800,
            swipeThreshold: 50,
            doubleTapDelay: 300,
          },
        };
      case 'venue-discovery':
        return {
          longPressAction: 'Explore venues near this location',
          doubleClickAction: 'Search venues here',
          selectionFeedback: 'Venue selected for exploration',
          gestureConfig: {
            longPressDelay: 600,
            swipeThreshold: 44,
            doubleTapDelay: 250,
          },
        };
      case 'general':
      default:
        return {
          longPressAction: 'Show location details',
          doubleClickAction: 'Zoom to location',
          selectionFeedback: 'Location selected',
          gestureConfig: {
            longPressDelay: 500,
            swipeThreshold: 44,
            doubleTapDelay: 300,
          },
        };
    }
  }, []);

  const modeConfig = getModeConfig(mode);

  // Handle map click with mode-aware behavior and offline feedback
  const handleMapClick = useCallback((event: google.maps.MapMouseEvent) => {
    if (!enableSelection) return;

    // Always deselect current selection on map click
    setSelectedPlace(null);

    // Call custom handler if provided
    onMapClick?.(event);

    // Mode-specific click behavior with offline awareness
    if (mode === 'venue-discovery' && event.latLng) {
      if (isOnline) {
        showInfo('Map Click', 'Click on venues to explore them');
      } else {
        showWarning('Offline Mode', 'Limited venue data available offline. Connect to explore more venues.');
      }
    }
  }, [enableSelection, setSelectedPlace, onMapClick, mode, isOnline]);

  // Handle place selection with mode-aware feedback and animations
  const handlePlaceSelect = useCallback(async (place: VisitedPlace) => {
    if (!enableSelection) return;

    setSelectedPlace(place);
    onPlaceSelect?.(place);

    // Trigger selection animations
    if (enableAnimations) {
      await Promise.all([
        triggerAnimation('selection-highlight'),
        triggerAnimation('marker-bounce'),
      ]);
    }

    // Mode-specific feedback
    const modeMessages = {
      'route-planning': 'Waypoint selected - tap to view details',
      'venue-discovery': 'Venue selected - explore details and reviews',
      'general': 'Location selected',
    };

    showInfo(modeMessages[mode], place.name);
  }, [enableSelection, setSelectedPlace, onPlaceSelect, mode, enableAnimations, triggerAnimation]);

  // Handle long press with mode-aware actions
  const handleLongPress = useCallback((event?: google.maps.MapMouseEvent) => {
    if (!enableLongPress || !isMapReady) return;

    const map = getMap();
    if (!map || !event?.latLng) return;

    // Mode-specific long press actions
    switch (mode) {
      case 'route-planning':
        showInfo('Long Press', 'Add waypoint feature coming soon');
        break;
      case 'venue-discovery':
        showInfo('Long Press', 'Searching for venues near this location...');
        break;
      case 'general':
      default:
        showInfo('Long Press', `Location: ${event.latLng.lat().toFixed(4)}, ${event.latLng.lng().toFixed(4)}`);
        break;
    }

    onLongPress?.(event);
  }, [enableLongPress, isMapReady, getMap, mode, onLongPress]);

  // Handle double click with mode-aware actions
  const handleDoubleClick = useCallback((event: google.maps.MapMouseEvent) => {
    if (!isMapReady) return;

    const map = getMap();
    if (!map || !event.latLng) return;

    // Mode-specific double click actions
    switch (mode) {
      case 'route-planning':
        // Zoom to location for better route planning
        const currentZoom = map.getZoom() ?? 14;
        map.setZoom(Math.min(currentZoom + 2, 18));
        map.panTo(event.latLng);
        showSuccess('Zoomed', 'Zoomed for better route planning');
        break;
      case 'venue-discovery':
        // Could trigger venue search at location
        showInfo('Double Clickxx', 'Searching venues at this location...');
        break;
      case 'general':
      default:
        map.setZoom(Math.min((map.getZoom() ?? 14) + 1, 18));
        map.panTo(event.latLng);
        break;
    }

    onDoubleClick?.(event);
  }, [isMapReady, getMap, mode, onDoubleClick]);

  // Handle map drag events
  const handleMapDrag = useCallback(() => {
    setInteracting(true);
  }, [setInteracting]);

  const handleMapDragEnd = useCallback(() => {
    setInteracting(false);
  }, [setInteracting]);

  // Handle zoom changes
  const handleZoomChanged = useCallback(() => {
    setInteracting(true);
  }, [setInteracting]);

  const handleMapIdle = useCallback(() => {
    setInteracting(false);
  }, [setInteracting]);

  // Gesture handling with mode-aware configuration
  const { ref: gestureRef, gestureState } = useGestures({
    onLongPress: handleLongPress,
    onDragStart: () => handleMapDrag(),
    onDragEnd: () => handleMapDragEnd(),
    config: modeConfig.gestureConfig,
    disabled: !enableGestures || !isMapReady,
  });

  // Event handlers object
  const eventHandlers: MapEventHandlers = useMemo(() => ({
    onMapClick: handleMapClick,
    onMapDrag: handleMapDrag,
    onMapDragEnd: handleMapDragEnd,
    onZoomChanged: handleZoomChanged,
    onMapIdle: handleMapIdle,
    onPlaceSelect: handlePlaceSelect,
  }), [
    handleMapClick,
    handleMapDrag,
    handleMapDragEnd,
    handleZoomChanged,
    handleMapIdle,
    handlePlaceSelect,
  ]);

  // Gesture state with additional interaction info
  const enhancedGestureState: GestureState = useMemo(() => ({
    isActive: gestureState?.isActive ?? false,
    isDragging: gestureState?.isDragging ?? false,
    isLongPressing: gestureState?.isLongPressing ?? false,
    lastTapTime: gestureState?.lastTapTime ?? 0,
  }), [gestureState]);

  return {
    gestureRef,
    eventHandlers,
    gestureState: enhancedGestureState,
    modeConfig,
    config: {
      mode,
      enableGestures,
      enableSelection,
      enableContextMenu,
      enableLongPress,
      enableAnimations,
      animationDuration,
    },
    // Animation state and controls
    animationState,
    animations: {
      // Marker animations
      animateMarkerBounce,
      animateMarkerFade,
      animateMarkerScale,
      // Panel animations
      animatePanelSlide,
      animatePanelExpand,
      animatePanelCollapse,
      // Route animations
      animateRouteDraw,
      // Gesture feedback
      animateGestureFeedback,
      animateSelectionHighlight,
      // Generic animation trigger
      triggerAnimation,
    },
    // Animation variants for Framer Motion components
    animationVariants: ANIMATION_VARIANTS,
    // FIFA colors for consistent theming
    fifaColors: FIFA_COLORS,
  };
}
