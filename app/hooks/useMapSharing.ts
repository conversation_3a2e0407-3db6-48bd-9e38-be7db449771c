/**
 * useMapSharing Hook for Task 15: Social Sharing Features
 *
 * Provides Web Share API integration for sharing current map view,
 * selected locations, or planned routes via social media platforms.
 */

import { useCallback, useState } from 'react';
// Removed useMapPerformance - simplified architecture
import { showError, showSuccess, showInfo, showWarning } from '~/components/wanderlust/NotificationSystem';
import type { VisitedPlace } from '~/types/wanderlust';

export interface ShareData {
  title: string;
  text: string;
  url: string;
  image?: string;
}

export interface ShareOptions {
  includeLocation?: boolean;
  includeRoute?: boolean;
  customMessage?: string;
  platform?: 'native' | 'twitter' | 'facebook' | 'whatsapp' | 'email' | 'copy';
}

export function useMapSharing() {
  const [isSharing, setIsSharing] = useState(false);

  // Simplified - no performance monitoring needed
  const measureInteractionLatency = () => () => {}; // No-op function

  // Check if Web Share API is supported
  const isWebShareSupported = useCallback((): boolean => {
    return 'share' in navigator;
  }, []);

  // Check if clipboard API is supported
  const isClipboardSupported = useCallback((): boolean => {
    return 'clipboard' in navigator && 'writeText' in navigator.clipboard;
  }, []);

  // Generate share URL with parameters
  const generateShareUrl = useCallback((place?: VisitedPlace, options: ShareOptions = {}): string => {
    const baseUrl = window.location.origin + window.location.pathname;
    const params = new URLSearchParams();

    // Add place information
    if (place && options.includeLocation) {
      params.append('place', place.id);
      params.append('lat', place.coordinates.latitude.toString());
      params.append('lng', place.coordinates.longitude.toString());
      params.append('name', encodeURIComponent(place.name));
    }

    // Add current map view
    const currentUrl = new URL(window.location.href);
    const existingParams = currentUrl.searchParams;

    // Preserve existing map state
    if (existingParams.has('zoom')) {
      params.append('zoom', existingParams.get('zoom')!);
    }
    if (existingParams.has('center')) {
      params.append('center', existingParams.get('center')!);
    }

    // Add route information if available
    if (options.includeRoute) {
      params.append('route', 'true');
    }

    // Add sharing source for analytics
    params.append('shared', 'true');
    params.append('timestamp', Date.now().toString());

    return `${baseUrl}?${params.toString()}`;
  }, []);

  // Generate share content
  const generateShareContent = useCallback((place?: VisitedPlace, options: ShareOptions = {}): ShareData => {
    const url = generateShareUrl(place, options);

    let title = 'Wanderlust Explorer - FIFA Club World Cup 2025™';
    let text = 'Explore amazing places on this interactive map';

    if (place) {
      title = `${place.name} - Wanderlust Explorer`;
      text = options.customMessage ||
             `Check out ${place.name}! ${place.description.en.substring(0, 100)}${place.description.en.length > 100 ? '...' : ''}`;
    } else if (options.includeRoute) {
      title = 'My Route - Wanderlust Explorer';
      text = options.customMessage || 'Check out my planned route on the interactive map!';
    } else {
      text = options.customMessage || 'Discover amazing places with this interactive map for FIFA Club World Cup 2025™';
    }

    return {
      title,
      text,
      url,
    };
  }, [generateShareUrl]);

  // Share via Web Share API
  const shareViaWebShare = useCallback(async (shareData: ShareData): Promise<boolean> => {
    if (!isWebShareSupported()) {
      return false;
    }

    try {
      await navigator.share(shareData);
      return true;
    } catch (error) {
      // User cancelled or error occurred
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Web Share API error:', error);
      }
      return false;
    }
  }, [isWebShareSupported]);

  // Copy to clipboard
  const copyToClipboard = useCallback(async (text: string): Promise<boolean> => {
    if (isClipboardSupported()) {
      try {
        await navigator.clipboard.writeText(text);
        return true;
      } catch (error) {
        console.error('Clipboard API error:', error);
      }
    }

    // Fallback for older browsers
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      return successful;
    } catch (error) {
      console.error('Fallback clipboard error:', error);
      return false;
    }
  }, [isClipboardSupported]);

  // Share via specific platform
  const shareViaPlatform = useCallback((shareData: ShareData, platform: string): void => {
    const encodedUrl = encodeURIComponent(shareData.url);
    const encodedText = encodeURIComponent(shareData.text);
    const encodedTitle = encodeURIComponent(shareData.title);

    let shareUrl = '';

    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedText}`;
        break;
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodedText}%20${encodedUrl}`;
        break;
      case 'email':
        shareUrl = `mailto:?subject=${encodedTitle}&body=${encodedText}%0A%0A${encodedUrl}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`;
        break;
      default:
        showError('Share Failed', 'Unsupported sharing platform');
        return;
    }

    // Open in new window/tab
    const popup = window.open(
      shareUrl,
      'share-popup',
      'width=600,height=400,scrollbars=yes,resizable=yes'
    );

    if (!popup) {
      showError('Share Failed', 'Popup blocked. Please allow popups for sharing.');
    }
  }, []);

  // Main share function
  const shareMapView = useCallback(async (place?: VisitedPlace, options: ShareOptions = {}): Promise<void> => {
    if (isSharing) {
      showWarning('Share in Progress', 'Please wait for the current share to complete');
      return;
    }

    setIsSharing(true);
    const endMeasurement = measureInteractionLatency();

    try {
      const shareData = generateShareContent(place, options);

      // Determine sharing method based on options and browser support
      if (options.platform === 'copy' || (!isWebShareSupported() && !options.platform)) {
        // Copy to clipboard
        const success = await copyToClipboard(shareData.url);
        if (success) {
          showSuccess('Link Copied', 'Map link copied to clipboard');
        } else {
          showError('Copy Failed', 'Unable to copy link to clipboard');
        }
      } else if (options.platform && options.platform !== 'native') {
        // Share via specific platform
        shareViaPlatform(shareData, options.platform);
        showInfo('Share Opened', `Sharing via ${options.platform}`);
      } else {
        // Try Web Share API first, fallback to clipboard
        const webShareSuccess = await shareViaWebShare(shareData);

        if (webShareSuccess) {
          showSuccess('Shared Successfully', 'Map view shared successfully');
        } else {
          // Fallback to clipboard
          const clipboardSuccess = await copyToClipboard(shareData.url);
          if (clipboardSuccess) {
            showSuccess('Link Copied', 'Map link copied to clipboard');
          } else {
            showError('Share Failed', 'Unable to share map view');
          }
        }
      }
    } catch (error) {
      showError('Share Error', 'An error occurred while sharing');
      console.error('Share error:', error);
    } finally {
      setIsSharing(false);
      endMeasurement();
    }
  }, [isSharing, measureInteractionLatency, generateShareContent, isWebShareSupported, shareViaWebShare, copyToClipboard, shareViaPlatform]);

  // Share current location
  const shareCurrentLocation = useCallback(async (position: GeolocationPosition, options: ShareOptions = {}): Promise<void> => {
    const customPlace: VisitedPlace = {
      id: 'current-location',
      name: 'My Current Location',
      description: {
        en: 'My current location on the map',
        fr: 'Ma position actuelle sur la carte',
        ar: 'موقعي الحالي على الخريطة',
      },
      coordinates: {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
      },
      category: 'landmark',
      rating: 0,
      visitDate: new Date().toISOString(),
      icon: '📍',
      city: 'Current Location',
      region: 'Unknown',
      revisitPotential: 'Worth a Look',
    };

    await shareMapView(customPlace, { ...options, includeLocation: true });
  }, [shareMapView]);

  // Share route
  const shareRoute = useCallback(async (waypoints: VisitedPlace[], options: ShareOptions = {}): Promise<void> => {
    if (waypoints.length === 0) {
      showWarning('No Route', 'No route to share');
      return;
    }

    const routeText = waypoints.length === 1
      ? `Check out ${waypoints[0].name}!`
      : `Check out my route: ${waypoints.map(p => p.name).join(' → ')}`;

    await shareMapView(undefined, {
      ...options,
      includeRoute: true,
      customMessage: options.customMessage || routeText,
    });
  }, [shareMapView]);

  // Generate QR code URL for sharing
  const generateQRCodeUrl = useCallback((place?: VisitedPlace, options: ShareOptions = {}): string => {
    const shareUrl = generateShareUrl(place, options);
    // Using a free QR code service - in production, you might want to use your own service
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(shareUrl)}`;
  }, [generateShareUrl]);

  return {
    // State
    isSharing,

    // Methods
    shareMapView,
    shareCurrentLocation,
    shareRoute,
    copyToClipboard,

    // Utilities
    isWebShareSupported: isWebShareSupported(),
    isClipboardSupported: isClipboardSupported(),
    generateShareUrl,
    generateQRCodeUrl,
    generateShareContent,
  };
}
