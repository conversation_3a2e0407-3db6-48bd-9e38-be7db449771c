/**
 * Enhanced Place Search Hook
 *
 * Unified search hook that combines local data search with Google Places API.
 * Provides intelligent result merging, caching, and context-aware filtering.
 */

import { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { useGooglePlaces } from './useGooglePlaces';
import { usePlaceSearch } from './usePlaceSearch';
import { useOfflineSync } from './useOfflineSync';
import { useEnhancedMapContext } from '~/components/maps/EnhancedMapProvider';
import { showError, showInfo, showWarning } from '~/components/wanderlust/NotificationSystem';
import type { CityRegion, VisitedPlace } from '~/types/wanderlust';
import type { PlaceSearchResult } from './useGooglePlaces';

export type SearchMode = 'local' | 'google' | 'hybrid';

export interface EnhancedSearchOptions {
  searchMode: SearchMode;
  enableCategoryFilter: boolean;
  enableLocationBias: boolean;
  cacheResults: boolean;
  debounceMs?: number;
  maxResults?: number;
}

export interface SearchResult {
  source: 'local' | 'google';
  place: VisitedPlace;
  relevanceScore: number;
  distance?: number;
  matchType: 'exact' | 'partial' | 'fuzzy';
}

export interface SearchState {
  query: string;
  results: SearchResult[];
  isSearching: boolean;
  selectedCategory: string;
  searchHistory: string[];
  recentSearches: string[];
  lastSearchTime: number;
  resultCount: number;
  // Advanced filtering properties
  priceRange: [number, number];
  minRating: number;
  distanceRadius: number;
  showAdvancedFilters: boolean;
}

export interface SearchActions {
  search: (query: string) => void;
  clearResults: () => void;
  setCategory: (category: string) => void;
  addToHistory: (query: string) => void;
  removeFromHistory: (query: string) => void;
  clearHistory: () => void;
  setQuery: (query: string) => void;
  // Advanced filtering actions
  setPriceRange: (range: [number, number]) => void;
  setMinRating: (rating: number) => void;
  setDistanceRadius: (radius: number) => void;
  toggleAdvancedFilters: () => void;
  resetAllFilters: () => void;
}

// Default options
const DEFAULT_OPTIONS: EnhancedSearchOptions = {
  searchMode: 'hybrid',
  enableCategoryFilter: true,
  enableLocationBias: true,
  cacheResults: true,
  debounceMs: 300,
  maxResults: 20,
};

export function useEnhancedPlaceSearch(
  regions: CityRegion[],
  options: Partial<EnhancedSearchOptions> = {}
) {
  const config = { ...DEFAULT_OPTIONS, ...options };
  const { searchMode, enableCategoryFilter, cacheResults, debounceMs, maxResults } = config;

  // Get enhanced map context and offline sync
  const enhancedMapContext = useEnhancedMapContext();
  const { isOnline, queueAction } = useOfflineSync();

  // Local search hook
  const {
    setSearchQuery: setLocalQuery,
    selectedCategory,
    setSelectedCategory,
    filteredPlaces,
    categories,
  } = usePlaceSearch(regions, enhancedMapContext.places || []);

  // Google Places hook
  const {
    searchPlaces: googleSearchPlaces,
    isSearching: isGoogleSearching,
    clearSearchResults: clearGoogleResults,
  } = useGooglePlaces(enhancedMapContext.getMap() || undefined);

  // Enhanced search state
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [lastSearchTime, setLastSearchTime] = useState(0);
  
  // Advanced filtering state
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [minRating, setMinRating] = useState<number>(0);
  const [distanceRadius, setDistanceRadius] = useState<number>(10);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState<boolean>(false);

  // Debounce timer
  const debounceTimerRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // Result cache
  const cacheRef = useRef<Map<string, { results: SearchResult[]; timestamp: number }>>(new Map());
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Calculate relevance score for local results
  const calculateLocalRelevance = useCallback((place: VisitedPlace, searchQuery: string): number => {
    const query = searchQuery.toLowerCase();
    const name = place.name.toLowerCase();
    const description = place.description.en.toLowerCase();
    const city = place.city.toLowerCase();

    let score = 0;

    // Exact name match
    if (name === query) score += 100;
    // Name starts with query
    else if (name.startsWith(query)) score += 80;
    // Name contains query
    else if (name.includes(query)) score += 60;

    // Description matches
    if (description.includes(query)) score += 30;

    // City matches
    if (city.includes(query)) score += 20;

    // Category bonus if filtering is enabled
    if (enableCategoryFilter && selectedCategory !== 'all' && place.category === selectedCategory) {
      score += 10;
    }

    // Rating bonus
    if (place.rating) {
      score += place.rating * 2;
    }

    return Math.min(score, 100);
  }, [enableCategoryFilter, selectedCategory]);

  // Convert Google Places result to SearchResult
  const convertGoogleResult = useCallback((googlePlace: PlaceSearchResult, searchQuery: string): SearchResult => {
    // Safely extract coordinates
    const lat = typeof googlePlace.geometry.location.lat === 'function'
      ? googlePlace.geometry.location.lat()
      : (googlePlace.geometry.location.lat as unknown as number);
    const lng = typeof googlePlace.geometry.location.lng === 'function'
      ? googlePlace.geometry.location.lng()
      : (googlePlace.geometry.location.lng as unknown as number);

    const visitedPlace: VisitedPlace = {
      id: googlePlace.place_id,
      name: googlePlace.name,
      description: { en: googlePlace.formatted_address || '', fr: '', ar: '' },
      coordinates: {
        latitude: lat || 0,
        longitude: lng || 0,
      },
      category: 'landmark',
      rating: googlePlace.rating,
      visitDate: undefined,
      icon: '📍',
      city: googlePlace.formatted_address?.split(',')[1]?.trim() || '',
      region: googlePlace.formatted_address?.split(',')[2]?.trim() || '',
      revisitPotential: undefined,
    };

    // Calculate relevance based on name match and rating
    let relevanceScore = 50; // Base score for Google results
    const query = searchQuery.toLowerCase();
    const name = googlePlace.name.toLowerCase();

    if (name.includes(query)) {
      relevanceScore += 30;
    }
    if (googlePlace.rating) {
      relevanceScore += googlePlace.rating * 4;
    }

    return {
      source: 'google',
      place: visitedPlace,
      relevanceScore: Math.min(relevanceScore, 100),
      matchType: name.includes(query) ? 'partial' : 'fuzzy',
    };
  }, []);

  // Search local places
  const searchLocal = useCallback(async (searchQuery: string): Promise<SearchResult[]> => {
    if (!searchQuery.trim()) return [];

    // Update local search
    setLocalQuery(searchQuery);

    // Convert filtered places to SearchResult format
    const localResults: SearchResult[] = filteredPlaces
      .filter((place): place is VisitedPlace => place !== null && place !== undefined)
      .map(place => {
        const relevanceScore = calculateLocalRelevance(place, searchQuery);
        const matchType = place.name.toLowerCase() === searchQuery.toLowerCase() 
          ? 'exact' as const
          : place.name.toLowerCase().includes(searchQuery.toLowerCase()) 
            ? 'partial' as const
            : 'fuzzy' as const;

        return {
          source: 'local' as const,
          place,
          relevanceScore,
          matchType,
        };
      })
      // Apply advanced filters
      .filter(result => {
        // Apply rating filter
        if (minRating > 0 && (!result.place.rating || result.place.rating < minRating)) {
          return false;
        }
        // Remove price filter: VisitedPlace does not have 'price'
        // if (result.place.price && (result.place.price < priceRange[0] || result.place.price > priceRange[1])) {
        //   return false;
        // }
        // Guard distance property
        if ('distance' in result && typeof result.distance === 'number' && result.distance > distanceRadius) {
          return false;
        }
        return true;
      })
      .sort((a, b) => b.relevanceScore - a.relevanceScore);

    return localResults;
  }, [filteredPlaces, calculateLocalRelevance, setLocalQuery, minRating, priceRange, distanceRadius]);

  // Search Google Places
  const searchGoogle = useCallback(async (searchQuery: string): Promise<SearchResult[]> => {
    if (!searchQuery.trim()) return [];

    // If offline, queue the search for later and return empty results
    if (!isOnline) {
      queueAction('place-search', { query: searchQuery });
      showWarning('Offline Mode', 'Search queued for when you\'re back online.');
      return [];
    }

    // Check cache first
    const cacheKey = `google:${searchQuery}:${selectedCategory}`;
    if (cacheResults && cacheRef.current.has(cacheKey)) {
      const cached = cacheRef.current.get(cacheKey)!;
      if (Date.now() - cached.timestamp < CACHE_DURATION) {
        return cached.results;
      }
    }

    try {
      // Perform Google Places search
      const googleResults = await googleSearchPlaces(searchQuery);

      // Convert to our format and apply advanced filters
      const formattedResults = googleResults
        .map(place => convertGoogleResult(place, searchQuery))
        .filter(result => {
          // Apply rating filter
          if (minRating > 0 && (!result.place.rating || result.place.rating < minRating)) {
            return false;
          }
          // Remove price filter: VisitedPlace does not have 'price'
          // if (result.place.price && (result.place.price < priceRange[0] || result.place.price > priceRange[1])) {
          //   return false;
          // }
          // Guard distance property
          if ('distance' in result && typeof result.distance === 'number' && result.distance > distanceRadius) {
            return false;
          }
          return true;
        });

      // Cache results
      if (cacheResults) {
        cacheRef.current.set(cacheKey, {
          results: formattedResults,
          timestamp: Date.now()
        });
      }

      return formattedResults;
    } catch (error) {
      console.error('Google Places search failed:', error);
      showError('Search Failed', 'Unable to search Google Places. Please try again.');
      return [];
    }
  }, [googleSearchPlaces, convertGoogleResult, isOnline, queueAction, selectedCategory, cacheResults, minRating, priceRange, distanceRadius]);

  // Merge and rank results from both sources
  const mergeAndRankResults = useCallback((localResults: SearchResult[], googleResults: SearchResult[]): SearchResult[] => {
    // Combine results
    const combinedResults = [...localResults, ...googleResults];

    // Deduplicate by place ID
    const uniqueResults = combinedResults.reduce<SearchResult[]>((acc, current) => {
      const existingIndex = acc.findIndex(item => item.place.id === current.place.id);
      
      if (existingIndex === -1) {
        // Not found, add to results
        acc.push(current);
      } else {
        // Found duplicate, keep the one with higher relevance score
        if (current.relevanceScore > acc[existingIndex].relevanceScore) {
          acc[existingIndex] = current;
        }
      }
      
      return acc;
    }, []);

    // Sort by relevance score
    return uniqueResults.sort((a, b) => {
      // Primary sort by relevance score
      const relevanceDiff = b.relevanceScore - a.relevanceScore;
      if (Math.abs(relevanceDiff) > 10) return relevanceDiff;
      
      // Secondary sort by source (prefer local results slightly)
      if (a.source !== b.source) {
        return a.source === 'local' ? -1 : 1;
      }
      
      // Tertiary sort by rating
      const aRating = a.place.rating || 0;
      const bRating = b.place.rating || 0;
      return bRating - aRating;
    });
  }, []);

  // Main search function
  const search = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      clearResults();
      return [];
    }

    // Check cache first
    const cacheKey = `${searchQuery}:${selectedCategory}:${minRating}:${priceRange[0]}-${priceRange[1]}:${distanceRadius}`;
    if (cacheResults && cacheRef.current.has(cacheKey)) {
      const cached = cacheRef.current.get(cacheKey)!;
      if (Date.now() - cached.timestamp < CACHE_DURATION) {
        setResults(cached.results);
        return cached.results;
      }
    }

    setIsSearching(true);
    setQuery(searchQuery);
    setLastSearchTime(Date.now());

    try {
      let searchResults: SearchResult[] = [];

      switch (searchMode) {
        case 'local':
          searchResults = await searchLocal(searchQuery);
          break;
        case 'google':
          searchResults = await searchGoogle(searchQuery);
          break;
        case 'hybrid':
        default:
          const [localResults, googleResults] = await Promise.all([
            searchLocal(searchQuery),
            searchGoogle(searchQuery)
          ]);
          searchResults = mergeAndRankResults(localResults, googleResults);
          break;
      }

      setResults(searchResults);

      // Cache results
      if (cacheResults) {
        cacheRef.current.set(cacheKey, {
          results: searchResults,
          timestamp: Date.now()
        });
      }

      // Add to search history
      addToHistory(searchQuery);

      return searchResults;
    } catch (error) {
      console.error('Enhanced search failed:', error);
      showError('Search Failed', 'Unable to search places. Please try again.');
      setResults([]);
      return [];
    } finally {
      setIsSearching(false);
    }
  }, [searchMode, selectedCategory, cacheResults, searchLocal, searchGoogle, mergeAndRankResults, minRating, priceRange, distanceRadius]);

  // Debounced search
  const debouncedSearch = useCallback((searchQuery: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      search(searchQuery);
    }, debounceMs);
  }, [search, debounceMs]);

  // Clear results
  const clearResults = useCallback(() => {
    setResults([]);
    setQuery('');
    setLocalQuery('');
    clearGoogleResults();
  }, [setLocalQuery, clearGoogleResults]);

  // Set category
  const setCategory = useCallback((category: string) => {
    setSelectedCategory(category);
    // Re-search if we have a query
    if (query) {
      search(query);
    }
  }, [setSelectedCategory, query, search]);

  // Add to history
  const addToHistory = useCallback((searchQuery: string) => {
    const trimmedQuery = searchQuery.trim();
    if (trimmedQuery && !searchHistory.includes(trimmedQuery)) {
      setSearchHistory(prev => [trimmedQuery, ...prev.slice(0, 9)]); // Keep last 10
      setRecentSearches(prev => [trimmedQuery, ...prev.slice(0, 4)]); // Keep last 5
    }
  }, [searchHistory]);

  // Remove from history
  const removeFromHistory = useCallback((searchQuery: string) => {
    setSearchHistory(prev => prev.filter(query => query !== searchQuery));
    setRecentSearches(prev => prev.filter(query => query !== searchQuery));
  }, []);

  // Clear history
  const clearHistory = useCallback(() => {
    setSearchHistory([]);
    setRecentSearches([]);
  }, []);
  
  // Toggle advanced filters
  const toggleAdvancedFilters = useCallback(() => {
    setShowAdvancedFilters(prev => !prev);
  }, []);
  
  // Reset all filters
  const resetAllFilters = useCallback(() => {
    setPriceRange([0, 1000]);
    setMinRating(0);
    setDistanceRadius(10);
    setSelectedCategory('all');
    
    // Re-search if we have a query
    if (query) {
      search(query);
    }
  }, [query, search, setSelectedCategory]);

  // Cleanup debounce timer
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // State object
  const searchState: SearchState = useMemo(() => ({
    query,
    results,
    isSearching: isSearching || isGoogleSearching,
    selectedCategory,
    searchHistory,
    recentSearches,
    lastSearchTime,
    resultCount: results.length,
    // Advanced filtering properties
    priceRange,
    minRating,
    distanceRadius,
    showAdvancedFilters,
  }), [
    query, 
    results, 
    isSearching, 
    isGoogleSearching, 
    selectedCategory, 
    searchHistory, 
    recentSearches, 
    lastSearchTime,
    priceRange,
    minRating,
    distanceRadius,
    showAdvancedFilters,
  ]);

  // Actions object
  const searchActions: SearchActions = useMemo(() => ({
    search: debouncedSearch,
    clearResults,
    setCategory,
    addToHistory,
    removeFromHistory,
    clearHistory,
    setQuery,
    // Advanced filtering actions
    setPriceRange,
    setMinRating,
    setDistanceRadius,
    toggleAdvancedFilters,
    resetAllFilters,
  }), [
    debouncedSearch, 
    clearResults, 
    setCategory, 
    addToHistory, 
    removeFromHistory, 
    clearHistory, 
    setQuery,
    setPriceRange,
    setMinRating,
    setDistanceRadius,
    toggleAdvancedFilters,
    resetAllFilters,
  ]);

  return {
    searchState,
    searchActions,
    categories,
    config,
  };
}
