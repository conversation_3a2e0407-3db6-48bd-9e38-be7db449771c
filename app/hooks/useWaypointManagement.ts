import { useCallback } from 'react';
import { useWanderlustStore } from '~/stores/wanderlust';
import { showSuccess, showError } from '~/components/wanderlust/NotificationSystem';
import type { VisitedPlace } from '~/types/wanderlust';

/**
 * Custom hook for waypoint management functionality
 * Provides consistent waypoint operations across components
 */
export function useWaypointManagement() {
  const {
    itinerary,
    addToItinerary,
    removeFromItinerary,
    reorderItinerary,
    clearItinerary,
  } = useWanderlustStore();

  // Handle waypoint addition with success notification
  const handleAddWaypoint = useCallback((place: VisitedPlace) => {
    addToItinerary(place);
    showSuccess(`${place.name} added to route`);
  }, [addToItinerary]);

  // Handle waypoint removal with success notification
  const handleRemoveWaypoint = useCallback((placeId: string) => {
    const placeToRemove = itinerary.find(p => p.id === placeId);
    if (placeToRemove) {
      removeFromItinerary(placeId);
      showSuccess(`${placeToRemove.name} removed from route`);
    }
  }, [removeFromItinerary, itinerary]);

  // Handle waypoint removal by index
  const handleRemoveWaypointByIndex = useCallback((index: number) => {
    const placeToRemove = itinerary[index];
    if (placeToRemove) {
      removeFromItinerary(placeToRemove.id);
      showSuccess(`${placeToRemove.name} removed from route`);
    }
  }, [removeFromItinerary, itinerary]);

  // Handle waypoint reordering
  const handleReorderWaypoint = useCallback((fromIndex: number, toIndex: number) => {
    reorderItinerary(fromIndex, toIndex);
    showSuccess('Waypoint order updated');
  }, [reorderItinerary]);

  // Handle clearing all waypoints
  const handleClearItinerary = useCallback(() => {
    clearItinerary();
    showSuccess('All waypoints cleared');
  }, [clearItinerary]);

  // Handle adding current location
  const handleAddCurrentLocation = useCallback(async () => {
    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000
        });
      });

      const locationPlace: VisitedPlace = {
        id: `current-location-${Date.now()}`,
        name: 'Current Location',
        description: {
          en: 'Your current GPS location',
          fr: 'Votre position GPS actuelle',
          ar: 'موقعك الحالي'
        },
        category: 'transport',
        coordinates: {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        },
        city: 'Current Location',
        region: 'GPS',
        revisitPotential: 'Worth a Look',
        icon: '📍'
      };

      handleAddWaypoint(locationPlace);
      showSuccess('Current location added to route');
    } catch (error) {
      showError('Unable to get current location. Please check permissions.');
    }
  }, [handleAddWaypoint]);

  return {
    itinerary,
    handleAddWaypoint,
    handleRemoveWaypoint,
    handleRemoveWaypointByIndex,
    handleReorderWaypoint,
    handleClearItinerary,
    handleAddCurrentLocation,
  };
}
