/**
 * Map Features Management Hook
 *
 * Manages which features are enabled on the map and provides
 * utilities for feature-specific functionality.
 */

import { useState, useCallback, useMemo } from 'react';

export type MapFeature = 'markers' | 'routes' | 'search' | 'traffic' | 'location' | 'clustering';

export interface MapFeaturesConfig {
  markers?: boolean;
  routes?: boolean;
  search?: boolean;
  traffic?: boolean;
  location?: boolean;
  clustering?: boolean;
}

export interface UseMapFeaturesOptions {
  initialFeatures?: MapFeature[];
  defaultConfig?: MapFeaturesConfig;
}

export function useMapFeatures(
  features: MapFeature[] = ['markers'],
  options: UseMapFeaturesOptions = {}
) {
  const { initialFeatures = features, defaultConfig = {} } = options;

  // Memoize the initial features to prevent unnecessary re-initializations
  const stableInitialFeatures = useMemo(() => initialFeatures, [JSON.stringify(initialFeatures)]);
  const stableDefaultConfig = useMemo(() => defaultConfig, [JSON.stringify(defaultConfig)]);

  // Initialize enabled features based on provided array
  const [enabledFeatures, setEnabledFeatures] = useState<MapFeaturesConfig>(() => {
    const config: MapFeaturesConfig = {
      markers: false,
      routes: false,
      search: false,
      traffic: false,
      location: false,
      ...stableDefaultConfig,
    };

    // Enable features from the initial array
    stableInitialFeatures.forEach(feature => {
      config[feature] = true;
    });

    return config;
  });

  // Toggle a specific feature
  const toggleFeature = useCallback((feature: MapFeature) => {
    setEnabledFeatures(prev => ({
      ...prev,
      [feature]: !prev[feature],
    }));
  }, []);

  // Enable a feature
  const enableFeature = useCallback((feature: MapFeature) => {
    setEnabledFeatures(prev => ({
      ...prev,
      [feature]: true,
    }));
  }, []);

  // Disable a feature
  const disableFeature = useCallback((feature: MapFeature) => {
    setEnabledFeatures(prev => ({
      ...prev,
      [feature]: false,
    }));
  }, []);

  // Enable multiple features
  const enableFeatures = useCallback((featuresToEnable: MapFeature[]) => {
    setEnabledFeatures(prev => {
      const updated = { ...prev };
      featuresToEnable.forEach(feature => {
        updated[feature] = true;
      });
      return updated;
    });
  }, []);

  // Disable multiple features
  const disableFeatures = useCallback((featuresToDisable: MapFeature[]) => {
    setEnabledFeatures(prev => {
      const updated = { ...prev };
      featuresToDisable.forEach(feature => {
        updated[feature] = false;
      });
      return updated;
    });
  }, []);

  // Set features configuration
  const setFeatures = useCallback((config: MapFeaturesConfig) => {
    setEnabledFeatures(prev => ({
      ...prev,
      ...config,
    }));
  }, []);

  // Reset to initial features
  const resetFeatures = useCallback(() => {
    const config: MapFeaturesConfig = {
      markers: false,
      routes: false,
      search: false,
      traffic: false,
      location: false,
      ...stableDefaultConfig,
    };

    stableInitialFeatures.forEach(feature => {
      config[feature] = true;
    });

    setEnabledFeatures(config);
  }, [stableInitialFeatures, stableDefaultConfig]);

  // Check if a feature is enabled
  const isFeatureEnabled = useCallback((feature: MapFeature): boolean => {
    return enabledFeatures[feature] || false;
  }, [enabledFeatures]);

  // Get list of enabled features
  const getEnabledFeatures = useMemo((): MapFeature[] => {
    return Object.entries(enabledFeatures)
      .filter(([, enabled]) => enabled)
      .map(([feature]) => feature as MapFeature);
  }, [enabledFeatures]);

  // Check if any features are enabled
  const hasEnabledFeatures = useMemo((): boolean => {
    return Object.values(enabledFeatures).some(enabled => enabled);
  }, [enabledFeatures]);

  // Feature-specific utilities
  const featureUtils = useMemo(() => ({
    // Markers
    shouldShowMarkers: enabledFeatures.markers || false,

    // Routes
    shouldShowRoutes: enabledFeatures.routes || false,

    // Search
    shouldShowSearch: enabledFeatures.search || false,

    // Traffic
    shouldShowTraffic: enabledFeatures.traffic || false,

    // Location
    shouldShowLocation: enabledFeatures.location || false,

    // Combined checks
    shouldShowAnyOverlay: enabledFeatures.search || false,
    shouldShowAnyLayer: enabledFeatures.markers || enabledFeatures.routes || enabledFeatures.traffic || false,
  }), [enabledFeatures]);

  return {
    // State
    enabledFeatures,

    // Actions
    toggleFeature,
    enableFeature,
    disableFeature,
    enableFeatures,
    disableFeatures,
    setFeatures,
    resetFeatures,

    // Queries
    isFeatureEnabled,
    getEnabledFeatures,
    hasEnabledFeatures,

    // Utilities
    ...featureUtils,
  };
}
