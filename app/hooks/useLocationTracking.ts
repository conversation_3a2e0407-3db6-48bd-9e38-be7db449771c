/**
 * useLocationTracking Hook for Task 14: Real-time Location Tracking
 *
 * Provides Geolocation API integration with live user position updates,
 * smooth marker transitions, and accuracy indicators.
 */

import { useCallback, useEffect, useState, useRef } from 'react';
// Removed useMapPerformance - simplified architecture
import { showError, showSuccess, showInfo, showWarning } from '~/components/wanderlust/NotificationSystem';

export interface LocationPosition {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  altitudeAccuracy?: number;
  heading?: number;
  speed?: number;
  timestamp: number;
}

export interface LocationTrackingOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
  updateInterval?: number;
  accuracyThreshold?: number;
}

const DEFAULT_OPTIONS: LocationTrackingOptions = {
  enableHighAccuracy: true,
  timeout: 10000, // 10 seconds
  maximumAge: 30000, // 30 seconds
  updateInterval: 5000, // 5 seconds
  accuracyThreshold: 100, // 100 meters
};

export function useLocationTracking(options: LocationTrackingOptions = {}) {
  const mergedOptions = { ...DEFAULT_OPTIONS, ...options };

  const [isTracking, setIsTracking] = useState(false);
  const [currentPosition, setCurrentPosition] = useState<GeolocationPosition | null>(null);
  const [positionHistory, setPositionHistory] = useState<LocationPosition[]>([]);
  const [accuracy, setAccuracy] = useState<number>(0);
  const [lastUpdate, setLastUpdate] = useState<number>(0);
  const [trackingError, setTrackingError] = useState<string | null>(null);
  const [isHighAccuracy, setIsHighAccuracy] = useState(false);

  // Simplified - no performance monitoring needed
  const measureInteractionLatency = () => () => {}; // No-op function

  // Tracking state
  const watchIdRef = useRef<number | null>(null);
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastPositionRef = useRef<GeolocationPosition | null>(null);

  // Check if geolocation is supported
  const isGeolocationSupported = useCallback((): boolean => {
    return 'geolocation' in navigator;
  }, []);

  // Calculate distance between two positions (Haversine formula)
  const calculateDistance = useCallback((pos1: GeolocationPosition, pos2: GeolocationPosition): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (pos1.coords.latitude * Math.PI) / 180;
    const φ2 = (pos2.coords.latitude * Math.PI) / 180;
    const Δφ = ((pos2.coords.latitude - pos1.coords.latitude) * Math.PI) / 180;
    const Δλ = ((pos2.coords.longitude - pos1.coords.longitude) * Math.PI) / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
  }, []);

  // Process new position update
  const processPositionUpdate = useCallback((position: GeolocationPosition) => {
    const endMeasurement = measureInteractionLatency();

    try {
      // Check if this is a significant position change
      if (lastPositionRef.current) {
        const distance = calculateDistance(lastPositionRef.current, position);

        // Skip update if position hasn't changed significantly and accuracy is poor
        if (distance < 5 && position.coords.accuracy > mergedOptions.accuracyThreshold!) {
          endMeasurement();
          return;
        }
      }

      setCurrentPosition(position);
      setAccuracy(position.coords.accuracy);
      setLastUpdate(Date.now());
      setTrackingError(null);
      lastPositionRef.current = position;

      // Update accuracy status
      const highAccuracy = position.coords.accuracy <= 50;
      if (highAccuracy !== isHighAccuracy) {
        setIsHighAccuracy(highAccuracy);

        if (highAccuracy) {
          showSuccess('High Accuracy', `Location accuracy improved to ${position.coords.accuracy.toFixed(0)}m`);
        } else {
          showWarning('Low Accuracy', `Location accuracy: ${position.coords.accuracy.toFixed(0)}m`);
        }
      }

      // Add to position history
      const locationPosition: LocationPosition = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        altitude: position.coords.altitude || undefined,
        altitudeAccuracy: position.coords.altitudeAccuracy || undefined,
        heading: position.coords.heading || undefined,
        speed: position.coords.speed || undefined,
        timestamp: position.timestamp,
      };

      setPositionHistory(prev => {
        const updated = [locationPosition, ...prev].slice(0, 100); // Keep last 100 positions
        return updated;
      });

    } catch (error) {
      console.error('Error processing position update:', error);
    } finally {
      endMeasurement();
    }
  }, [calculateDistance, measureInteractionLatency, mergedOptions.accuracyThreshold, isHighAccuracy]);

  // Handle geolocation errors
  const handleLocationError = useCallback((error: GeolocationPositionError) => {
    setTrackingError(error.message);

    switch (error.code) {
      case error.PERMISSION_DENIED:
        showError('Location Access Denied', 'Please enable location services to use tracking');
        setIsTracking(false);
        break;
      case error.POSITION_UNAVAILABLE:
        showWarning('Location Unavailable', 'Your location is currently unavailable');
        break;
      case error.TIMEOUT:
        showWarning('Location Timeout', 'Location request timed out. Retrying...');
        break;
      default:
        showError('Location Error', 'An unknown location error occurred');
    }
  }, []);

  // Start location tracking
  const startTracking = useCallback(() => {
    if (!isGeolocationSupported()) {
      showError('Location Not Supported', 'Geolocation is not supported by this browser');
      return;
    }

    if (isTracking) {
      showWarning('Already Tracking', 'Location tracking is already active');
      return;
    }

    const endMeasurement = measureInteractionLatency();

    try {
      showInfo('Location Tracking', 'Starting continuous location tracking...');

      // Start watching position
      watchIdRef.current = navigator.geolocation.watchPosition(
        processPositionUpdate,
        handleLocationError,
        {
          enableHighAccuracy: mergedOptions.enableHighAccuracy,
          timeout: mergedOptions.timeout,
          maximumAge: mergedOptions.maximumAge,
        }
      );

      setIsTracking(true);
      setTrackingError(null);

      // Set up periodic updates for better reliability
      updateIntervalRef.current = setInterval(() => {
        navigator.geolocation.getCurrentPosition(
          processPositionUpdate,
          handleLocationError,
          {
            enableHighAccuracy: mergedOptions.enableHighAccuracy,
            timeout: mergedOptions.timeout,
            maximumAge: mergedOptions.maximumAge,
          }
        );
      }, mergedOptions.updateInterval);

      showSuccess('Tracking Started', 'Location tracking is now active');
    } catch (error) {
      showError('Tracking Failed', 'Unable to start location tracking');
      console.error('Location tracking error:', error);
    } finally {
      endMeasurement();
    }
  }, [isGeolocationSupported, isTracking, processPositionUpdate, handleLocationError, mergedOptions, measureInteractionLatency]);

  // Stop location tracking
  const stopTracking = useCallback(() => {
    const endMeasurement = measureInteractionLatency();

    try {
      if (watchIdRef.current !== null) {
        navigator.geolocation.clearWatch(watchIdRef.current);
        watchIdRef.current = null;
      }

      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
        updateIntervalRef.current = null;
      }

      setIsTracking(false);
      setTrackingError(null);

      showInfo('Tracking Stopped', 'Location tracking has been disabled');
    } catch (error) {
      showError('Stop Tracking Failed', 'Error stopping location tracking');
      console.error('Stop tracking error:', error);
    } finally {
      endMeasurement();
    }
  }, [measureInteractionLatency]);

  // Get current position once (without tracking)
  const getCurrentPosition = useCallback((): Promise<GeolocationPosition> => {
    if (!isGeolocationSupported()) {
      return Promise.reject(new Error('Geolocation not supported'));
    }

    const endMeasurement = measureInteractionLatency();

    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          processPositionUpdate(position);
          endMeasurement();
          resolve(position);
        },
        (error) => {
          handleLocationError(error);
          endMeasurement();
          reject(error);
        },
        {
          enableHighAccuracy: mergedOptions.enableHighAccuracy,
          timeout: mergedOptions.timeout,
          maximumAge: mergedOptions.maximumAge,
        }
      );
    });
  }, [isGeolocationSupported, processPositionUpdate, handleLocationError, mergedOptions, measureInteractionLatency]);

  // Clear position history
  const clearHistory = useCallback(() => {
    setPositionHistory([]);
    showInfo('History Cleared', 'Position history has been cleared');
  }, []);

  // Get tracking statistics
  const getTrackingStats = useCallback(() => {
    if (positionHistory.length === 0) {
      return null;
    }

    const totalDistance = positionHistory.reduce((total, pos, index) => {
      if (index === 0) return 0;

      const prev = positionHistory[index - 1];
      // Simple distance calculation using Haversine formula
      const R = 6371e3; // Earth's radius in meters
      const φ1 = (prev.latitude * Math.PI) / 180;
      const φ2 = (pos.latitude * Math.PI) / 180;
      const Δφ = ((pos.latitude - prev.latitude) * Math.PI) / 180;
      const Δλ = ((pos.longitude - prev.longitude) * Math.PI) / 180;

      const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
                Math.cos(φ1) * Math.cos(φ2) *
                Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c;

      return total + distance;
    }, 0);

    const averageAccuracy = positionHistory.reduce((sum, pos) => sum + pos.accuracy, 0) / positionHistory.length;
    const trackingDuration = positionHistory.length > 0
      ? positionHistory[0].timestamp - positionHistory[positionHistory.length - 1].timestamp
      : 0;

    return {
      totalDistance: Math.round(totalDistance),
      averageAccuracy: Math.round(averageAccuracy),
      trackingDuration: Math.round(trackingDuration / 1000), // in seconds
      positionCount: positionHistory.length,
    };
  }, [positionHistory, calculateDistance]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (watchIdRef.current !== null) {
        navigator.geolocation.clearWatch(watchIdRef.current);
      }
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }
    };
  }, []);

  return {
    // State
    isTracking,
    currentPosition,
    positionHistory,
    accuracy,
    lastUpdate,
    trackingError,
    isHighAccuracy,

    // Methods
    startTracking,
    stopTracking,
    getCurrentPosition,
    clearHistory,

    // Utilities
    isSupported: isGeolocationSupported(),
    getTrackingStats,
    calculateDistance,
  };
}
