import { useState, useCallback } from 'react';
import { useWanderlustStore } from '~/stores/wanderlust';
import { showSuccess, showError, showInfo } from '~/components/wanderlust/NotificationSystem';
import type { TravelRoute } from '~/types/wanderlust';
import type { EnhancedRouteOptions } from '~/types/routes-v2';
import {
  calculateEnhancedRoute,
  getRouteAlternatives
} from '~/lib/route-planning-utils';

/**
 * Custom hook for route calculation functionality
 * Provides consistent route calculation logic across components
 * Enhanced with Routes API v2 features
 */
export function useRouteCalculation() {
  const [isCalculating, setIsCalculating] = useState(false);
  const [isCalculatingAlternatives, setIsCalculatingAlternatives] = useState(false);
  const [travelMode, setTravelMode] = useState<TravelRoute['travelMode']>('DRIVING');
  const [routingPreference, setRoutingPreference] = useState<'TRAFFIC_UNAWARE' | 'TRAFFIC_AWARE' | 'TRAFFIC_AWARE_OPTIMAL'>('TRAFFIC_AWARE');
  const [alternativeRoutes, setAlternativeRoutes] = useState<TravelRoute[]>([]);
  const [selectedAlternativeIndex, setSelectedAlternativeIndex] = useState<number>(0);
  const [routeMetadata, setRouteMetadata] = useState<{
    apiVersion: 'v1' | 'v2';
    calculationTime: number;
    cacheHit: boolean;
  } | null>(null);

  const {
    itinerary,
    currentRoute,
    routePlanning,
    calculateRouteFromItinerary,
    clearRoute,
    clearRouteAndItinerary,
  } = useWanderlustStore();

  // Manual route calculation with proper clearing
  const handleCalculateRoute = useCallback(async () => {
    console.log('🚀 RouteCalculation: Starting route calculation', {
      waypointsCount: itinerary.length,
      hasCurrentRoute: !!currentRoute,
      isPlanning: routePlanning.isPlanning
    });

    if (itinerary.length < 2) {
      showError('Add at least 2 waypoints to calculate a route');
      return;
    }

    setIsCalculating(true);
    try {
      // Clear existing route first to prevent visual overlap
      console.log('🧹 RouteCalculation: Clearing existing route before calculation');
      clearRoute();

      // Small delay to ensure map clears before new route
      console.log('⏳ RouteCalculation: Waiting for cleanup to complete');
      await new Promise(resolve => setTimeout(resolve, 150));

      console.log('📊 RouteCalculation: Starting route calculation from itinerary');
      await calculateRouteFromItinerary();

      console.log('✅ RouteCalculation: Route calculated successfully');
      showSuccess('Route calculated successfully');
    } catch (error) {
      console.error('❌ RouteCalculation: Route calculation failed:', error);
      showError('Failed to calculate route');
    } finally {
      setIsCalculating(false);
      console.log('🏁 RouteCalculation: Calculation process completed');
    }
  }, [itinerary.length, calculateRouteFromItinerary, clearRoute, currentRoute, routePlanning.isPlanning]);

  // Handle travel mode change (manual recalculation required)
  const handleTravelModeChange = useCallback((newMode: TravelRoute['travelMode']) => {
    setTravelMode(newMode);
    // Note: Route will need to be recalculated manually
    if (currentRoute) {
      showSuccess(`Travel mode changed to ${newMode.toLowerCase()}. Recalculate route to update.`);
    }
  }, [currentRoute]);

  // Check if route calculation is ready
  const isCalculationReady = itinerary.length >= 2;

  // Check if currently calculating
  const isCurrentlyCalculating = isCalculating || routePlanning.isPlanning;

  // Clear route only (keep waypoints)
  const handleClearRoute = useCallback(() => {
    console.log('🧹 RouteCalculation: Clearing route only');
    clearRoute();
    showSuccess('Route cleared');
  }, [clearRoute]);

  // Clear route and all waypoints
  const handleClearRouteAndItinerary = useCallback(() => {
    console.log('🧹 RouteCalculation: Clearing route and all waypoints');
    clearRouteAndItinerary();
    showSuccess('Route and waypoints cleared');
  }, [clearRouteAndItinerary]);

  // Enhanced route calculation with Routes V2 features
  const handleCalculateEnhancedRoute = useCallback(async () => {
    console.log('🚀 RouteCalculation: Starting enhanced route calculation', {
      itineraryLength: itinerary.length,
      travelMode,
      routingPreference,
      itinerary: itinerary.map(p => ({ name: p.name, coordinates: p.coordinates }))
    });

    if (itinerary.length < 2) {
      console.warn('❌ RouteCalculation: Not enough waypoints');
      showError('Add at least 2 waypoints to calculate a route');
      return;
    }

    setIsCalculating(true);
    setAlternativeRoutes([]);
    setRouteMetadata(null);

    try {
      // Clear existing route first
      clearRoute();
      await new Promise(resolve => setTimeout(resolve, 150));

      const options: Partial<EnhancedRouteOptions> = {
        routingPreference,
        includeAlternativeRoutes: true,
        maxAlternativeRoutes: 3,
        polylineQuality: 'HIGH_QUALITY',
        computeTollInfo: true,
        optimizeWaypointOrder: true,
        avoidTolls: false,
        avoidHighways: false,
        avoidFerries: false,
      };

      showInfo('Calculating enhanced route with traffic data...');

      const waypoints = itinerary.map(place => ({
        location: {
          lat: place.coordinates.latitude,
          lng: place.coordinates.longitude,
        },
        stopover: true,
        placeId: place.id,
      }));

      console.log('🗺️ RouteCalculation: Calling calculateEnhancedRoute with waypoints:', waypoints);

      const result = await calculateEnhancedRoute(waypoints, travelMode, options);

      console.log('✅ RouteCalculation: calculateEnhancedRoute returned result:', {
        primaryRoute: !!result.primaryRoute,
        alternativeRoutes: result.alternativeRoutes.length,
        metadata: result.metadata
      });

      // Update store with primary route
      const routeForStore = {
        ...result.primaryRoute,
        travelMode,
      };

      // Set the route in the store (this will trigger map updates)
      clearRoute(); // Clear first to ensure clean state
      await new Promise(resolve => setTimeout(resolve, 100));

      // Use the store's method to set the route
      const storeActions = useWanderlustStore.getState();
      storeActions.setCurrentRoute(routeForStore);
      storeActions.setShowDirections(true);

      // Update local state
      setAlternativeRoutes(result.alternativeRoutes);
      setRouteMetadata(result.metadata);
      setSelectedAlternativeIndex(0);

      const alternativesText = result.alternativeRoutes.length > 0
        ? ` (${result.alternativeRoutes.length} alternatives found)`
        : '';

      showSuccess(
        `Enhanced route calculated successfully${alternativesText}`,
        `${result.metadata.apiVersion.toUpperCase()} API • ${result.metadata.calculationTime.toFixed(0)}ms`
      );

      console.log('✅ RouteCalculation: Enhanced route calculated successfully', {
        apiVersion: result.metadata.apiVersion,
        calculationTime: result.metadata.calculationTime,
        alternativesCount: result.alternativeRoutes.length,
        cacheHit: result.metadata.cacheHit,
      });
    } catch (error) {
      console.error('❌ RouteCalculation: Enhanced route calculation failed:', error);
      showError('Failed to calculate enhanced route');
    } finally {
      setIsCalculating(false);
    }
  }, [itinerary, travelMode, routingPreference, clearRoute, setAlternativeRoutes, setRouteMetadata, setSelectedAlternativeIndex]);

  // Calculate route alternatives
  const handleCalculateAlternatives = useCallback(async () => {
    if (itinerary.length < 2) {
      showError('Add at least 2 waypoints to find alternatives');
      return;
    }

    setIsCalculatingAlternatives(true);

    try {
      showInfo('Finding alternative routes...');

      const alternatives = await getRouteAlternatives(
        itinerary.map(place => ({
          location: {
            lat: place.coordinates.latitude,
            lng: place.coordinates.longitude,
          },
          stopover: true,
          placeId: place.id,
        })),
        travelMode,
        3
      );

      setAlternativeRoutes(alternatives);
      setSelectedAlternativeIndex(0);

      showSuccess(
        `Found ${alternatives.length} route alternatives`,
        alternatives.slice(0, 2).map(r => `${r.estimatedDuration} (${r.estimatedDistance})`).join(', ')
      );
    } catch (error) {
      console.error('❌ RouteCalculation: Alternative routes calculation failed:', error);
      showError('Failed to find alternative routes');
    } finally {
      setIsCalculatingAlternatives(false);
    }
  }, [itinerary, travelMode, setIsCalculatingAlternatives, setAlternativeRoutes, setSelectedAlternativeIndex]);

  // Select alternative route
  const handleSelectAlternative = useCallback((index: number) => {
    if (index >= 0 && index < alternativeRoutes.length) {
      const selectedRoute = alternativeRoutes[index];
      setSelectedAlternativeIndex(index);

      // Update store with selected alternative
      const storeActions = useWanderlustStore.getState();
      storeActions.setCurrentRoute(selectedRoute);

      showSuccess(`Switched to alternative route ${index + 1}`);
    }
  }, [alternativeRoutes, setSelectedAlternativeIndex]);

  // Travel mode options
  const travelModes = [
    { mode: 'DRIVING' as const, icon: 'Car', label: 'Driving' },
    { mode: 'WALKING' as const, icon: 'Footprints', label: 'Walking' },
    { mode: 'BICYCLING' as const, icon: 'Bike', label: 'Cycling' },
    { mode: 'TRANSIT' as const, icon: 'Bus', label: 'Transit' }
  ];

  // Routing preference options for Routes V2
  const routingPreferences = [
    { value: 'TRAFFIC_UNAWARE' as const, label: 'Fastest Route', description: 'Ignores current traffic' },
    { value: 'TRAFFIC_AWARE' as const, label: 'Traffic Aware', description: 'Considers current traffic' },
    { value: 'TRAFFIC_AWARE_OPTIMAL' as const, label: 'Optimal Route', description: 'Best route with traffic optimization' }
  ];

  return {
    // State
    isCalculating,
    isCurrentlyCalculating,
    isCalculatingAlternatives,
    travelMode,
    routingPreference,
    alternativeRoutes,
    selectedAlternativeIndex,
    routeMetadata,

    // Current route info
    currentRoute,
    routePlanning,
    isCalculationReady,

    // Options
    travelModes,
    routingPreferences,

    // Actions
    setTravelMode,
    setRoutingPreference,
    handleCalculateRoute,
    handleCalculateEnhancedRoute,
    handleCalculateAlternatives,
    handleSelectAlternative,
    handleTravelModeChange,
    handleClearRoute,
    handleClearRouteAndItinerary,
  };
}
