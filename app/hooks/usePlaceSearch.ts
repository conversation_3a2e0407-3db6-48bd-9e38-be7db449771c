import { useState, useMemo } from 'react';
import type { CityRegion, VisitedPlace } from '~/types/wanderlust';

/**
 * Custom hook for place search and filtering functionality
 * Provides consistent search logic across components
 */
export function usePlaceSearch(regions: CityRegion[], itinerary: VisitedPlace[]) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Get all places from regions
  const allPlaces = useMemo(() => {
    return regions.flatMap(region => region.places || []);
  }, [regions]);

  // Get unique categories
  const categories = useMemo(() => {
    const cats = Array.from(new Set(allPlaces.map(place => place.category)));
    return ['all', ...cats.sort()];
  }, [allPlaces]);

  // Filter places based on search, category, and exclude already added waypoints
  const filteredPlaces = useMemo(() => {
    let filtered = allPlaces;

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(place =>
        place.name.toLowerCase().includes(query) ||
        place.city.toLowerCase().includes(query) ||
        place.description.en.toLowerCase().includes(query)
      );
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(place => place.category === selectedCategory);
    }

    // Exclude places already in itinerary
    filtered = filtered.filter(place =>
      !itinerary.find(wp => wp.id === place.id)
    );

    return filtered;
  }, [allPlaces, searchQuery, selectedCategory, itinerary]);

  // Get category icon
  const getCategoryIcon = (category: string) => {
    const icons: Record<string, string> = {
      all: '🌍',
      food: '🍽️',
      landmark: '🏛️',
      museum: '🏛️',
      park: '🌳',
      accommodation: '🏨',
      transport: '🚇',
      entertainment: '🎭',
      shopping: '🛍️',
    };
    return icons[category] || '📍';
  };

  // Reset search and filters
  const resetFilters = () => {
    setSearchQuery('');
    setSelectedCategory('all');
  };

  return {
    searchQuery,
    setSearchQuery,
    selectedCategory,
    setSelectedCategory,
    allPlaces,
    filteredPlaces,
    categories,
    getCategoryIcon,
    resetFilters,
  };
}
