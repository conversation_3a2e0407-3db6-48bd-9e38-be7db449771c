/**
 * useRoutesV2 Hook - Modern Google Routes API v2 Integration
 *
 * Replaces legacy useGoogleDirections with modern Routes API v2 implementation.
 * Provides enhanced route calculation with traffic-aware routing, alternative routes,
 * toll information, and improved performance.
 */

import { useCallback, useState, useRef, useMemo } from 'react';
import { GoogleRoutesV2Service, createRoutesV2Service } from '~/lib/google-routes-v2-service';
import { RoutesV2Transformer } from '~/lib/routes-v2-transformer';
import { showError, showSuccess, showInfo, showWarning } from '~/components/wanderlust/NotificationSystem';
import type { RouteWaypoint, TravelRoute, VisitedPlace } from '~/types/wanderlust';
import type { EnhancedRouteOptions, EnhancedTravelRoute } from '~/types/routes-v2';

export interface RouteInfo {
  distance: string;
  duration: string;
  distanceValue: number; // in meters
  durationValue: number; // in seconds
  durationInTraffic?: string;
  durationInTrafficValue?: number;
}

export interface RouteStep {
  instruction: string;
  distance: string;
  duration: string;
  maneuver?: string;
  polyline?: string;
}

export interface RouteDetails {
  overview: RouteInfo;
  legs: Array<{
    start_address: string;
    end_address: string;
    distance: string;
    duration: string;
    durationInTraffic?: string;
    steps: RouteStep[];
  }>;
  waypoint_order: number[];
  tollInfo?: {
    estimatedCost?: number;
    currency?: string;
  };
  warnings: string[];
}

export function useRoutesV2() {
  const [currentRoute, setCurrentRoute] = useState<EnhancedTravelRoute | null>(null);
  const [alternativeRoutes, setAlternativeRoutes] = useState<EnhancedTravelRoute[]>([]);
  const [routeDetails, setRouteDetails] = useState<RouteDetails | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [travelMode, setTravelMode] = useState<TravelRoute['travelMode']>('DRIVING');
  const [error, setError] = useState<string | null>(null);

  // Routes v2 service instance
  const serviceRef = useRef<GoogleRoutesV2Service | null>(null);

  // Get or create service instance
  const getService = useCallback((): GoogleRoutesV2Service => {
    if (!serviceRef.current) {
      serviceRef.current = createRoutesV2Service({
        apiKey: import.meta.env.VITE_GOOGLE_MAPS_API_KEY,
        endpoint: 'https://routes.googleapis.com/directions/v2:computeRoutes',
      });
    }
    return serviceRef.current;
  }, []);

  // Route cache to prevent duplicate calculations
  const routeCacheRef = useRef<Map<string, EnhancedTravelRoute>>(new Map());

  // Generate cache key for route
  const generateCacheKey = useCallback((waypoints: RouteWaypoint[], mode: TravelRoute['travelMode'], options: Partial<EnhancedRouteOptions>): string => {
    const points = waypoints.map(w => `${w.location.lat},${w.location.lng}`).join('|');
    const optionsKey = JSON.stringify(options);
    return `${mode}:${points}:${optionsKey}`;
  }, []);

  // Convert VisitedPlace to RouteWaypoint
  const visitedPlaceToWaypoint = useCallback((place: VisitedPlace, stopover: boolean = true): RouteWaypoint => ({
    location: {
      lat: place.coordinates.latitude,
      lng: place.coordinates.longitude,
    },
    stopover,
    placeId: place.id,
  }), []);

  // Update route details from enhanced travel route
  const updateRouteDetails = useCallback((route: EnhancedTravelRoute) => {
    try {
      const details: RouteDetails = {
        overview: {
          distance: route.estimatedDistance || '',
          duration: route.estimatedDuration || '',
          distanceValue: 0, // Will be calculated from legs
          durationValue: 0, // Will be calculated from legs
          durationInTraffic: route.durationInTraffic,
          durationInTrafficValue: route.durationInTrafficValue,
        },
        legs: route.legs?.map(leg => ({
          start_address: leg.start_address || '',
          end_address: leg.end_address || '',
          distance: leg.distance?.text || '',
          duration: leg.duration?.text || '',
          durationInTraffic: leg.duration_in_traffic?.text,
          steps: leg.steps?.map(step => ({
            instruction: step.html_instructions?.replace(/<[^>]*>/g, '') || '',
            distance: step.distance?.text || '',
            duration: step.duration?.text || '',
            maneuver: step.maneuver,
            polyline: step.polyline?.points || '',
          })) || [],
        })) || [],
        waypoint_order: route.overview?.waypointOrder || [],
        tollInfo: route.tollInfo,
        warnings: route.warnings || [],
      };

      setRouteDetails(details);
    } catch (error) {
      console.error('Error updating route details:', error);
    }
  }, []);

  // Format distance for display
  const formatDistance = useCallback((meters: number): string => {
    if (meters < 1000) {
      return `${meters}m`;
    } else {
      return `${(meters / 1000).toFixed(1)}km`;
    }
  }, []);

  // Format duration for display
  const formatDuration = useCallback((seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  }, []);

  // Calculate route using Routes API v2
  const calculateRoute = useCallback(async (
    waypoints: RouteWaypoint[] | VisitedPlace[],
    mode: TravelRoute['travelMode'] = travelMode,
    options: Partial<EnhancedRouteOptions> = {}
  ): Promise<EnhancedTravelRoute | null> => {
    // Convert VisitedPlace[] to RouteWaypoint[] if needed
    const routeWaypoints: RouteWaypoint[] = waypoints.map(wp =>
      'coordinates' in wp ? visitedPlaceToWaypoint(wp) : wp
    );

    if (routeWaypoints.length < 2) {
      showWarning('Route Planning', 'At least 2 locations are required to calculate a route');
      return null;
    }

    // Check cache first
    const cacheKey = generateCacheKey(routeWaypoints, mode, options);
    if (routeCacheRef.current.has(cacheKey)) {
      const cached = routeCacheRef.current.get(cacheKey)!;
      setCurrentRoute(cached);
      updateRouteDetails(cached);
      showInfo('Route Loaded', 'Using cached route calculation');
      return cached;
    }

    setIsCalculating(true);
    setError(null);

    try {
      showInfo('Route Planning', `Calculating ${mode.toLowerCase()} route with Routes API v2...`);

      const service = getService();
      const enhancedOptions: Partial<EnhancedRouteOptions> = {
        polylineQuality: 'HIGH_QUALITY',
        computeTollInfo: true,
        includeAlternativeRoutes: true,
        maxAlternativeRoutes: 3,
        // Don't set default routing preference - let transformer handle it based on travel mode
        ...options,
      };

      const request = RoutesV2Transformer.toComputeRoutesRequest(
        routeWaypoints,
        mode,
        enhancedOptions
      );

      const response = await service.computeRoutes(request);
      const routes = RoutesV2Transformer.fromRoutesV2ResponseMultiple(
        response,
        routeWaypoints,
        mode,
        enhancedOptions
      );

      const [primaryRoute, ...alternatives] = routes;

      // Cache the primary route
      routeCacheRef.current.set(cacheKey, primaryRoute);

      // Limit cache size
      if (routeCacheRef.current.size > 20) {
        const firstKey = routeCacheRef.current.keys().next().value;
        if (firstKey) {
          routeCacheRef.current.delete(firstKey);
        }
      }

      setCurrentRoute(primaryRoute);
      setAlternativeRoutes(alternatives);
      updateRouteDetails(primaryRoute);

      const distance = primaryRoute.estimatedDistance || '';
      const duration = primaryRoute.estimatedDuration || '';
      const trafficInfo = ''; // Traffic info will be available in route details

      showSuccess(
        'Route Calculated',
        `${distance} • ${duration}${trafficInfo}`
      );

      return primaryRoute;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Route calculation failed';
      setError(errorMessage);
      showError('Route Calculation Failed', errorMessage);
      console.error('Routes v2 calculation error:', err);
      return null;
    } finally {
      setIsCalculating(false);
    }
  }, [travelMode, generateCacheKey, visitedPlaceToWaypoint, updateRouteDetails, getService]);

  // Get route alternatives
  const getAlternativeRoutes = useCallback(async (
    waypoints: RouteWaypoint[] | VisitedPlace[],
    mode: TravelRoute['travelMode'] = travelMode,
    maxAlternatives: number = 3
  ): Promise<EnhancedTravelRoute[]> => {
    const options: Partial<EnhancedRouteOptions> = {
      includeAlternativeRoutes: true,
      maxAlternativeRoutes: maxAlternatives,
      // Don't set routing preference - let transformer handle it based on travel mode
    };

    await calculateRoute(waypoints, mode, options);
    return alternativeRoutes;
  }, [travelMode, calculateRoute, alternativeRoutes]);

  // Clear current route
  const clearRoute = useCallback(() => {
    setCurrentRoute(null);
    setAlternativeRoutes([]);
    setRouteDetails(null);
    setError(null);
    showInfo('Route Cleared', 'Route has been removed from the map');
  }, []);

  // Change travel mode
  const changeTravelMode = useCallback((mode: TravelRoute['travelMode']) => {
    setTravelMode(mode);
    showInfo('Travel Mode', `Changed to ${mode.toLowerCase()} mode`);
  }, []);

  // Get turn-by-turn directions as text
  const getTurnByTurnDirections = useCallback((): string[] => {
    if (!routeDetails) return [];

    const directions: string[] = [];
    routeDetails.legs.forEach((leg, legIndex) => {
      directions.push(`Leg ${legIndex + 1}: ${leg.start_address} to ${leg.end_address}`);
      leg.steps.forEach((step, stepIndex) => {
        directions.push(`${stepIndex + 1}. ${step.instruction} (${step.distance})`);
      });
    });

    return directions;
  }, [routeDetails]);

  // Clear cache
  const clearCache = useCallback(() => {
    routeCacheRef.current.clear();
    getService().clearCache();
    showInfo('Cache Cleared', 'Route cache has been cleared');
  }, [getService]);

  // Memoize the return value to prevent object recreation
  return useMemo(() => ({
    // State
    currentRoute,
    alternativeRoutes,
    routeDetails,
    isCalculating,
    travelMode,
    error,

    // Methods
    calculateRoute,
    getAlternativeRoutes,
    clearRoute,
    changeTravelMode,
    getTurnByTurnDirections,
    clearCache,

    // Utilities
    isReady: true, // Routes v2 doesn't need map instance initialization
    formatDistance,
    formatDuration,
    visitedPlaceToWaypoint,

    // Service access for advanced features (stable reference)
    getService,
  }), [
    currentRoute,
    alternativeRoutes,
    routeDetails,
    isCalculating,
    travelMode,
    error,
    calculateRoute,
    getAlternativeRoutes,
    clearRoute,
    changeTravelMode,
    getTurnByTurnDirections,
    clearCache,
    formatDistance,
    formatDuration,
    visitedPlaceToWaypoint,
    getService,
  ]);
}
