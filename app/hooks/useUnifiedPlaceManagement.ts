/**
 * Unified Place Management Hook
 *
 * Provides context-aware place operations that adapt behavior based on the current mode.
 * Unifies waypoint management for route planning and place collection for venue discovery.
 */

import { useCallback, useMemo } from 'react';
import { useWanderlustStore } from '~/stores/wanderlust';
import { useEnhancedMapContext } from '~/components/maps/EnhancedMapProvider';
import { useOfflineSync } from './useOfflineSync';
import { showSuccess, showError, showWarning, showInfo } from '~/components/wanderlust/NotificationSystem';
import type { VisitedPlace } from '~/types/wanderlust';

export type PlaceManagementMode = 'route-planning' | 'venue-discovery';

export interface UnifiedPlaceManagementOptions {
  mode: PlaceManagementMode;
  enableWaypoints?: boolean;
  enableSearch?: boolean;
  maxPlaces?: number;
  enableReordering?: boolean;
  enableDuplicateCheck?: boolean;
}

export interface PlaceAction {
  type: 'add' | 'remove' | 'reorder' | 'select' | 'favorite' | 'clear';
  payload?: any;
}

export interface PlaceManagementState {
  places: VisitedPlace[];
  selectedPlace: VisitedPlace | null;
  canAddMore: boolean;
  isReorderingEnabled: boolean;
  totalCount: number;
  maxPlaces: number;
}

export interface PlaceValidation {
  canAddPlace: (place: VisitedPlace) => boolean;
  validatePlaceLimit: () => boolean;
  checkDuplicates: (place: VisitedPlace) => boolean;
  getValidationMessage: (place: VisitedPlace) => string | null;
}

export function useUnifiedPlaceManagement(options: UnifiedPlaceManagementOptions) {
  const {
    mode,
    enableWaypoints = mode === 'route-planning',
    enableSearch = true,
    maxPlaces = mode === 'route-planning' ? 25 : 100,
    enableReordering = mode === 'route-planning',
    enableDuplicateCheck = true,
  } = options;

  // Get appropriate store/context based on mode
  const wanderlustStore = useWanderlustStore();
  const enhancedMapContext = useEnhancedMapContext();
  const { isOnline, queueAction } = useOfflineSync();

  // Get places based on mode
  const places = useMemo(() => {
    if (mode === 'route-planning') {
      return wanderlustStore.itinerary;
    } else {
      return enhancedMapContext.places || [];
    }
  }, [mode, wanderlustStore.itinerary, enhancedMapContext.places]);

  const selectedPlace = useMemo(() => {
    return enhancedMapContext.selectedPlace;
  }, [enhancedMapContext.selectedPlace]);

  // Validation functions
  const validation: PlaceValidation = useMemo(() => ({
    canAddPlace: (place: VisitedPlace) => {
      // Check place limit
      if (places.length >= maxPlaces) {
        return false;
      }

      // Check duplicates if enabled
      if (enableDuplicateCheck && places.find(p => p.id === place.id)) {
        return false;
      }

      return true;
    },

    validatePlaceLimit: () => {
      return places.length < maxPlaces;
    },

    checkDuplicates: (place: VisitedPlace) => {
      return places.some(p => p.id === place.id);
    },

    getValidationMessage: (place: VisitedPlace) => {
      if (places.length >= maxPlaces) {
        const modeMessages = {
          'route-planning': `Maximum ${maxPlaces} waypoints allowed`,
          'venue-discovery': `Maximum ${maxPlaces} places allowed`,
        };
        return modeMessages[mode];
      }

      if (enableDuplicateCheck && places.find(p => p.id === place.id)) {
        const modeMessages = {
          'route-planning': 'This waypoint is already in your route',
          'venue-discovery': 'This place is already in your collection',
        };
        return modeMessages[mode];
      }

      return null;
    },
  }), [places, maxPlaces, enableDuplicateCheck, mode]);

  // Context-aware place operations with offline support
  const addPlace = useCallback((place: VisitedPlace) => {
    // Validate before adding
    const validationMessage = validation.getValidationMessage(place);
    if (validationMessage) {
      showWarning('Cannot Add Place', validationMessage);
      return false;
    }

    try {
      if (mode === 'route-planning') {
        // Add as waypoint
        wanderlustStore.addToItinerary(place);
        showSuccess('Waypoint Added', `${place.name} added to route`);

        // Queue for offline sync if needed
        if (!isOnline) {
          queueAction('place-add', {
            place,
            mode: 'route-planning',
            timestamp: Date.now()
          });
          showInfo('Offline Action', 'Waypoint addition queued for sync');
        }
      } else {
        // Add to venue collection
        enhancedMapContext.addPlace(place);
        showSuccess('Venue Added', `${place.name} added to collection`);

        // Queue for offline sync if needed
        if (!isOnline) {
          queueAction('place-add', {
            place,
            mode: 'venue-discovery',
            timestamp: Date.now()
          });
          showInfo('Offline Action', 'Place addition queued for sync');
        }
      }
      return true;
    } catch (error) {
      console.error('Failed to add place:', error);

      // Queue for retry if offline
      if (!isOnline) {
        queueAction('place-add', {
          place,
          mode,
          timestamp: Date.now(),
          retryReason: 'add-failed'
        });
        showInfo('Offline Retry', 'Place addition queued for retry when online');
        return true; // Optimistic update
      }

      showError('Add Failed', 'Unable to add place. Please try again.');
      return false;
    }
  }, [mode, wanderlustStore, enhancedMapContext, validation, isOnline, queueAction]);

  const removePlace = useCallback((placeId: string) => {
    try {
      if (mode === 'route-planning') {
        // Remove waypoint
        const place = places.find(p => p.id === placeId);
        if (place) {
          wanderlustStore.removeFromItinerary(place.id);
          showSuccess('Waypoint Removed', `${place.name} removed from route`);
        }
      } else {
        // Remove from venue collection
        const place = places.find(p => p.id === placeId);
        enhancedMapContext.removePlace(placeId);
        if (place) {
          showSuccess('Venue Removed', `${place.name} removed from collection`);
        }
      }
      return true;
    } catch (error) {
      console.error('Failed to remove place:', error);
      showError('Remove Failed', 'Unable to remove place. Please try again.');
      return false;
    }
  }, [mode, places, wanderlustStore, enhancedMapContext]);

  const reorderPlaces = useCallback((fromIndex: number, toIndex: number) => {
    if (!enableReordering || mode !== 'route-planning') {
      showWarning('Reordering Disabled', 'Place reordering is not available in this mode');
      return false;
    }

    try {
      wanderlustStore.reorderItinerary(fromIndex, toIndex);
      showSuccess('Route Reordered', 'Waypoints have been reordered');
      return true;
    } catch (error) {
      console.error('Failed to reorder places:', error);
      showError('Reorder Failed', 'Unable to reorder places. Please try again.');
      return false;
    }
  }, [enableReordering, mode, wanderlustStore]);

  const selectPlace = useCallback((place: VisitedPlace | null) => {
    enhancedMapContext.setSelectedPlace(place);
    if (place) {
      const modeMessages = {
        'route-planning': 'Waypoint selected',
        'venue-discovery': 'Venue selected',
      };
      showSuccess(modeMessages[mode], place.name);
    }
  }, [enhancedMapContext, mode]);

  const clearPlaces = useCallback(() => {
    try {
      if (mode === 'route-planning') {
        wanderlustStore.clearItinerary();
        showSuccess('Route Cleared', 'All waypoints removed from route');
      } else {
        // Clear venue collection (reset to empty)
        enhancedMapContext.setPlaces([]);
        showSuccess('Collection Cleared', 'All venues removed from collection');
      }
      return true;
    } catch (error) {
      console.error('Failed to clear places:', error);
      showError('Clear Failed', 'Unable to clear places. Please try again.');
      return false;
    }
  }, [mode, wanderlustStore, enhancedMapContext]);

  // State object
  const state: PlaceManagementState = useMemo(() => ({
    places,
    selectedPlace,
    canAddMore: places.length < maxPlaces,
    isReorderingEnabled: enableReordering && mode === 'route-planning',
    totalCount: places.length,
    maxPlaces,
  }), [places, selectedPlace, maxPlaces, enableReordering, mode]);

  // Actions object
  const actions = useMemo(() => ({
    addPlace,
    removePlace,
    reorderPlaces,
    selectPlace,
    clearPlaces,
  }), [addPlace, removePlace, reorderPlaces, selectPlace, clearPlaces]);

  return {
    state,
    actions,
    validation,
    mode,
    options: {
      enableWaypoints,
      enableSearch,
      maxPlaces,
      enableReordering,
      enableDuplicateCheck,
    },
  };
}
