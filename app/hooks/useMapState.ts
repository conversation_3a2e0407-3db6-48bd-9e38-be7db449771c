/**
 * Simplified Map State Management
 *
 * Focused map state hook using Zustand for center, zoom, places, and selection.
 * Replaces complex wanderlust store dependencies with clean, focused state.
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { VisitedPlace } from '~/types/wanderlust';

export interface MapCenter {
  lat: number;
  lng: number;
}

export interface MapState {
  // Core map state
  center: MapCenter;
  zoom: number;
  places: VisitedPlace[];
  selectedPlace: VisitedPlace | null;

  // Map interaction state
  isMapReady: boolean;
  isInteracting: boolean;

  // View state
  mapType: 'roadmap' | 'satellite';
  showTraffic: boolean;

  // Actions
  setCenter: (center: MapCenter) => void;
  setZoom: (zoom: number) => void;
  setPlaces: (places: VisitedPlace[]) => void;
  setSelectedPlace: (place: VisitedPlace | null) => void;
  setMapReady: (ready: boolean) => void;
  setInteracting: (interacting: boolean) => void;
  setMapType: (type: 'roadmap' | 'satellite') => void;
  setShowTraffic: (show: boolean) => void;

  // Utility actions
  panTo: (lat: number, lng: number, zoom?: number) => void;
  fitBounds: (places: VisitedPlace[]) => void;
  reset: () => void;
}

// Default map center (New York City - FIFA Club World Cup 2025™ host city)
const DEFAULT_CENTER: MapCenter = {
  lat: 40.7589,
  lng: -73.9851,
};

const DEFAULT_ZOOM = 10;

export const useMapState = create<MapState>()(
  devtools(
    (set, get) => ({
      // Initial state
      center: DEFAULT_CENTER,
      zoom: DEFAULT_ZOOM,
      places: [],
      selectedPlace: null,
      isMapReady: false,
      isInteracting: false,
      mapType: 'roadmap',
      showTraffic: false,

      // Core actions
      setCenter: (center) => set({ center }, false, 'setCenter'),

      setZoom: (zoom) => set({ zoom }, false, 'setZoom'),

      setPlaces: (places) => set({ places }, false, 'setPlaces'),

      setSelectedPlace: (selectedPlace) => set({ selectedPlace }, false, 'setSelectedPlace'),

      setMapReady: (isMapReady) => set({ isMapReady }, false, 'setMapReady'),

      setInteracting: (isInteracting) => set({ isInteracting }, false, 'setInteracting'),

      setMapType: (mapType) => set({ mapType }, false, 'setMapType'),

      setShowTraffic: (showTraffic) => set({ showTraffic }, false, 'setShowTraffic'),

      // Utility actions
      panTo: (lat, lng, zoom) => {
        const newCenter = { lat, lng };
        const updates: Partial<MapState> = { center: newCenter };

        if (zoom !== undefined) {
          updates.zoom = zoom;
        }

        set(updates, false, 'panTo');
      },

      fitBounds: (places) => {
        if (places.length === 0) return;

        if (places.length === 1) {
          // Single place - center on it with appropriate zoom
          const place = places[0];
          set({
            center: {
              lat: place.coordinates.latitude,
              lng: place.coordinates.longitude,
            },
            zoom: 15,
          }, false, 'fitBounds-single');
          return;
        }

        // Multiple places - calculate bounds
        let minLat = places[0].coordinates.latitude;
        let maxLat = places[0].coordinates.latitude;
        let minLng = places[0].coordinates.longitude;
        let maxLng = places[0].coordinates.longitude;

        places.forEach(place => {
          minLat = Math.min(minLat, place.coordinates.latitude);
          maxLat = Math.max(maxLat, place.coordinates.latitude);
          minLng = Math.min(minLng, place.coordinates.longitude);
          maxLng = Math.max(maxLng, place.coordinates.longitude);
        });

        // Calculate center
        const centerLat = (minLat + maxLat) / 2;
        const centerLng = (minLng + maxLng) / 2;

        // Calculate appropriate zoom level based on bounds
        const latDiff = maxLat - minLat;
        const lngDiff = maxLng - minLng;
        const maxDiff = Math.max(latDiff, lngDiff);

        let zoom = 10;
        if (maxDiff < 0.01) zoom = 15;
        else if (maxDiff < 0.05) zoom = 13;
        else if (maxDiff < 0.1) zoom = 11;
        else if (maxDiff < 0.5) zoom = 9;
        else if (maxDiff < 1) zoom = 8;
        else zoom = 7;

        set({
          center: { lat: centerLat, lng: centerLng },
          zoom,
        }, false, 'fitBounds-multiple');
      },

      reset: () => set({
        center: DEFAULT_CENTER,
        zoom: DEFAULT_ZOOM,
        places: [],
        selectedPlace: null,
        isMapReady: false,
        isInteracting: false,
        mapType: 'roadmap',
        showTraffic: false,
      }, false, 'reset'),
    }),
    {
      name: 'map-state',
      // Only enable devtools in development
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);

// Selector hooks for optimized re-renders
export const useMapCenter = () => useMapState(state => state.center);
export const useMapZoom = () => useMapState(state => state.zoom);
export const useMapPlaces = () => useMapState(state => state.places);
export const useSelectedPlace = () => useMapState(state => state.selectedPlace);
export const useMapReady = () => useMapState(state => state.isMapReady);
export const useMapInteracting = () => useMapState(state => state.isInteracting);
export const useMapType = () => useMapState(state => state.mapType);
export const useShowTraffic = () => useMapState(state => state.showTraffic);

// Individual action hooks to prevent object recreation issues
export const useSetMapReady = () => useMapState(state => state.setMapReady);
export const useSetInteracting = () => useMapState(state => state.setInteracting);
export const useSetCenter = () => useMapState(state => state.setCenter);
export const useSetZoom = () => useMapState(state => state.setZoom);
export const useSetPlaces = () => useMapState(state => state.setPlaces);
export const useSetSelectedPlace = () => useMapState(state => state.setSelectedPlace);
export const useSetMapType = () => useMapState(state => state.setMapType);
export const useSetShowTraffic = () => useMapState(state => state.setShowTraffic);
export const usePanTo = () => useMapState(state => state.panTo);
export const useFitBounds = () => useMapState(state => state.fitBounds);
export const useResetMap = () => useMapState(state => state.reset);

// Combined action hooks (use individual hooks above for useEffect dependencies)
export const useMapActions = () => useMapState(state => ({
  setCenter: state.setCenter,
  setZoom: state.setZoom,
  setPlaces: state.setPlaces,
  setSelectedPlace: state.setSelectedPlace,
  setMapReady: state.setMapReady,
  setInteracting: state.setInteracting,
  setMapType: state.setMapType,
  setShowTraffic: state.setShowTraffic,
  panTo: state.panTo,
  fitBounds: state.fitBounds,
  reset: state.reset,
}));
