import type { DayProgram, WeatherInfo } from '../lib/types';
import { supabase } from '../lib/supabase';
import { loadStandardizedItinerary, getAvailableDates } from './standardizedItineraryLoader.browser';
import {
  getItineraryOffline,
  saveItineraryOffline,
  getAvailableDatesOffline,
  processSyncQueue
} from './syncUtils';

// Keep the mock data for fallback and testing
const mockWeatherDay1: WeatherInfo = {
  date: "2025-06-01",
  temperature: 22,
  condition: "Ensoleillé",
  icon: "sunny"
};

const mockWeatherDay2: WeatherInfo = {
  date: "2025-06-02",
  temperature: 24,
  condition: "Partiellement nuageux",
  icon: "partly_cloudy"
};

export const mockDayProgramDay1: DayProgram = {
  date: "2025-06-01",
  title: "Arrivée à New York",
  summary: "Arrivée à New York, enregistrement à l'hôtel, exploration de la ville.",
  weather: mockWeatherDay1,
  reminders: ["Apporter les documents de voyage", "Charger la batterie externe du téléphone"],
  docs: ["hotel-checkin.pdf", "itinerary.pdf"],
  items: [
    {
      id: "breakfast1",
      type: "meal",
      title: "Petit-déjeuner à l'aéroport",
      time: "07:30",
      duration: "1h",
      location: {
        name: "Aéroport Charles de Gaulle Terminal 2",
        lat: 49.0097,
        lng: 2.5479
      },
      notes: "Prendre un petit-déjeuner rapide avant l'embarquement",
      status: "pending"
    },
    {
      id: "flight1",
      type: "transport",
      title: "Vol de Paris à New York",
      time: "09:00",
      duration: "8h",
      location: {
        name: "Aéroport Charles de Gaulle (CDG)",
        lat: 49.0097,
        lng: 2.5479
      },
      transport: {
        mode: "Vol",
        carrier: "Air France",
        bookingReference: "AF123",
        seatMap: {
          "Alice": "12A",
          "Bob": "12B",
          "Charlie": "12C"
        },
        notes: "Faire l'enregistrement en ligne au préalable."
      },
      attachments: ["billet-avion.pdf"],
      status: "completed"
    },
    {
      id: "lunch1",
      type: "meal",
      title: "Déjeuner à JFK",
      time: "15:30",
      duration: "1h",
      location: {
        name: "Terminal 4, Aéroport JFK",
        lat: 40.6413,
        lng: -73.7781
      },
      notes: "Déjeuner après l'immigration",
      status: "pending"
    },
    {
      id: "transfer1",
      type: "transport",
      title: "Transfert de l'aéroport à l'hôtel",
      time: "17:00",
      duration: "1h",
      location: {
        name: "Aéroport international John F. Kennedy (JFK)",
        lat: 40.6413,
        lng: -73.7781
      },
      transport: {
        mode: "Taxi",
        pickup_time: "17:00",
        pickup_location: "Terminal 4, sortie des arrivées",
        estimated_cost: 65
      },
      status: "pending"
    },
    {
      id: "hotel1",
      type: "hotel",
      title: "Enregistrement à l'Hôtel XYZ",
      time: "18:30",
      duration: "1h",
      location: {
        name: "Hôtel XYZ",
        address: "123 Rue Principale, New York, NY",
        lat: 40.7128,
        lng: -74.0060
      },
      notes: "Demande d'enregistrement anticipé.",
      status: "pending"
    },
    {
      id: "dinner1",
      type: "meal",
      title: "Dîner dans un restaurant local",
      time: "20:00",
      duration: "2h",
      location: {
        name: "Restaurant ABC",
        address: "456 Rue Secondaire, New York, NY",
        lat: 40.7145,
        lng: -74.0076
      },
      notes: "Réservation pour 4 personnes.",
      status: "pending"
    }
  ]
};

export const mockDayProgramDay2: DayProgram = {
  date: "2025-06-02",
  title: "Journée de visite et match",
  summary: "Visite de la Statue de la Liberté, déjeuner, et match de football.",
  weather: mockWeatherDay2,
  reminders: ["Apporter le billet de match", "Vérifier l'itinéraire"],
  docs: ["match-ticket.pdf", "map.pdf"],
  items: [
    {
      id: "breakfast2",
      type: "meal",
      title: "Petit-déjeuner à l'hôtel",
      time: "07:00",
      duration: "1h",
      location: {
        name: "Hôtel XYZ",
        lat: 40.7128,
        lng: -74.0060
      },
      notes: "Petit-déjeuner buffet à l'hôtel",
      status: "pending"
    },
    {
      id: "activity1",
      type: "activity",
      title: "Visite de la Statue de la Liberté",
      time: "09:00",
      duration: "3h",
      location: {
        name: "Statue de la Liberté",
        address: "Liberty Island, New York, NY",
        lat: 40.6892,
        lng: -74.0445
      },
      notes: "Visite guidée réservée.",
      status: "pending",
      isGroupEvent: true
    },
    {
      id: "lunch2",
      type: "meal",
      title: "Déjeuner dans un café",
      time: "12:30",
      duration: "1h",
      location: {
        name: "Café DEF",
        address: "789 Rue Tertiaire, New York, NY",
        lat: 40.7161,
        lng: -74.0089
      },
      notes: "Déjeuner léger.",
      status: "pending"
    },
    {
      id: "match1",
      type: "match",
      title: "Match de football au MetLife Stadium",
      time: "19:00",
      duration: "2h",
      location: {
        name: "MetLife Stadium",
        address: "1 MetLife Stadium Dr, East Rutherford, NJ",
        lat: 40.8135,
        lng: -74.0745
      },
      transport: {
        mode: "Uber",
        pickup_time: "17:30",
        pickup_location: "Hôtel XYZ",
        estimated_cost: 40,
        shared_with: ["User1", "User2", "User3"]
      },
      notes: "Arriver tôt pour les contrôles de sécurité. Place Section 134, Rangée 12.",
      attachments: ["match-ticket.pdf"],
      status: "pending",
      important: true,
      requiresConfirmation: true,
      isGroupEvent: true
    }
  ]
};

async function fetchMockDayProgram(date: string): Promise<DayProgram> {
  await new Promise(resolve => setTimeout(resolve, 500));
  return date === "2025-06-01" ? mockDayProgramDay1 : mockDayProgramDay2;
}

/**
 * Fetches an itinerary for a specific date
 *
 * Uses a cascading data source strategy:
 * 1. Try offline storage first (if offline)
 * 2. Try standardized files
 * 3. Try Supabase
 * 4. Fall back to mock data
 *
 * @param date - Date string in YYYY-MM-DD format
 * @returns Promise resolving to the DayProgram for the specified date
 */
export async function fetchItinerary(date: string): Promise<DayProgram> {

  try {
    // Check if we're offline
    if (!navigator.onLine) {
      const offlineData = getItineraryOffline(date);

      if (offlineData) {
        return offlineData;
      }

 return date === "2025-06-01" ? { ...mockDayProgramDay1, _source: 'mock' } : { ...mockDayProgramDay2, _source: 'mock' };
    }

    // Try to sync any pending changes
    try {
      const syncResults = await processSyncQueue();
    } catch (syncError) {
      // Continue with fetching even if sync fails
    }

    // First, try to load from standardized files
    const standardizedData = await loadStandardizedItinerary(date);

    if (standardizedData) {
      // Save to offline storage for future offline access
      saveItineraryOffline(date, standardizedData);
      return standardizedData;
    }

    // If no standardized data, try Supabase
    const { data: days, error: daysError } = await supabase
      .from('itinerary_days')
      .select('id')
      .eq('date', date)
      .limit(1);

    if (daysError) {

      // Check offline storage before falling back to mock data
      const offlineData = getItineraryOffline(date);
      if (offlineData) {
        return offlineData;
      }

 return date === "2025-06-01" ? { ...mockDayProgramDay1, _source: 'mock' } : { ...mockDayProgramDay2, _source: 'mock' };
    }

    if (!days || days.length === 0) {

      // Check offline storage before falling back to mock data
      const offlineData = getItineraryOffline(date);
      if (offlineData) {
        return offlineData;
      }

 return date === "2025-06-01" ? { ...mockDayProgramDay1, _source: 'mock' } : { ...mockDayProgramDay2, _source: 'mock' };
    }

    const dayId = days[0].id;

    try {
      // Try the RPC function first as it's more efficient

      const { data: rpcData, error: rpcError } = await supabase
        .rpc('get_itinerary_day', { day_id: dayId });

      if (!rpcError && rpcData) {
        // Transform the data to DayProgram format
        const transformedData = transformSupabaseData(rpcData, date);

        // Save to offline storage
        saveItineraryOffline(date, transformedData);

        return transformedData;
      }

      // If RPC fails, fall back to direct queries

      // Get the day details
      const { data: dayData, error: dayError } = await supabase
        .from('itinerary_days')
        .select('*')
        .eq('id', dayId)
        .single();

      if (dayError) throw dayError;

      // Get the activities
      const { data: activitiesData, error: activitiesError } = await supabase
        .from('activities')
        .select(`
          *,
          location:locations(*),
          transport:transport_details(*)
        `)
        .eq('itinerary_day_id', dayId)
        .order('sequence_order');

      if (activitiesError) throw activitiesError;

      // Get the weather
      let weatherData = null;
      try {
        const { data, error } = await supabase
          .from('weather')
          .select('temperature, condition, icon')
          .eq('itinerary_day_id', dayId)
          .maybeSingle();

        if (!error && data) {
          weatherData = data;
        }
      } catch (weatherError) {
        // Continue without weather data
      }

      // Get the reminders
      const { data: remindersData, error: remindersError } = await supabase
        .from('reminders')
        .select('text')
        .eq('itinerary_day_id', dayId);

      if (remindersError) throw remindersError;

      // Get the documents
      const { data: docsData, error: docsError } = await supabase
        .from('documents')
        .select('file_name')
        .eq('itinerary_day_id', dayId);

      if (docsError) throw docsError;

      // Combine all the data
      const combinedData = {
        ...dayData,
        weather: weatherData,
        reminders: remindersData?.map(r => r.text) || [],
        docs: docsData?.map(d => d.file_name) || [],
        items: activitiesData || []
      };

      if (!combinedData) {

        // Check offline storage before falling back to mock data
        const offlineData = getItineraryOffline(date);
        if (offlineData) {
          return offlineData;
        }

 return date === "2025-06-01" ? { ...mockDayProgramDay1, _source: 'mock' } : { ...mockDayProgramDay2, _source: 'mock' };
      }

      // Transform the data from Supabase format to DayProgram format
      const transformedData = {
        date: combinedData.date,
        title: combinedData.title,
        summary: combinedData.summary || '',
        weather: combinedData.weather ? {
          date: date,
          temperature: combinedData.weather.temperature,
          condition: combinedData.weather.condition,
          icon: combinedData.weather.icon
        } : undefined,
        reminders: combinedData.reminders || [],
        docs: combinedData.docs || [],
        items: combinedData.activities ? combinedData.activities.map((item: Activity) => ({
          id: item.id,
          type: item.type,
          title: combinedData.title,
          time: item.time,
          duration: item.duration,
          location: item.location,
          transport: item.transport,
          notes: item.notes,
          attachments: item.attachments,
          status: item.status,
          important: item.important,
          requiresConfirmation: item.requires_confirmation || false,
          isGroupEvent: item.is_group_event || false
        })) : [],
        _source: 'supabase' as const // Indicate this data came from Supabase
      };

      // Save to offline storage
      saveItineraryOffline(date, transformedData);

      return transformedData;
    } catch (queryError) {
      console.error('Error with queries:', queryError);

      // Check offline storage before falling back to mock data
      const offlineData = getItineraryOffline(date);
      if (offlineData) {
        return offlineData;
      }

      const mockData = date === "2025-06-01" ? mockDayProgramDay1 : mockDayProgramDay2;
      return { ...mockData, _source: 'mock' };
    }
  } catch (error) {
    console.error('Error fetching itinerary:', error);
 return date === "2025-06-01" ? { ...mockDayProgramDay1, _source: 'mock' } : { ...mockDayProgramDay2, _source: 'mock' };
  }
}

/**
 * Transforms data from Supabase RPC function to DayProgram format
 */
interface Activity {
  id: string;
  type: string;
  title: string;
  time: string;
  duration: string;
  location: any;
  transport: any;
  notes: string;
  attachments: any;
  status: string;
  important: boolean;
  requires_confirmation: boolean;
  is_group_event: boolean;
}
function transformSupabaseData(combinedData: any, date: string): DayProgram {
  return {
    date: combinedData.date,
    title: combinedData.title,
    summary: combinedData.summary || '',
    weather: combinedData.weather ? {
      date: date,
      temperature: combinedData.weather.temperature,
      condition: combinedData.weather.condition,
      icon: combinedData.weather.icon
    } : undefined,
    reminders: combinedData.reminders || [],
    docs: combinedData.docs || [],
    items: combinedData.activities ? combinedData.activities.map((item: Activity) => ({
      id: item.id,
      type: item.type,
      title: combinedData.title,
      time: item.time,
      duration: item.duration,
      location: item.location,
      transport: item.transport,
      notes: item.notes,
      attachments: item.attachments,
      status: item.status,
      important: item.important,
      requiresConfirmation: item.requires_confirmation || false,
      isGroupEvent: item.is_group_event || false
    })) : [],
    _source: 'supabase' as const // Indicate this data came from Supabase
  };
}

/**
 * Fetches available itinerary dates
 *
 * Uses a cascading data source strategy:
 * 1. Try offline storage first (if offline)
 * 2. Try standardized files
 * 3. Try Supabase
 *
 * @returns Promise resolving to an array of date strings in YYYY-MM-DD format
 */
export async function getAvailableItineraryDates(): Promise<string[]> {
  try {
    // Check if we're offline
    if (!navigator.onLine) {
      const offlineDates = getAvailableDatesOffline();
      if (offlineDates && offlineDates.length > 0) {
        return offlineDates;
      }
      return [];
    }

    // First, try to load from standardized files
    const standardizedDates = await getAvailableDates();
    if (standardizedDates && standardizedDates.length > 0) {
      return standardizedDates;
    }

    // If no standardized data, try Supabase
    const { data, error } = await supabase
      .from('itinerary_days')
      .select('date');

    if (error) {
      return [];
    }

    const dates = data.map(item => item.date);
    return dates;
  } catch (error) {
    console.error('Error fetching available itinerary dates:', error);
    return [];
  }
}

/**
 * Creates a new itinerary day
 *
 * @param tripId - The ID of the trip to associate the itinerary with
 * @param data - Partial DayProgram data to create the itinerary
 * @returns Promise resolving to the newly created DayProgram
 */
export async function createItinerary(tripId: string, data: Partial<DayProgram>): Promise<DayProgram> {
  try {
    // Validate required fields
    if (!data.date) {
      throw new Error('Date is required to create an itinerary');
    }

    if (!data.title) {
      throw new Error('Title is required to create an itinerary');
    }

    // Insert the itinerary day
    const { data: itineraryData, error: itineraryError } = await supabase
      .from('itinerary_days')
      .insert([
        {
          trip_id: tripId,
          date: data.date,
          title: data.title,
          summary: data.summary || ''
        }
      ])
      .select()
      .single();

    if (itineraryError) {
      throw itineraryError;
    }

    // Insert reminders
    if (data.reminders && data.reminders.length > 0) {
      const remindersToInsert = data.reminders.map(text => ({
        itinerary_day_id: itineraryData.id,
        text: text
      }));

      const { error: remindersError } = await supabase
        .from('reminders')
        .insert(remindersToInsert);

      if (remindersError) {
        throw remindersError;
      }
    }

    // Fetch the created itinerary
    return await fetchItinerary(data.date as string);
  } catch (error) {
    console.error('Error creating itinerary:', error);
    throw error;
  }
}

/**
 * Updates an existing itinerary day
 *
 * @param date - The date of the itinerary to update
 * @returns Promise resolving to an object indicating success or failure
 */
export async function updateItinerary(date: string, data: Partial<DayProgram>): Promise<DayProgram> {
  try {
    // Get the itinerary day ID
    const { data: days, error: daysError } = await supabase
      .from('itinerary_days')
      .select('id')
      .eq('date', date)
      .limit(1);

    if (daysError) {
      throw daysError;
    }

    if (!days || days.length === 0) {
      throw new Error(`No itinerary found for date: ${date}`);
    }

    const dayId = days[0].id;

    // Update the itinerary day
    const { data: itineraryData, error: itineraryError } = await supabase
      .from('itinerary_days')
      .update({
        title: data.title,
        summary: data.summary
      })
      .eq('id', dayId)
      .select()
      .single();

    if (itineraryError) {
      throw itineraryError;
    }

    // Update reminders
    if (data.reminders) {
      // Delete existing reminders
      const { error: deleteRemindersError } = await supabase
        .from('reminders')
        .delete()
        .eq('itinerary_day_id', dayId);

      if (deleteRemindersError) {
        throw deleteRemindersError;
      }

      // Insert new reminders
      const remindersToInsert = data.reminders.map(text => ({
        itinerary_day_id: dayId,
        text: text
      }));

      const { error: remindersError } = await supabase
        .from('reminders')
        .insert(remindersToInsert);

      if (remindersError) {
        throw remindersError;
      }
    }

    // Fetch the updated itinerary
    return await fetchItinerary(date);
  } catch (error) {
    console.error('Error updating itinerary:', error);
    throw error;
  }
}

/**
 * Deletes an itinerary day
 *
 * @param date - The date of the itinerary to delete
 * @returns Promise resolving to an object indicating success or failure
 */
export async function deleteItinerary(date: string): Promise<{ success: boolean, message: string }> {
  try {
    // Get the itinerary day ID
    const { data: days, error: daysError } = await supabase
      .from('itinerary_days')
      .select('id')
      .eq('date', date)
      .limit(1);

    if (daysError) {
      throw daysError;
    }

    if (!days || days.length === 0) {
      return { success: false, message: `No itinerary found for date: ${date}` };
    }

    const dayId = days[0].id;

    // Delete the itinerary day
    const { error: deleteError } = await supabase
      .from('itinerary_days')
      .delete()
      .eq('id', dayId);

    if (deleteError) {
      throw deleteError;
    }

    return { success: true, message: `Itinerary for date ${date} deleted successfully` };
  } catch (error) {
    console.error('Error deleting itinerary:', error);
    return { success: false, message: 'Failed to delete itinerary' };
  }
}