# Routes API v2 Production Runbook

## Overview

This runbook provides operational procedures for managing the Google Routes API v2 implementation in production environments. It covers deployment, monitoring, troubleshooting, and emergency procedures.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Deployment Procedures](#deployment-procedures)
3. [Monitoring and Alerting](#monitoring-and-alerting)
4. [Troubleshooting Guide](#troubleshooting-guide)
5. [Emergency Procedures](#emergency-procedures)
6. [Maintenance Tasks](#maintenance-tasks)
7. [Contact Information](#contact-information)

## System Architecture

### Components Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Client   │───▶│  Load Balancer  │───▶│  App Servers    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Feature Flags  │◀───│  Routes V2 API  │◀───│  Route Service  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Monitoring    │◀───│     Cache       │◀───│  Legacy API     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Services

- **Routes V2 Service**: Primary route calculation service
- **Feature Flag Manager**: Controls feature rollouts
- **Monitoring System**: Tracks metrics and health
- **Deployment Manager**: Handles deployments and rollbacks
- **Cache Layer**: Improves performance and reduces API calls

## Deployment Procedures

### Pre-Deployment Checklist

- [ ] All tests pass (unit, integration, e2e)
- [ ] Performance benchmarks met
- [ ] Security review completed
- [ ] Staging environment validated
- [ ] Rollback plan prepared
- [ ] Team notifications sent

### Deployment Strategies

#### 1. Feature Flag Deployment (Recommended)

```bash
# Start gradual rollout
npm run deploy:feature-flag --percentage=1

# Monitor for 5 minutes, then increase
npm run deploy:feature-flag --percentage=5

# Continue gradual increase
npm run deploy:feature-flag --percentage=25
npm run deploy:feature-flag --percentage=50
npm run deploy:feature-flag --percentage=100
```

#### 2. Canary Deployment

```bash
# Deploy to canary environment
npm run deploy:canary --traffic=5

# Monitor metrics and health
npm run monitor:canary

# Increase traffic gradually
npm run deploy:canary --traffic=25
npm run deploy:canary --traffic=50
npm run deploy:canary --traffic=100
```

#### 3. Blue-Green Deployment

```bash
# Deploy to green environment
npm run deploy:blue-green --environment=green

# Run health checks
npm run health-check:green

# Switch traffic
npm run switch-traffic:green
```

### Post-Deployment Verification

1. **Health Checks**: Verify all services are healthy
2. **Metrics Validation**: Check key performance indicators
3. **Feature Verification**: Test critical user flows
4. **Error Monitoring**: Watch for increased error rates

## Monitoring and Alerting

### Key Metrics to Monitor

#### Performance Metrics

- **Response Time**: < 2 seconds for route calculations
- **Error Rate**: < 1% of total requests
- **Cache Hit Rate**: > 80%
- **API Quota Usage**: < 80% of daily limit

#### System Health Metrics

- **Memory Usage**: < 80% of available memory
- **CPU Usage**: < 70% average
- **Disk Usage**: < 85% of available space
- **Network Latency**: < 100ms to Google APIs

### Alert Thresholds

| Metric | Warning | Critical | Action |
|--------|---------|----------|--------|
| Error Rate | > 2% | > 5% | Investigate/Rollback |
| Response Time | > 3s | > 5s | Scale/Optimize |
| Memory Usage | > 80% | > 90% | Scale/Restart |
| Cache Hit Rate | < 70% | < 50% | Check Cache Health |

### Monitoring Dashboard

Access the production dashboard at: `/admin/routes-v2-dashboard`

Key sections:

- **System Overview**: Overall health and status
- **Performance Metrics**: Real-time performance data
- **Feature Flags**: Current flag status and controls
- **Deployment Status**: Active deployments and history

## Troubleshooting Guide

### Common Issues

#### 1. High Error Rate

**Symptoms**: Increased 4xx/5xx responses from Routes API

**Diagnosis**:

```bash
# Check error logs
kubectl logs -f deployment/routes-service --tail=100

# Check API quota
curl -H "X-Goog-Api-Key: $API_KEY" \
  "https://routes.googleapis.com/directions/v2:computeRoutes"

# Check feature flag status
npm run feature-flags:status
```

**Resolution**:

1. Check API key validity and quotas
2. Verify request format and parameters
3. Check for rate limiting
4. Consider fallback to legacy API

#### 2. Slow Response Times

**Symptoms**: Route calculations taking > 5 seconds

**Diagnosis**:

```bash
# Check performance metrics
npm run metrics:performance

# Check cache status
npm run cache:status

# Check API latency
npm run api:latency-test
```

**Resolution**:

1. Check cache hit rate and optimize
2. Verify API endpoint performance
3. Consider request optimization
4. Scale service if needed

#### 3. Cache Issues

**Symptoms**: Low cache hit rate, increased API calls

**Diagnosis**:

```bash
# Check cache health
npm run cache:health

# Check cache statistics
npm run cache:stats

# Check memory usage
npm run system:memory
```

**Resolution**:

1. Restart cache service
2. Increase cache size if needed
3. Check cache eviction policies
4. Verify cache key generation

#### 4. Feature Flag Issues

**Symptoms**: Features not rolling out correctly

**Diagnosis**:

```bash
# Check feature flag status
npm run feature-flags:list

# Check user targeting
npm run feature-flags:debug --user-id=USER_ID

# Check flag evaluation logs
npm run logs:feature-flags
```

**Resolution**:

1. Verify flag configuration
2. Check user targeting rules
3. Clear evaluation cache
4. Restart feature flag service

### Debugging Commands

```bash
# System health check
npm run health-check:all

# Performance diagnostics
npm run diagnostics:performance

# Cache diagnostics
npm run diagnostics:cache

# API connectivity test
npm run test:api-connectivity

# Feature flag diagnostics
npm run diagnostics:feature-flags

# Full system diagnostics
npm run diagnostics:full
```

## Emergency Procedures

### Immediate Rollback

If critical issues are detected:

```bash
# Emergency rollback via feature flag
npm run emergency:rollback

# Or via deployment manager
npm run deployment:rollback --reason="Critical issue detected"
```

### Circuit Breaker Activation

If Routes V2 API is completely unavailable:

```bash
# Disable Routes V2 completely
npm run feature-flags:disable routes_v2_enabled

# Force fallback to legacy API
npm run fallback:enable --force
```

### Incident Response

1. **Assess Impact**: Determine scope and severity
2. **Communicate**: Notify stakeholders and team
3. **Mitigate**: Implement immediate fixes or rollback
4. **Monitor**: Watch for resolution confirmation
5. **Document**: Record incident details and lessons learned

### Emergency Contacts

- **On-Call Engineer**: ******-0123
- **Team Lead**: ******-0124
- **DevOps Team**: ******-0125
- **Incident Commander**: ******-0126

## Maintenance Tasks

### Daily Tasks

- [ ] Review system health dashboard
- [ ] Check error rates and performance metrics
- [ ] Verify API quota usage
- [ ] Review deployment status

### Weekly Tasks

- [ ] Analyze performance trends
- [ ] Review feature flag usage
- [ ] Check cache performance
- [ ] Update monitoring thresholds if needed

### Monthly Tasks

- [ ] Review and update runbook
- [ ] Analyze incident patterns
- [ ] Performance optimization review
- [ ] Security review and updates

### Quarterly Tasks

- [ ] Disaster recovery testing
- [ ] Capacity planning review
- [ ] Technology stack updates
- [ ] Team training and knowledge sharing

## Configuration Management

### Environment Variables

#### Production Environment

```bash
# Core configuration
VITE_USE_ROUTES_V2=true
VITE_ROUTES_V2_PERCENTAGE=100
VITE_ENABLE_ROUTES_FALLBACK=true
VITE_ROUTES_DEBUG=false

# API configuration
VITE_GOOGLE_MAPS_API_KEY=your-production-api-key
VITE_ROUTES_V2_ENDPOINT=https://routes.googleapis.com/directions/v2:computeRoutes
VITE_API_TIMEOUT=30000

# Monitoring
VITE_ENABLE_METRICS=true
VITE_ENABLE_ERROR_TRACKING=true
VITE_SENTRY_DSN=your-sentry-dsn

# Performance
VITE_CACHE_ENABLED=true
VITE_CACHE_TTL_MINUTES=30
VITE_RATE_LIMIT_PER_MINUTE=300
```

#### Staging Environment

```bash
# Core configuration
VITE_USE_ROUTES_V2=true
VITE_ROUTES_V2_PERCENTAGE=100
VITE_ENABLE_ROUTES_FALLBACK=true
VITE_ROUTES_DEBUG=true

# Use staging API key
VITE_GOOGLE_MAPS_API_KEY=your-staging-api-key

# Enhanced monitoring for testing
VITE_ENABLE_METRICS=true
VITE_ENABLE_ERROR_TRACKING=true
VITE_PERFORMANCE_TRACKING=true
```

### Feature Flag Configuration

Default production flags:

- `routes_v2_enabled`: 100% (all users)
- `enhanced_routing`: 100% (all users)
- `alternative_routes`: 80% (gradual rollout)
- `real_time_traffic`: 50% (limited regions)
- `ai_route_suggestions`: 10% (beta users only)

## Performance Baselines

### Response Time Targets

- Simple route (2 waypoints): < 1 second
- Complex route (5+ waypoints): < 3 seconds
- Alternative routes: < 5 seconds
- Cached routes: < 100ms

### Throughput Targets

- Peak requests per minute: 1,000
- Sustained requests per hour: 30,000
- Concurrent users: 500

### Resource Usage Targets

- Memory usage: < 2GB per instance
- CPU usage: < 50% average
- Cache hit rate: > 85%
- API quota usage: < 80% daily limit

## Security Considerations

### API Key Management

- Rotate API keys quarterly
- Use separate keys for different environments
- Monitor API key usage and quotas
- Implement key rotation procedures

### Data Privacy

- No user data stored in logs
- Cache data encrypted at rest
- Secure transmission (HTTPS only)
- GDPR compliance maintained

### Access Control

- Dashboard access restricted to authorized personnel
- Feature flag changes require approval
- Deployment permissions limited to DevOps team
- Audit logs maintained for all changes

## Contact Information

### Team Contacts

**Development Team**

- Team Lead: John Doe (<<EMAIL>>)
- Senior Developer: Jane Smith (<<EMAIL>>)
- DevOps Engineer: Bob Johnson (<<EMAIL>>)

**Operations Team**

- Operations Manager: Alice Brown (<<EMAIL>>)
- Site Reliability Engineer: Charlie Wilson (<<EMAIL>>)

**Management**

- Product Manager: Diana Davis (<<EMAIL>>)
- Engineering Manager: Frank Miller (<<EMAIL>>)

### External Contacts

**Google Cloud Support**

- Support Case Portal: <https://cloud.google.com/support>
- Phone: **************
- Priority: P1 for production issues

**Monitoring Services**

- Sentry Support: <<EMAIL>>
- DataDog Support: <<EMAIL>>

---

**Document Version**: 1.0  
**Last Updated**: 2024-01-15  
**Next Review**: 2024-04-15  
**Owner**: DevOps Team
